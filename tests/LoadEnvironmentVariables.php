<?php

declare(strict_types=1);

namespace Tests;

use Dotenv\Dotenv;
use Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables as BaseLoadEnvironmentVariables;

class LoadEnvironmentVariables extends BaseLoadEnvironmentVariables
{
    protected function createDotenv($app)
    {
        return Dotenv::createMutable(
            $app->environmentPath(),
            $app->environmentFile(),
        );
    }
}
