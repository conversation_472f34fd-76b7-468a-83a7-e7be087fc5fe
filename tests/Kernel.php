<?php

declare(strict_types=1);

namespace Tests;

use App\Console\Kernel as BaseKernel;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;

class Kernel extends BaseKernel
{
    /**
     * @var string
     */
    private const ENV_NAME = '.env.testing';

    public function __construct(Application $app, Dispatcher $events, string $envName = self::ENV_NAME)
    {
        $app->loadEnvironmentFrom($envName);

        parent::__construct($app, $events);

        $this->bootstrappers[0] = LoadEnvironmentVariables::class;
    }
}
