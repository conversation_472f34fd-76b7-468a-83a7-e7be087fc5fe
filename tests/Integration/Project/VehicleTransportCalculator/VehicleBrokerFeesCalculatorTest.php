<?php

declare(strict_types=1);

namespace Tests\Integration\Project\VehicleTransportCalculator;

use App\Models\Option;
use App\Project\Option\Enums\UserOptions;
use App\Project\Vehicle\Enums\VehicleSellerTypes;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;
use App\Project\VehicleTransportCalculator\VehicleBrokerFeesCalculator;
use Tests\Integration\IntegrationTestCase;

class VehicleBrokerFeesCalculatorTest extends IntegrationTestCase
{
    public function testBrokerFeesWithOtherSeller(): void
    {
        $brokerAddition = 99 * 100;
        $user = $this->userInstance();
        $user->options()->save(
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_OTHER,
                'value' => $brokerAddition,
            ])
        );
        $vehicle = $this->vehicleInstance(
            user: $user,
            data: [
                'seller_type' => VehicleSellerTypes::OTHER,
                'user_pay_for_vehicle' => true,
                'type_of_service' => VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT,
            ]
        );
        $brokerFeesCalculator = $this->vehicleBrokerFeesCalculatorInstance();

        $fees = $brokerFeesCalculator($vehicle);

        $this->assertSame($brokerAddition, $fees);
    }

    public function testBrokerFeesWithIaaSeller(): void
    {
        $brokerAddition = 23 * 100;
        $user = $this->userInstance();
        $user->options()->save(
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_IAA,
                'value' => $brokerAddition,
            ])
        );
        $vehicle = $this->vehicleInstance(
            user: $user,
            data: [
                'seller_type' => VehicleSellerTypes::IAA,
                'user_pay_for_vehicle' => true,
                'type_of_service' => VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT,
            ]
        );
        $brokerFeesCalculator = $this->vehicleBrokerFeesCalculatorInstance();

        $fees = $brokerFeesCalculator($vehicle);

        $this->assertSame($brokerAddition, $fees);
    }

    public function testBrokerFeesWithWithCopartSellerAndCwPays(): void
    {
        $brokerAddition = 33 * 100;
        $cwPaysAddition = 20 * 100;
        $user = $this->userInstance();
        $user->options()->saveMany([
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_COPART,
                'value' => $brokerAddition,
            ]),
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_CW_PAYS,
                'value' => $cwPaysAddition,
            ]),
        ]);
        $vehicle = $this->vehicleInstance(
            user: $user,
            data: [
                'seller_type' => VehicleSellerTypes::COPART,
                'user_pay_for_vehicle' => false,
                'type_of_service' => VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT,
            ]
        );
        $brokerFeesCalculator = $this->vehicleBrokerFeesCalculatorInstance();

        $fees = $brokerFeesCalculator($vehicle);

        $this->assertSame($brokerAddition + $cwPaysAddition, $fees);
    }

    public function testBrokerFeesWithWithOtherTypeOfService(): void
    {
        $brokerAddition = 33 * 100;
        $cwPaysAddition = 20 * 100;
        $user = $this->userInstance();
        $user->options()->saveMany([
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_COPART,
                'value' => $brokerAddition,
            ]),
            Option::factory()->make([
                'key' => UserOptions::BROKER_FEE_CW_PAYS,
                'value' => $cwPaysAddition,
            ]),
        ]);
        $vehicle = $this->vehicleInstance(
            user: $user,
            data: [
                'seller_type' => VehicleSellerTypes::COPART,
                'user_pay_for_vehicle' => false,
                'type_of_service' => VehicleTypesOfServices::BUY_UNDER_LICENSE,
            ]
        );
        $brokerFeesCalculator = $this->vehicleBrokerFeesCalculatorInstance();

        $fees = $brokerFeesCalculator($vehicle);

        $this->assertSame(0, $fees);
    }

    private function vehicleBrokerFeesCalculatorInstance(): VehicleBrokerFeesCalculator
    {
        return $this->app->make(VehicleBrokerFeesCalculator::class);
    }
}
