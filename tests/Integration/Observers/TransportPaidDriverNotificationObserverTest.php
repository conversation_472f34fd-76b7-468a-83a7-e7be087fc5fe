<?php

declare(strict_types=1);

namespace Tests\Integration\Observers;

use App\Models\Driver;
use App\Models\DriverVehicle;
use App\Notifications\TransportPaid;
use Illuminate\Support\Facades\Notification;
use Tests\Integration\IntegrationTestCase;

class TransportPaidDriverNotificationObserverTest extends IntegrationTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        Notification::fake();
    }

    public function testSendEmailToDriverOnPaidAtUpdated(): void
    {
        $vehicle = $this->vehicleInstance();
        $driver = Driver::factory()->create();
        $driverVehicle = DriverVehicle::factory()->createQuietly([
            'driver_id' => $driver->getKey(),
            'vehicle_id' => $vehicle->getKey(),
            'transport_price' => 1000,
        ]);

        $driverVehicle->paid_at = now();
        $driverVehicle->save();

        Notification::assertSentTo(
            $driver,
            TransportPaid::class,
            static fn (TransportPaid $notification, array $channels): bool => count($channels) === 1 &&
                in_array('mail', $channels, true)
        );
    }

    public function testDoesNotSendEmailToDriverWhenPaidAtIsUnchanged(): void
    {
        $vehicle = $this->vehicleInstance();
        $driver = Driver::factory()->create();
        $driverVehicle = DriverVehicle::factory()->createQuietly([
            'driver_id' => $driver->getKey(),
            'vehicle_id' => $vehicle->getKey(),
            'transport_price' => 1000,
        ]);

        $driverVehicle->transport_price = 2000;
        $driverVehicle->save();

        Notification::assertNothingSent();
    }

    public function testDoesNotSendEmailToDriverWhenPaidAtIsChangedToNull(): void
    {
        $vehicle = $this->vehicleInstance();
        $driver = Driver::factory()->create();
        $driverVehicle = DriverVehicle::factory()->createQuietly([
            'driver_id' => $driver->getKey(),
            'vehicle_id' => $vehicle->getKey(),
            'transport_price' => 1000,
        ]);

        $driverVehicle->paid_at = null;
        $driverVehicle->save();

        Notification::assertNothingSent();
    }
}
