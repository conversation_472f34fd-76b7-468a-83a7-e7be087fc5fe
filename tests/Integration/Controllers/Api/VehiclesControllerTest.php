<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Api;

use App\Http\Middleware\VerifyApiAccess;
use App\Models\DealerClient;
use App\Models\OrderInvoice;
use App\Models\UserBusinessCard;
use Illuminate\Support\Str;
use Tests\Integration\IntegrationTestCase;

class VehiclesControllerTest extends IntegrationTestCase
{
    public function testIndex(): void
    {
        $firstUser = $this->userInstance();
        $secondUser = $this->userInstance();
        $firstDealerClient = DealerClient::factory()->create([
            'user_id' => $firstUser->getKey(),
            'email' => $email = '<EMAIL>',
        ]);
        $secondDealerClient = DealerClient::factory()->create([
            'user_id' => $secondUser->getKey(),
            'email' => $email,
        ]);
        $this->vehicleInstance(user: $firstUser, data: [
            'share_link_disabled' => false,
            'dealer_client_id' => $firstDealerClient->getKey(),
        ]);
        $this->userVehicleInstance(user: $firstUser, data: [
            'share_link_disabled' => false,
            'dealer_client_id' => $firstDealerClient->getKey(),
        ]);
        $this->vehicleInstance(user: $firstUser, data: [
            'share_link_disabled' => false,
            'dealer_client_id' => $secondDealerClient->getKey(),
        ]);
        $this->userVehicleInstance(user: $firstUser, data: [
            'share_link_disabled' => false,
            'dealer_client_id' => $secondDealerClient->getKey(),
        ]);

        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->getJson(route('api.vehicles.index', ['email' => $email]));

        $response->assertSuccessful();
        $this->assertCount(2, $response->json('data'));
        $response->assertJsonStructure(
            [
                'data' => [
                    '*' => [
                        'id',
                        'share_uuid',
                        'model_alias',
                        'vehicle_description',
                        'vin_number',
                        'shipping_line',
                        'eta',
                        'keys',
                        'terminal',
                        'container_number',
                        'status',
                        'updated_at',
                        'images',
                        'has_collection_images',
                        'delivery_location',
                    ],
                ],
                'links' => [
                    'first',
                    'last',
                    'prev',
                    'next',
                ],
                'meta' => [
                    'current_page',
                    'from',
                    'last_page',
                    'path',
                    'per_page',
                    'to',
                    'total',
                ],
            ],
        );
    }

    public function testShowForVehicle(): void
    {
        $shareUuid = Str::uuid()->toString();
        $user = $this->userInstance();
        UserBusinessCard::factory()
            ->create(['user_id' => $user->getKey()]);
        $vehicle = $this->vehicleInstance(user: $user, data: ['share_uuid' => $shareUuid, 'share_link_disabled' => false]);
        OrderInvoice::factory()->create(['order_id' => $vehicle->order->getKey()]);

        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->getJson(route('api.vehicles.show', $shareUuid));

        $response->assertSuccessful();
        $response->assertJsonStructure(
            [
                'data' => [
                    'id',
                    'share_uuid',
                    'model_alias',
                    'vehicle_description',
                    'vin_number',
                    'shipping_line',
                    'eta',
                    'keys',
                    'terminal',
                    'container_number',
                    'status',
                    'updated_at',
                    'images',
                    'has_collection_images',
                    'delivery_location',
                    'order_invoice' => [
                        'invoice_number',
                        'invoice_number_created_at',
                    ],
                    'user_business_card' => [
                        'id',
                        'desktop_image',
                        'mobile_image',
                    ],
                ],
            ]
        );
    }

    public function testShowForUserVehicle(): void
    {
        $shareUuid = Str::uuid()->toString();
        $user = $this->userInstance();
        UserBusinessCard::factory()
            ->create(['user_id' => $user->getKey()]);
        $this->userVehicleInstance(user: $user, data: ['share_uuid' => $shareUuid, 'share_link_disabled' => false]);
        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->getJson(route('api.vehicles.show', $shareUuid));

        $response->assertSuccessful();
        $response->assertJsonStructure(
            [
                'data' => [
                    'id',
                    'share_uuid',
                    'model_alias',
                    'vehicle_description',
                    'vin_number',
                    'shipping_line',
                    'eta',
                    'keys',
                    'terminal',
                    'container_number',
                    'status',
                    'updated_at',
                    'images',
                    'has_collection_images',
                    'delivery_location',
                    'user_business_card' => [
                        'id',
                        'desktop_image',
                        'mobile_image',
                    ],
                ],
            ]
        );
    }

    public function testShowForShareLinkDisabled(): void
    {
        $shareUuid = Str::uuid()->toString();
        $this->vehicleInstance(data: ['share_uuid' => $shareUuid, 'share_link_disabled' => true]);

        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->get(route('api.vehicles.show', $shareUuid));

        $response->assertNotFound();
    }

    public function testShowForWrongApiKey(): void
    {
        $shareUuid = Str::uuid()->toString();
        $this->vehicleInstance(data: ['share_uuid' => $shareUuid]);

        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, 'wrong_api_key')
            ->get(route('api.vehicles.show', $shareUuid));

        $response->assertForbidden();
    }
}
