<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Api\Drivers;

use App\Http\Middleware\VerifyApiAccess;
use App\Models\Driver;
use Tests\Integration\IntegrationTestCase;
use Tests\TestsMailer;

class SendPaymentStatusEmailControllerTest extends IntegrationTestCase
{
    use TestsMailer;

    public function testSendsPaymentStatusEmail(): void
    {
        $driver = Driver::factory()->create();
        $this->mockDriverMailerSend(
            view: 'emails.driver.payment-status-link',
            to: $driver->email,
            data: [
                'title' => 'You have received a link to track the status of your payments',
                'uri' => $uri = 'https://example.com',
            ],
        );

        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->postJson(
                route('api.drivers.send-payment-status-email'),
                [
                    'email' => $driver->email,
                    'uri' => $uri,
                ]
            );

        $response->assertNoContent();
    }

    public function testSendsPaymentStatusEmailForNotExistingDealerClientEmail(): void
    {
        $response = $this->withHeader(VerifyApiAccess::API_KEY_HEADER, config('api.access_key'))
            ->postJson(
                route('api.drivers.send-payment-status-email'),
                [
                    'email' => '<EMAIL>',
                    'uri' => 'https://example.com',
                ]
            );

        $response->assertNotFound();
    }
}
