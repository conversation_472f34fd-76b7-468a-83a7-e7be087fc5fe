<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Admin;

use App\Models\Option;
use Tests\Integration\IntegrationTestCase;

class SettingsControllerTest extends IntegrationTestCase
{
    /**
     * @var array<string, string>
     */
    private const PRICE_FIELDS_REQUEST_BODY = [
        'vehicles_prices_standard_vehicles_addition' => '123.34',
        'vehicles_prices_standard_atv_motorcycle_addition' => '145.35',
        'vehicles_prices_premium_low_volume_vehicles_addition' => '353.54',
        'vehicles_prices_premium_low_volume_atv_motorcycle_addition' => '123.24',
        'vehicles_prices_premium_high_volume_vehicles_addition' => '100.00',
        'vehicles_prices_premium_high_volume_atv_motorcycle_addition' => '0',
        'vehicles_prices_vip_low_volume_vehicles_addition' => '99.99',
        'vehicles_prices_vip_low_volume_atv_motorcycle_addition' => '1',
        'vehicles_prices_vip_high_volume_vehicles_addition' => '2',
        'vehicles_prices_vip_high_volume_atv_motorcycle_addition' => '3',
        'vehicles_prices_default_broker_fee' => '31.21',
    ];

    /**
     * @var array<string, int>
     */
    private const PRICE_FIELDS_DATABASE = [
        'vehicles_prices_standard_vehicles_addition' => 12334,
        'vehicles_prices_standard_atv_motorcycle_addition' => 14535,
        'vehicles_prices_premium_low_volume_vehicles_addition' => 35354,
        'vehicles_prices_premium_low_volume_atv_motorcycle_addition' => 12324,
        'vehicles_prices_premium_high_volume_vehicles_addition' => 10000,
        'vehicles_prices_premium_high_volume_atv_motorcycle_addition' => 0,
        'vehicles_prices_vip_low_volume_vehicles_addition' => 9999,
        'vehicles_prices_vip_low_volume_atv_motorcycle_addition' => 1,
        'vehicles_prices_vip_high_volume_vehicles_addition' => 2,
        'vehicles_prices_vip_high_volume_atv_motorcycle_addition' => 3,
        'vehicles_prices_default_broker_fee' => 3121,
    ];

    public function testSavePriceFields(): void
    {
        $user = $this->superAdminUser();
        $this->actingAs($user);
        $response = $this->put(
            route('admin.settings.update'),
            self::PRICE_FIELDS_REQUEST_BODY
        );

        $response->assertStatus(302);
        foreach (self::PRICE_FIELDS_DATABASE as $key => $value) {
            $this->assertDatabaseHas(
                (new Option())->getTable(),
                [
                    'optionable_id' => null,
                    'optionable_type' => null,
                    'key' => $key,
                    'value' => $value,
                ]
            );
        }
    }

    public function testUpdatePriceFields(): void
    {
        foreach (self::PRICE_FIELDS_DATABASE as $key => $value) {
            $option = new Option();
            $option->key = $key;
            $option->value = $value; // @phpstan-ignore-line
            $option->save();
        }

        $user = $this->superAdminUser();
        $this->actingAs($user);
        $response = $this->put(
            route('admin.settings.update'),
            array_fill_keys(array_keys(self::PRICE_FIELDS_REQUEST_BODY), '1.23')
        );

        $response->assertStatus(302);
        foreach (array_keys(self::PRICE_FIELDS_DATABASE) as $key) {
            $this->assertDatabaseHas(
                (new Option())->getTable(),
                [
                    'optionable_id' => null,
                    'optionable_type' => null,
                    'key' => $key,
                    'value' => 123,
                ]
            );
        }
    }
}
