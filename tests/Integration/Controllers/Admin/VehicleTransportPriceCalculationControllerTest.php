<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Admin;

use App\Models\City;
use App\Models\Option;
use App\Models\VehicleDeliveryLocation;
use App\Project\Option\Enums\GlobalOptions;
use App\Project\User\Enums\UserAccountTypes;
use Tests\Integration\IntegrationTestCase;
use Tests\Integration\VehicleCalculationDataProvider;

class VehicleTransportPriceCalculationControllerTest extends IntegrationTestCase
{
    use VehicleCalculationDataProvider;

    public function testVehicleTransportPrices(): void
    {
        $pickUpCity = City::factory()->create();
        $deliveryLocation = VehicleDeliveryLocation::factory()->create();
        $this->createExampleVehicleCalculationData($pickUpCity, $deliveryLocation);
        $user = $this->userInstance(UserAccountTypes::VIP_LOW);
        $vehicle = $this->vehicleInstance(
            user: $user,
            vehicleDeliveryLocation: $deliveryLocation
        );
        $vehiclesAddition = 200 * 100;
        $motorcycleAndAtvAddition = 50 * 100;
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_VIP_LOW_VOLUME_VEHICLES_ADDITION,
            'value' => $vehiclesAddition,
        ]);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_VIP_LOW_VOLUME_ATV_MOTORCYCLE_ADDITION,
            'value' => $motorcycleAndAtvAddition,
        ]);
        $superAdmin = $this->superAdminUser();

        $this->actingAs($superAdmin);
        $response = $this->get(route('admin.vehicles.prices', [$vehicle, $pickUpCity]));

        $response->assertSuccessful();
        $this->assertVehicleCalculationPrices($response, $vehiclesAddition, $motorcycleAndAtvAddition);
    }
}
