<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Admin;

use App\Models\Access;
use App\Models\Message;
use App\Models\Notification;
use App\Models\Order;
use App\Models\Type;
use App\Models\UserNotification;
use App\Project\User\Enums\Roles;
use Tests\Integration\IntegrationTestCase;
use Tests\TestsMailer;

class MessagesControllerTest extends IntegrationTestCase
{
    use TestsMailer;

    public function testAssignWorkers(): void
    {
        $user = $this->superAdminUser();
        $type = Type::factory()->create([
            'slug' => Type::PARCELS_SLUG,
        ]);
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
            'type_id' => $type->getKey(),
        ]);
        $message = Message::factory()->create([
            'author_id' => $user->getKey(),
            'messageable_id' => $order->getKey(),
            'messageable_type' => $order->getMorphClass(),
        ]);
        $firstWorker = $this->userInstance(role: Roles::WORKER);
        $secondWorker = $this->userInstance(role: Roles::WORKER);
        $thirdWorker = $this->userInstance(role: Roles::WORKER);
        $message->messageable
            ->messageUsers()
            ->attach(
                $thirdWorker,
                [
                    'manager_access' => true,
                    'extra' => ['messages_access' => true],
                    'accessable_type' => $order->getMorphClass(),
                ]
            );

        $this->actingAs($user);
        $response = $this->put(
            route('admin.messages.assign-workers', $message),
            [
                'message_id' => $message->getKey(),
                'workers' => [$firstWorker->getKey(), $secondWorker->getKey()],
            ]
        );

        $response->assertNoContent();
        $this->assertDatabaseHas(
            (new Access())->getTable(),
            [
                'user_id' => $firstWorker->getKey(),
                'accessable_id' => $order->getKey(),
                'accessable_type' => $order->getMorphClass(),
                'manager_access' => true,
            ]
        );
        $this->assertDatabaseHas(
            (new Access())->getTable(),
            [
                'user_id' => $secondWorker->getKey(),
                'accessable_id' => $order->getKey(),
                'accessable_type' => $order->getMorphClass(),
                'manager_access' => true,
            ]
        );
        $this->assertDatabaseMissing(
            (new Access())->getTable(),
            [
                'user_id' => $thirdWorker->getKey(),
            ]
        );
    }

    public function testStoreMessage(): void
    {
        $adminUser = $this->superAdminUser();
        $user = $this->userInstance();
        UserNotification::factory()
            ->for($user)
            ->create(['new_reply' => true]);
        $type = Type::factory()->create([
            'slug' => Type::PARCELS_SLUG,
        ]);
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
            'type_id' => $type->getKey(),
        ]);
        $content = 'test';

        $this->mockMailerSend(
            view: 'emails.notification',
            to: $user->email,
            data: [
                'subject' => __('admin/email.reply_notification.subject', [], 'pl'),
                'content' => $notificationContent = __(
                    'admin/email.reply_notification.content.order',
                    ['id' => $order->id],
                    'pl'
                ).' <br>'.$content,
                'url' => route('warehouse.orders.show', [$order->warehouse->slug, $order->type->slug, $order->id]
                ),
                'urlTitle' => __('notices.title'),
            ],
            bcc: config('mail.bcc'),
        );
        $this->actingAs($adminUser);
        $response = $this->post(
            route('admin.messages.store'),
            [
                'messageable_id' => $order->getKey(),
                'messageable_type' => $order->getMorphClass(),
                'content' => $content,
            ]
        );

        $response->assertRedirect();
        $this->assertDatabaseHas(
            (new Message())->getTable(),
            [
                'messageable_id' => $order->getKey(),
                'messageable_type' => $order->getMorphClass(),
                'content' => $content,
            ]
        );
        $this->assertDatabaseHas(
            (new Notification())->getTable(),
            [
                'link' => "warehouse/{$order->warehouse->slug}/parcels/orders/{$order->id}",
                'user_id' => $user->getKey(),
                'content' => $this->castAsJson([
                    'pl' => $notificationContent,
                    'en' => $notificationContent,
                ]),
            ]
        );
    }

    public function testStoreMessageWithoutEmailNotification(): void
    {
        $adminUser = $this->superAdminUser();
        $user = $this->userInstance();
        UserNotification::factory()
            ->for($user)
            ->create(['new_reply' => false]);
        $type = Type::factory()->create([
            'slug' => Type::PARCELS_SLUG,
        ]);
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
            'type_id' => $type->getKey(),
        ]);
        $content = 'test';
        $notificationContent = __(
            'admin/email.reply_notification.content.order',
            ['id' => $order->id],
            'pl'
        ).' <br>'.$content;

        $this->mockMailerNothingSent();
        $this->actingAs($adminUser);
        $response = $this->post(
            route('admin.messages.store'),
            [
                'messageable_id' => $order->getKey(),
                'messageable_type' => $order->getMorphClass(),
                'content' => $content,
            ]
        );

        $response->assertRedirect();
        $this->assertDatabaseHas(
            (new Message())->getTable(),
            [
                'messageable_id' => $order->getKey(),
                'messageable_type' => $order->getMorphClass(),
                'content' => $content,
            ]
        );
        $this->assertDatabaseHas(
            (new Notification())->getTable(),
            [
                'link' => "warehouse/{$order->warehouse->slug}/parcels/orders/{$order->id}",
                'user_id' => $user->getKey(),
                'content' => $this->castAsJson([
                    'pl' => $notificationContent,
                    'en' => $notificationContent,
                ]),
            ]
        );
    }
}
