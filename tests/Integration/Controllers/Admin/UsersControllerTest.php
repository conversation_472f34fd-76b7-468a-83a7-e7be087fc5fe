<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Admin;

use App\Models\Option;
use App\Models\User;
use App\Project\User\Enums\UserAccountTypes;
use Tests\Integration\IntegrationTestCase;

class UsersControllerTest extends IntegrationTestCase
{
    public function testUpdateOptions(): void
    {
        $user = User::factory()->create();
        $admin = $this->superAdminUser();

        $this->actingAs($admin);
        $response = $this->patch(
            route('admin.users.update', $user),
            [
                'options' => [
                    'account_type' => UserAccountTypes::PREMIUM_LOW,
                    'broker_fee_copart' => '123.23',
                    'broker_fee_iaa' => '134.23',
                    'broker_fee_other' => '567.45',
                    'broker_fee_cw_pays' => '267.45',
                    'payment_deadline_days' => '12',
                ],
                'billing' => [],
                'notifications' => [],
            ]
        );

        $response->assertStatus(302);
        foreach (
            [
                'account_type' => UserAccountTypes::PREMIUM_LOW,
                'broker_fee_copart' => 12323,
                'broker_fee_iaa' => 13423,
                'broker_fee_other' => 56745,
                'broker_fee_cw_pays' => 26745,
                'payment_deadline_days' => 12,
            ] as $key => $value
        ) {
            $this->assertDatabaseHas(
                (new Option())->getTable(),
                [
                    'optionable_id' => $user->getKey(),
                    'optionable_type' => $user::class,
                    'key' => $key,
                    'value' => $value,
                ]
            );
        }
    }
}
