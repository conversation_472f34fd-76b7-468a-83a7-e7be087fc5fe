<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers\Admin;

use App\Models\Driver;
use App\Models\DriverVehicle;
use App\Project\Export\Sheet;
use App\Project\Export\Sheets;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Testing\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Mockery;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidFactoryInterface;
use Ramsey\Uuid\UuidInterface;
use Tests\Integration\IntegrationTestCase;

class DriversControllerTest extends IntegrationTestCase
{
    use WithFaker;

    private const DRIVER_FILES_FIELDS = [
        'w9' => '6494d8aa-9396-463a-aa92-86ce79da32ba',
        'insurance' => '6494d8aa-9396-463a-aa92-86ce79da32bb',
        'irs' => '6494d8aa-9396-463a-aa92-86ce79da32bc',
        'dot_certificate' => '6494d8aa-9396-463a-aa92-86ce79da32bd',
    ];

    public function testStore(): void
    {
        $this->mockStoreData();
        $user = $this->superAdminUser();
        $driver = Driver::factory()->make();
        $driverData = $this->driverData($driver);

        $this->actingAs($user);
        $response = $this->post(
            route('admin.drivers.store'),
            $driverData
        );

        $response->assertRedirect(route('admin.drivers.edit', Driver::firstOrFail()));
        $this->assertDriver($driverData);
    }

    public function testUpdate(): void
    {
        $this->mockStoreData();
        $user = $this->superAdminUser();
        $driver = Driver::factory()->create();
        foreach (self::DRIVER_FILES_FIELDS as $key => $uuid) {
            Storage::disk(config('filesystems.cloud'))
                ->putFileAs(
                    "testing/driver/{$key}",
                    UploadedFile::fake()->create("{$key}.pdf"),
                    "{$key}.pdf"
                );
            $driver->{$key} = "{$key}.pdf";
            $driver->save();
        }
        $newDriver = Driver::factory()->make();
        $driverData = $this->driverData($newDriver);

        $this->actingAs($user);
        $response = $this->put(
            route('admin.drivers.update', $driver),
            $driverData
        );

        $response->assertRedirect(route('admin.drivers.edit', $driver));
        $this->assertDriver($driverData);
    }

    public function testExport(): void
    {
        Excel::fake();
        Excel::matchByRegex();
        $user = $this->superAdminUser();
        $drivers = Driver::factory()
            ->count(3)
            ->create()
            ->each(function (Driver $driver, int $key): void {
                $vehicle = $this->vehicleInstance();
                DriverVehicle::factory()
                    ->for($driver)
                    ->for($vehicle)
                    ->count(3)
                    ->createQuietly([
                        'paid_at' => $this->faker->dateTimeBetween(
                            Carbon::now()->subYear()->startOfYear(),
                            Carbon::now()->subYear()->endOfYear(),
                        ),
                        'transport_price' => 100 * ($key + 1),
                    ]);
            });

        $this->actingAs($user);
        $response = $this->get(route('admin.drivers.export'));

        $response->assertSuccessful();

        Excel::assertDownloaded('/drivers_.+\.csv/', function (Sheets $sheets) use ($drivers): bool {
            /** @var Sheet $sheet */
            $sheet = $sheets->sheets()[0];
            $driversData = $sheet->array();
            $this->assertCount($drivers->count(), $driversData);
            $drivers->each(function (Driver $driver, int $key) use ($driversData, $drivers): void {
                $driverData = $driversData[$drivers->count() - $key - 1];
                $this->assertSame($driver->tax_company_name, $driverData['Recipient\'s Name']);
                $this->assertSame(
                    price_to_string(
                        price: ($key + 1) * 100 * 3,
                        decPoint: '.',
                        thousandSep: '',
                    ),
                    $driverData['Box 1 Nonemployee compensation']
                );
            });

            return true;
        });
    }

    public function testDestroy(): void
    {
        $user = $this->superAdminUser();
        $driver = Driver::factory()->create();

        $this->actingAs($user);
        $response = $this->delete(route('admin.drivers.destroy', $driver));

        $response->assertOk();
        $this->assertDatabaseMissing($driver->getTable(), [
            'id' => $driver->getKey(),
        ]);
    }

    private function mockStoreData(): void
    {
        Storage::fake(config('filesystems.cloud'));
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(...array_map(fn (string $uuid): UuidInterface => Uuid::fromString($uuid), self::DRIVER_FILES_FIELDS))
            ->getMock();
        Uuid::setFactory($factoryMock);
    }

    private function driverData(Driver $driver): array
    {
        return [
            'company_name' => $driver->company_name,
            'dot_number' => $driver->dot_number,
            'payments' => $driver->payments,
            'tax_number_type' => $driver->tax_number_type->value,
            'tax_number' => $driver->tax_number,
            'tax_company_name' => $driver->tax_company_name,
            'email' => $driver->email,
            'address' => $driver->address,
            'zip_code' => $driver->zip_code,
            'city_id' => $driver->city_id,
            'comment' => $driver->comment,
        ] + array_combine(
            array_keys(self::DRIVER_FILES_FIELDS),
            array_map(
                fn (string $fileKey): File => UploadedFile::fake()->create("{$fileKey}.pdf"),
                array_keys(self::DRIVER_FILES_FIELDS)
            )
        );
    }

    private function assertDriver(array $driverData): void
    {
        $this->assertDatabaseHas(
            (new Driver())->getTable(),
            [
                ...$driverData,
                ...array_combine(
                    array_keys(self::DRIVER_FILES_FIELDS),
                    array_map(
                        fn (string $uuid): string => "{$uuid}.pdf",
                        array_values(self::DRIVER_FILES_FIELDS),
                    ),
                ),
            ]
        );
        foreach (self::DRIVER_FILES_FIELDS as $key => $uuid) {
            Storage::disk(config('filesystems.cloud'))
                ->assertExists("testing/driver/{$key}/{$uuid}.pdf");
            Storage::disk(config('filesystems.cloud'))
                ->assertMissing("testing/driver/{$key}/{$key}.pdf");
        }
    }
}
