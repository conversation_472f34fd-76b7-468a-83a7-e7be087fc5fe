<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\City;
use App\Models\Option;
use App\Models\State;
use App\Models\User;
use App\Models\VehicleDeliveryLocation;
use App\Models\VehicleTitleType;
use App\Project\Option\Enums\GlobalOptions;
use App\Project\User\Enums\UserAccountTypes;
use App\ViewModels\Dashboard\Settings\SettingsPriceListViewModel;
use Illuminate\Pagination\LengthAwarePaginator;
use Tests\Integration\IntegrationTestCase;
use Tests\Integration\VehicleCalculationDataProvider;

class SettingsControllerTest extends IntegrationTestCase
{
    use VehicleCalculationDataProvider;

    public function testPriceList(): void
    {
        $pickUpCity = City::factory()->create([
            'name' => 'Kemmertown',
            'state_id' => fn () => State::factory()->create(['code' => 'bH'])->getKey(),
        ]);
        $deliveryLocation = VehicleDeliveryLocation::factory()->create();
        $this->createExampleVehicleCalculationData($pickUpCity, $deliveryLocation);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_VEHICLES_ADDITION,
            'value' => 100 * 100,
        ]);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_ATV_MOTORCYCLE_ADDITION,
            'value' => 50 * 100,
        ]);
        $user = $this->userInstance(UserAccountTypes::PREMIUM_LOW);

        $this->actingAs($user);
        $response = $this->get(
            route(
                'settings.price-list.index',
                [
                    'accessType' => UserAccountTypes::PREMIUM_LOW,
                    'delivery_location' => $deliveryLocation,
                ]
            )
        );

        $response->assertSuccessful();
        $response->assertViewHas('viewModel', function (SettingsPriceListViewModel $viewModel): bool {
            $compareData = [
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$500,00',
                    'New York, NW' => '$400,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.big_vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$575,00',
                    'New York, NW' => '$450,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.very_big_vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$650,00',
                    'New York, NW' => '$500,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.motorcycle'), // @phpstan-ignore-line
                    'California, NW' => '-',
                    'New York, NW' => '$1.550,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.atv'), // @phpstan-ignore-line
                    'California, NW' => '-',
                    'New York, NW' => '$1.650,00',
                ],
            ];
            $this->assertSame($viewModel->vehiclePrices?->all(), $compareData);

            return true;
        });
    }

    public function testPriceListWithHiddenCity(): void
    {
        $pickUpCity = City::factory()->create([
            'name' => 'Kemmertown',
            'state_id' => fn () => State::factory()->create(['code' => 'bH'])->getKey(),
        ]);
        $deliveryLocation = VehicleDeliveryLocation::factory()->create();
        $this->createExampleVehicleCalculationData(
            pickUpCity: $pickUpCity,
            deliveryLocation: $deliveryLocation,
            hideNewYork: true
        );
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_VEHICLES_ADDITION,
            'value' => 100 * 100,
        ]);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_ATV_MOTORCYCLE_ADDITION,
            'value' => 50 * 100,
        ]);
        $user = $this->userInstance(UserAccountTypes::PREMIUM_LOW);

        $this->actingAs($user);
        $response = $this->get(
            route(
                'settings.price-list.index',
                [
                    'accessType' => UserAccountTypes::PREMIUM_LOW,
                    'delivery_location' => $deliveryLocation,
                ]
            )
        );

        $response->assertSuccessful();
        $response->assertViewHas('viewModel', function (SettingsPriceListViewModel $viewModel): bool {
            $compareData = [
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$500,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.big_vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$575,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.very_big_vehicle'), // @phpstan-ignore-line
                    'California, NW' => '$650,00',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.motorcycle'), // @phpstan-ignore-line
                    'California, NW' => '-',
                ],
                [
                    __('settings.vehicle_pricing.city') => 'Kemmertown, bH', // @phpstan-ignore-line
                    __('settings.vehicle_pricing.type') => __('vehicles.size_types.atv'), // @phpstan-ignore-line
                    'California, NW' => '-',
                ],
            ];
            $this->assertSame($viewModel->vehiclePrices?->all(), $compareData);

            return true;
        });
    }

    public function testTitleTypesIndex(): void
    {
        $user = User::factory()->create();
        VehicleTitleType::factory()
            ->count(3)
            ->create([
                'status' => VehicleTitleType::OK_STATUS,
            ]);

        $this->actingAs($user);
        $response = $this->get(route('settings.title-types'));

        $response->assertSuccessful();
        $response->assertViewHas(
            'vehicleTitleTypes',
            fn (LengthAwarePaginator $vehicleTitleTypes): bool => count($vehicleTitleTypes->items()) === 3
        );
    }

    public function testTitleTypesIndexWithSearchAndStatus(): void
    {
        $user = User::factory()->create();
        VehicleTitleType::factory()
            ->count(3)
            ->create();
        $vehicleTitleType = VehicleTitleType::factory()
            ->create([
                'name' => 'Test nazwy',
                'status' => VehicleTitleType::NOT_OK_STATUS,
            ]);

        $this->actingAs($user);
        $response = $this->get(
            route(
                'settings.title-types',
                ['status' => VehicleTitleType::OK_STATUS, 'search' => 'Test nazwy']
            )
        );

        $response->assertSuccessful();
        $response->assertViewHas(
            'vehicleTitleTypes',
            fn (LengthAwarePaginator $vehicleTitleTypes): bool => count(
                $vehicleTitleTypes->items()
            ) === 1 && $vehicleTitleTypes->items()[0]->id === $vehicleTitleType->id
        );
    }
}
