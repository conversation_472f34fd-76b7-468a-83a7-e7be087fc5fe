<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Company;
use App\Models\Order;
use App\Models\Transaction;
use App\Models\TransactionTemplate;
use App\Models\User;
use App\Project\User\Enums\Roles;
use Tests\Integration\IntegrationTestCase;

class ChartControllerTest extends IntegrationTestCase
{
    public function testDataForRegularUser(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
        ]);
        $this->vehicleInstance(
            order: $order,
            user: $user,
            data: [
                'created_at' => '2018-12-14 18:44:03',
            ]
        );

        $this->actingAs($user);
        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'vehicles',
                'is_pa' => false,
            ])
        );

        $response->assertSuccessful();
        $response->assertJson([
            'Jan-2018' => 0,
            'Feb-2018' => 0,
            'Mar-2018' => 0,
            'Apr-2018' => 0,
            'May-2018' => 0,
            'Jun-2018' => 0,
            'Jul-2018' => 0,
            'Aug-2018' => 0,
            'Sep-2018' => 0,
            'Oct-2018' => 0,
            'Nov-2018' => 0,
            'Dec-2018' => 1,
        ]);
    }

    public function testHasNoAccessToUsersData(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);
        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'users',
                'is_pa' => false,
            ])
        );

        $response->assertStatus(403);
    }

    public function testHasNoAccessToPaData(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);
        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'vehicles',
                'is_pa' => true,
            ])
        );

        $response->assertStatus(403);
    }

    public function testHasAccessToPaDataAsAdmin(): void
    {
        User::factory()->create([
            'created_at' => '2018-12-14 18:44:03',
        ]);
        $adminUser = User::factory()->create([
            'role' => Roles::SUPER_ADMIN,
        ]);

        $this->actingAs($adminUser);
        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'users',
                'is_pa' => true,
            ])
        );

        $response->assertSuccessful();
        $response->assertJson([
            'Jan-2018' => 0,
            'Feb-2018' => 0,
            'Mar-2018' => 0,
            'Apr-2018' => 0,
            'May-2018' => 0,
            'Jun-2018' => 0,
            'Jul-2018' => 0,
            'Aug-2018' => 0,
            'Sep-2018' => 0,
            'Oct-2018' => 0,
            'Nov-2018' => 0,
            'Dec-2018' => 1,
        ]);
    }

    public function testDataForIncomeReport(): void
    {
        $user = User::factory()->create();
        $template = TransactionTemplate::factory()->create([
            'id' => 2,
            'slug' => 'transaction_template',
            'type' => 'transaction',
        ]);
        $company = Company::factory()->create();
        Transaction::factory()->create([
            'amount' => 10000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $company->getKey(),
            'paid_at' => '2018-12-14 18:44:03',
        ]);
        Transaction::factory()->create([
            'amount' => 10000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $company->getKey(),
            'paid_at' => '2018-12-24 18:54:00',
        ]);
        Transaction::factory()->create([
            'amount' => 5000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $company->getKey(),
            'paid_at' => '2018-11-01 18:00:00',
        ]);

        $adminUser = User::factory()->create([
            'role' => Roles::SUPER_ADMIN,
        ]);
        $this->actingAs($adminUser);

        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'income',
                'is_pa' => 1,
            ])
        );

        $response->assertSuccessful();
        $response->assertJson([
            'Jan-2018' => 0,
            'Feb-2018' => 0,
            'Mar-2018' => 0,
            'Apr-2018' => 0,
            'May-2018' => 0,
            'Jun-2018' => 0,
            'Jul-2018' => 0,
            'Aug-2018' => 0,
            'Sep-2018' => 0,
            'Oct-2018' => 0,
            'Nov-2018' => 5000,
            'Dec-2018' => 20000,
        ]);
    }

    public function testIncomeReportForCompany(): void
    {
        $user = User::factory()->create();
        $template = TransactionTemplate::factory()->create([
            'id' => 2,
            'slug' => 'transaction_template',
            'type' => 'transaction',
        ]);
        $company = Company::factory()->create();
        $anotherCompany = Company::factory()->create();
        Transaction::factory()->create([
            'amount' => 10000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $company->getKey(),
            'paid_at' => '2018-12-14 18:44:03',
        ]);
        Transaction::factory()->create([
            'amount' => 10000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $anotherCompany->getKey(),
            'paid_at' => '2018-12-24 18:54:00',
        ]);
        Transaction::factory()->create([
            'amount' => 5000,
            'template_id' => $template->getKey(),
            'user_id' => $user->getKey(),
            'company_id' => $anotherCompany->getKey(),
            'paid_at' => '2018-11-01 18:00:00',
        ]);

        $adminUser = User::factory()->create([
            'role' => Roles::SUPER_ADMIN,
        ]);
        $this->actingAs($adminUser);

        $response = $this->json(
            'GET',
            route('chart.data', [
                'year' => 2018,
                'type' => 'income',
                'is_pa' => 1,
                'company_id' => $anotherCompany->getKey(),
            ])
        );

        $response->assertSuccessful();
        $response->assertJson([
            'Jan-2018' => 0,
            'Feb-2018' => 0,
            'Mar-2018' => 0,
            'Apr-2018' => 0,
            'May-2018' => 0,
            'Jun-2018' => 0,
            'Jul-2018' => 0,
            'Aug-2018' => 0,
            'Sep-2018' => 0,
            'Oct-2018' => 0,
            'Nov-2018' => 5000,
            'Dec-2018' => 10000,
        ]);
    }
}
