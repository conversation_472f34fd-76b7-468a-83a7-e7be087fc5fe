<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Notification;
use App\Models\User;
use Carbon\Carbon;
use Tests\Integration\IntegrationTestCase;

class NotificationsControllerTest extends IntegrationTestCase
{
    public function testMarkAllAsRead(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $user = User::factory()->create();
        $notifications = Notification::factory()
            ->count(3)
            ->create([
                'user_id' => $user->getKey(),
                'displayed_at' => null,
            ]);

        $this->actingAs($user);
        $response = $this->json(
            'GET',
            route('notifications.mark-all-as-read')
        );

        $response->assertSuccessful();
        foreach ($notifications as $notification) {
            $this->assertDatabaseHas((new Notification())->getTable(), [
                'id' => $notification->getKey(),
                'displayed_at' => $now,
            ]);
        }
    }
}
