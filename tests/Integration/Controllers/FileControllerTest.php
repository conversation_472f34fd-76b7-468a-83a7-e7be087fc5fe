<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Order;
use App\Models\User;
use App\Models\Vehicle;
use App\Project\Storage\CloudStorage;
use App\Project\Storage\CloudStorageModelsMapper;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidFactoryInterface;
use Tests\Integration\IntegrationTestCase;

class FileControllerTest extends IntegrationTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        Storage::fake(config('filesystems.cloud'));
    }

    public function testShowMultipleWithOneFile(): void
    {
        $fileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($fileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $order = Order::factory()->make([
            'user_id' => $user->getKey(),
        ]);
        $file = UploadedFile::fake()->create(name: 'document1.pdf', kilobytes: 500);
        $order->user_invoice = [
            $this->cloudStorage()
                ->storeFile(
                    file: $file,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFilesRoute($order, 'user_invoice'));

        $response->assertRedirect(
            route('files.show', ['order', $order->getKey(), 'user_invoice', "{$fileName}.pdf"])
        );
    }

    public function testShowMultipleWithMultipleFiles(): void
    {
        $firstFileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $secondFileName = '6494d8aa-9396-463a-aa92-86ce79da32cd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($firstFileName))
            ->once()
            ->getMock()
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($secondFileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $order = Order::factory()->make([
            'user_id' => $user->getKey(),
        ]);
        $firstFile = UploadedFile::fake()->create(name: 'document1.pdf', kilobytes: 500);
        $secondFile = UploadedFile::fake()->create(name: 'document2.pdf', kilobytes: 500);
        $order->user_invoice = [
            $this->cloudStorage()
                ->storeFile(
                    file: $firstFile,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
            $this->cloudStorage()
                ->storeFile(
                    file: $secondFile,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFilesRoute($order, 'user_invoice'));

        $response->assertSuccessful();
        $this->assertNotEmpty($response->streamedContent());
    }

    public function testShowMultipleForUnauthorized(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->make();
        $firstFile = UploadedFile::fake()->create(name: 'document1.pdf', kilobytes: 500);
        $secondFile = UploadedFile::fake()->create(name: 'document2.pdf', kilobytes: 500);
        $order->user_invoice = [
            $this->cloudStorage()
                ->storeFile(
                    file: $firstFile,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
            $this->cloudStorage()
                ->storeFile(
                    file: $secondFile,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFilesRoute($order, 'user_invoice'));

        $response->assertForbidden();
    }

    public function testShow(): void
    {
        $fileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($fileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $order = Order::factory()->make([
            'user_id' => $user->getKey(),
        ]);
        $file = UploadedFile::fake()->image(name: 'document1.png');
        $order->user_invoice = [
            $this->cloudStorage()
                ->storeFile(
                    file: $file,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFileRoute(
            fileName: "{$fileName}.png",
            model: $order,
            attributeName: 'user_invoice'
        ));

        $response->assertSuccessful();
        $this->assertSame(file_get_contents($file->getPathname()), $response->streamedContent());
    }

    public function testShowForEncryptedFile(): void
    {
        $fileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($fileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $order = Order::factory()->make([
            'user_id' => $user->getKey(),
        ]);
        $file = UploadedFile::fake()->image(name: 'document1.png');
        $order->user_invoice = [
            $this->cloudStorage()
                ->encryptedStoreFile(
                    file: $file,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFileRoute(
            fileName: "{$fileName}.png",
            model: $order,
            attributeName: 'user_invoice',
            encrypted: true,
        ));

        $response->assertSuccessful();
        $this->assertSame(file_get_contents($file->getPathname()), $response->streamedContent());
    }

    public function testShowForUnauthorized(): void
    {
        $fileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($fileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $order = Order::factory()->make();
        $file = UploadedFile::fake()->create(name: 'document1.pdf', kilobytes: 500);
        $order->user_invoice = [
            $this->cloudStorage()
                ->storeFile(
                    file: $file,
                    model: $order,
                    attributeName: 'user_invoice'
                ),
        ];
        $order->saveQuietly();

        $this->actingAs($user);
        $response = $this->get(CloudStorage::viewFileRoute(
            fileName: "{$fileName}.pdf",
            model: $order,
            attributeName: 'user_invoice'
        ));

        $response->assertForbidden();
    }

    public function testRemoveFile(): void
    {
        $filePath = 'testing/vehicle/files/';
        $fileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($fileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $file = UploadedFile::fake()->create(name: 'document1.pdf', kilobytes: 500);
        $vehicle = $this->vehicleInstance(data: [
            'title_file' => [
                $this->cloudStorage()
                    ->storeFile(
                        file: $file,
                        model: new Vehicle(),
                        attributeName: 'title_file'
                    ),
            ],
        ]);

        $this->actingAs($vehicle->user);
        $response = $this->delete(route('files.remove', [
            'modelAlias' => CloudStorageModelsMapper::alias($vehicle),
            'id' => $vehicle->getKey(),
            'attributeName' => 'title_file',
        ]), [
            'filename' => "{$fileName}.pdf",
        ]);

        $response->assertSuccessful();
        $this->assertDatabaseHas($vehicle->getTable(), [
            'id' => $vehicle->getKey(),
            'title_file' => null,
        ]);
        $this->storage()->assertMissing("{$filePath}{$fileName}.pdf");
    }

    public function testRemoveFileUnauthorized(): void
    {
        $user = User::factory()->create();
        $vehicle = $this->vehicleInstance(data: [
            'title_file' => [
                'test_file.pdf',
            ],
        ]);

        $this->actingAs($user);

        $response = $this->delete(route('files.remove', [
            'modelAlias' => CloudStorageModelsMapper::alias($vehicle),
            'id' => $vehicle->getKey(),
            'attributeName' => 'title_file',
        ]), [
            'filename' => 'test_file.pdf',
        ]);

        $response->assertForbidden();
    }

    private function storage(): FilesystemAdapter
    {
        return Storage::disk(config('filesystems.cloud'));
    }

    private function cloudStorage(): CloudStorage
    {
        return $this->app->make(CloudStorage::class);
    }
}
