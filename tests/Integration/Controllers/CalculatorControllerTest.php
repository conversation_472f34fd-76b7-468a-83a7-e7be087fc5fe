<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\City;
use App\Models\Option;
use App\Models\VehicleDeliveryLocation;
use App\Project\Option\Enums\GlobalOptions;
use Tests\Integration\IntegrationTestCase;
use Tests\Integration\VehicleCalculationDataProvider;

class CalculatorControllerTest extends IntegrationTestCase
{
    use VehicleCalculationDataProvider;

    public function testVehicleCalculation(): void
    {
        $pickUpCity = City::factory()->create();
        $deliveryLocation = VehicleDeliveryLocation::factory()->create();
        $this->createExampleVehicleCalculationData(pickUpCity: $pickUpCity, deliveryLocation: $deliveryLocation);
        $vehiclesAddition = 300 * 100;
        $motorcycleAndAtvAddition = 100 * 100;
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_STANDARD_VEHICLES_ADDITION,
            'value' => $vehiclesAddition,
        ]);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_STANDARD_ATV_MOTORCYCLE_ADDITION,
            'value' => $motorcycleAndAtvAddition,
        ]);

        $response = $this->json(
            'GET',
            route('calculators.vehicle-calculation'),
            [
                'city_id' => $pickUpCity->getKey(),
                'vehicle_delivery_location_id' => $deliveryLocation->getKey(),
            ]
        );

        $response->assertSuccessful();
        $this->assertVehicleCalculationPrices($response, $vehiclesAddition, $motorcycleAndAtvAddition);
    }

    public function testVehicleCalculationWithHiddenCity(): void
    {
        $pickUpCity = City::factory()->create();
        $deliveryLocation = VehicleDeliveryLocation::factory()->create();
        $this->createExampleVehicleCalculationData(
            pickUpCity: $pickUpCity,
            deliveryLocation: $deliveryLocation,
            hideNewYork: true
        );
        $vehiclesAddition = 300 * 100;
        $motorcycleAndAtvAddition = 100 * 100;
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_STANDARD_VEHICLES_ADDITION,
            'value' => $vehiclesAddition,
        ]);
        Option::factory()->create([
            'key' => GlobalOptions::VEHICLES_PRICES_STANDARD_ATV_MOTORCYCLE_ADDITION,
            'value' => $motorcycleAndAtvAddition,
        ]);

        $response = $this->json(
            'GET',
            route('calculators.vehicle-calculation'),
            [
                'city_id' => $pickUpCity->getKey(),
                'vehicle_delivery_location_id' => $deliveryLocation->getKey(),
            ]
        );

        $response->assertSuccessful();
        $this->assertVehicleCalculationPrices(
            response: $response,
            vehicleAddition: $vehiclesAddition,
            motorcycleAndAtvAddition: $motorcycleAndAtvAddition,
            hideNewYork: true,
        );
    }
}
