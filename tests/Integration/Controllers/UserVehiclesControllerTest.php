<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Port;
use App\Models\UserVehicle;
use App\Models\VehicleShippingLine;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Tests\Integration\IntegrationTestCase;

class UserVehiclesControllerTest extends IntegrationTestCase
{
    public function testShow(): void
    {
        $user = $this->userInstance();
        $userVehicle = UserVehicle::factory()
            ->create(['user_id' => $user->getKey()]);

        $this->actingAs($user);
        $response = $this->get(route('dealers.user-vehicles.show', $userVehicle));

        $response->assertSuccessful();
        $response->assertViewIs('dealers.user-vehicles.show');
        $response->assertViewHas('userVehicle', $userVehicle);
    }

    public function testShowWithAnotherUser(): void
    {
        $user = $this->userInstance();
        $anotherUser = $this->userInstance();
        $userVehicle = UserVehicle::factory()
            ->create(['user_id' => $anotherUser->getKey()]);

        $this->actingAs($user);
        $response = $this->get(route('dealers.user-vehicles.show', $userVehicle));

        $response->assertForbidden();
    }

    public function testCreate(): void
    {
        Port::factory()->count(2)->create();
        VehicleShippingLine::factory()->count(2)->create();
        $user = $this->userInstance();

        $this->actingAs($user);
        $response = $this->get(route('dealers.user-vehicles.create'));

        $response->assertSuccessful();
        $response->assertViewIs('dealers.user-vehicles.create');
        $response->assertViewHas('ports', fn (Collection $ports): bool => $ports->count() === 2);
        $response->assertViewHas(
            'vehicleShippingLines',
            fn (Collection $vehicleShippingLines): bool => $vehicleShippingLines->count() === 2
        );
        $response->assertViewHas('statuses');
        $response->assertViewHas('keys');
    }

    public function testStore(): void
    {
        $userVehicle = UserVehicle::factory()->make();
        $requestData = $this->requestData(
            userVehicle: $userVehicle,
            port: Port::factory()->create(),
            vehicleShippingLine: VehicleShippingLine::factory()->create(),
        );
        $user = $this->userInstance();

        $this->actingAs($user);
        $response = $this->post(route('dealers.user-vehicles.store'), $requestData);

        $response->assertRedirect(route('dealers.user-vehicles.show', $user->userVehicles()->first()));
        $response->assertSessionHasNoErrors();
        $response->assertSessionHas('success');
        $this->assertDatabaseHas(
            (new UserVehicle())->getTable(),
            [...$requestData, 'user_id' => $user->getKey(), 'eta' => Carbon::createFromFormat('d.m.Y', $requestData['eta'])->startOfDay()]
        );
    }

    public function testUpdate(): void
    {
        $user = $this->userInstance();
        $userVehicle = UserVehicle::factory()
            ->create(['user_id' => $user->getKey()]);
        $requestData = $this->requestData(
            userVehicle: UserVehicle::factory()->make(),
            port: Port::factory()->create(),
            vehicleShippingLine: VehicleShippingLine::factory()->create(),
        );

        $this->actingAs($user);
        $response = $this->put(route('dealers.user-vehicles.update', $userVehicle), $requestData);

        $response->assertRedirect(route('dealers.user-vehicles.show', $userVehicle));
        $response->assertSessionHasNoErrors();
        $response->assertSessionHas('success');
        $this->assertDatabaseHas(
            (new UserVehicle())->getTable(),
            [...$requestData, 'user_id' => $user->getKey(), 'eta' => Carbon::createFromFormat('d.m.Y', $requestData['eta'])->startOfDay()]
        );
    }

    public function testUpdateWithAnotherUser(): void
    {
        $user = $this->userInstance();
        $anotherUser = $this->userInstance();
        $userVehicle = UserVehicle::factory()
            ->create(['user_id' => $anotherUser->getKey()]);
        $requestData = $this->requestData(
            userVehicle: UserVehicle::factory()->make(),
            port: Port::factory()->create(),
            vehicleShippingLine: VehicleShippingLine::factory()->create(),
        );

        $this->actingAs($user);
        $response = $this->put(route('dealers.user-vehicles.update', $userVehicle), $requestData);

        $response->assertForbidden();
    }

    /**
     * @return array<string, mixed>
     */
    private function requestData(UserVehicle $userVehicle, Port $port, VehicleShippingLine $vehicleShippingLine): array
    {
        return [
            'brand' => $userVehicle->brand,
            'model' => $userVehicle->model,
            'year' => $userVehicle->year,
            'vin_number' => $userVehicle->vin_number,
            'status' => $userVehicle->status,
            'keys' => $userVehicle->keys,
            'container_number' => $userVehicle->container_number,
            'eta' => $userVehicle->eta->format('d.m.Y'),
            'port_id' => $port->getKey(),
            'vehicle_shipping_line_id' => $vehicleShippingLine->getKey(),
        ];
    }
}
