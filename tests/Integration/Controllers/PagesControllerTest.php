<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Http\Middleware\DashboardSetLocale;
use Illuminate\Support\Facades\App;
use Tests\Integration\IntegrationTestCase;

class PagesControllerTest extends IntegrationTestCase
{
    public function testDashboardSetLocaleMiddleware(): void
    {
        $response = $this->get(route('en.pages.home'));

        $response->assertCookie(DashboardSetLocale::COOKIE_KEY, 'en');
        $this->assertSame('en', App::getLocale());
    }

    public function testDashboardSetLocaleMiddlewareDontSetCookieIfAlreadySet(): void
    {
        $response = $this->withCookie(DashboardSetLocale::COOKIE_KEY, 'en')
            ->get(route('en.pages.home'));

        $response->assertCookieMissing(DashboardSetLocale::COOKIE_KEY);
        $this->assertSame('en', App::getLocale());
    }

    public function testDashboardSetLocaleMiddlewareChangeCookieOnNewLocale(): void
    {
        $response = $this->withCookie(DashboardSetLocale::COOKIE_KEY, 'pl')
            ->get(route('en.pages.home'));

        $response->assertCookie(DashboardSetLocale::COOKIE_KEY, 'en');
        $this->assertSame('en', App::getLocale());
    }
}
