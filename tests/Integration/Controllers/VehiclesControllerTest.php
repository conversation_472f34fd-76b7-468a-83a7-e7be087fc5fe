<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Order;
use App\Models\User;
use App\Project\Vehicle\Enums\CustomsAgencyStatus;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Integration\IntegrationTestCase;

class VehiclesControllerTest extends IntegrationTestCase
{
    use WithFaker;

    public function testSetCustomsAgencyStatus(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
        ]);
        $vehicle = $this->vehicleInstance(
            order: $order,
            user: $user,
        );
        $newStatus = $this->faker->randomElement(CustomsAgencyStatus::ALL_TYPES);

        $this->actingAs($user);
        $response = $this->put(route('vehicles.set-customs-agency-status', $vehicle), [
            'customs_agency_status' => $newStatus,
        ]);

        $response->assertNoContent();
        $this->assertDatabaseHas(
            $vehicle->getTable(),
            ['id' => $vehicle->getKey(), 'customs_agency_status' => $newStatus]
        );
    }

    public function testSetCustomsAgencyStatusForMultipleVehicles(): void
    {
        $user = User::factory()->create();
        $firstOrder = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
        ]);
        $firstVehicle = $this->vehicleInstance(
            order: $firstOrder,
            user: $user,
        );
        $secondOrder = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
        ]);
        $secondVehicle = $this->vehicleInstance(
            order: $secondOrder,
            user: $user,
        );
        $newStatus = $this->faker->randomElement(CustomsAgencyStatus::ALL_TYPES);

        $this->actingAs($user);
        $response = $this->put(
            route('vehicles.set-customs-agency-status'),
            [
                'selected' => [$firstVehicle->getKey(), $secondVehicle->getKey()],
                'customs_agency_status' => $newStatus,
            ]
        );

        $response->assertSuccessful();
        foreach ([$firstVehicle, $secondVehicle] as $vehicle) {
            $this->assertDatabaseHas(
                $vehicle->getTable(),
                ['id' => $vehicle->getKey(), 'customs_agency_status' => $newStatus]
            );
        }
    }

    public function testValidateCreate(): void
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(
            route('vehicles.validate-create', ['vehicleId' => null, 'vin_number' => 'WAUDG74F25N111998'])
        );

        $response->assertSuccessful();
        $this->assertTrue($response->json());
    }

    public function testValidateCreateForExistingVin(): void
    {
        $user = User::factory()->create();
        $this->actingAs($user);
        $order = Order::factory()->createQuietly([
            'user_id' => $user->getKey(),
        ]);
        $this->vehicleInstance(
            order: $order,
            user: $user,
            data: [
                'vin_number' => 'WAUDG74F25N111998',
            ]
        );

        $response = $this->get(
            route('vehicles.validate-create', ['vehicleId' => null, 'vin_number' => 'WAUDG74F25N111998'])
        );

        $response->assertSuccessful();
        $this->assertIsString($response->json());
    }
}
