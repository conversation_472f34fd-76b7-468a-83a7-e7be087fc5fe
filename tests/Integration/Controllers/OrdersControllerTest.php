<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\Company;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\UserBalance;
use App\Project\Transactional\TransactionalType;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Integration\IntegrationTestCase;

class OrdersControllerTest extends IntegrationTestCase
{
    use WithFaker;

    public function testPostValuation(): void
    {
        $this->seedOrderRelatedData();
        $vehicle = $this->vehicleInstance();
        $company = Company::factory()->create();
        $status = OrderStatus::whereSlug(OrderStatus::PAID_SLUG)->firstOrFail();
        $admin = $this->superAdminUser();
        $fields = [
            'price_purchase',
            'price_purchase_negative',
            'price_payment',
            'price_ocean_shipping',
            'price_landing_shipping',
            'price_cleaning',
            'price_tax',
            'price_insurance',
            'price_garbage_removal',
            'price_moisture_absorber',
            'price_commission',
            'price_discount',
            'price_hidden',
        ];
        $prices = array_combine(
            $fields,
            array_map(
                fn (string $field): string => (string) $this->faker->randomFloat(nbMaxDecimals: 2, max: 99999),
                $fields
            )
        );
        $pricesNormalized = array_map(
            fn (string $price): int => clearMoneyInput($price),
            $prices
        );
        $balance = -1 * (array_sum(array_intersect_key($pricesNormalized, array_flip(Order::PRICE_POSITIVE_COLUMNS)))
            - array_sum(array_intersect_key($pricesNormalized, array_flip(Order::PRICE_NEGATIVE_COLUMNS))));

        $this->actingAs($admin);
        $response = $this->post(
            route('admin.orders.post-valuation', $vehicle->order),
            [
                'company_id' => $company->getKey(),
                'status_id' => $status->getKey(),
                ...$prices,
            ]
        );

        $response->assertRedirect();
        $response->assertSessionHas('success');
        $this->assertDatabaseHas(
            $vehicle->order,
            array_merge(
                [
                    'id' => $vehicle->order->getKey(),
                    'status_id' => $status->getKey(),
                    ...$pricesNormalized,
                ],
            )
        );
        $this->assertDatabaseHas(
            new UserBalance(),
            [
                'user_id' => $vehicle->user_id,
                'balance' => $balance,
                'company_id' => $company->getKey(),
                'type' => TransactionalType::TRANSACTION->value,
            ]
        );
        $this->assertSame($balance, $vehicle->user->account_balance);
    }
}
