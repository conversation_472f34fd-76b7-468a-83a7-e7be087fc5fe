<?php

declare(strict_types=1);

namespace Tests\Integration\Controllers;

use App\Models\User;
use App\Models\UserBusinessCard;
use App\Project\Storage\CloudStorage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidFactoryInterface;
use Tests\Integration\IntegrationTestCase;

class BusinessCardControllerTest extends IntegrationTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        Storage::fake(config('filesystems.cloud'));
    }

    public function testStore(): void
    {
        $user = User::factory()->create();
        $desktopFilePath = 'testing/user_business_card/desktop_image/';
        $desktopFileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $mobileFilePath = 'testing/user_business_card/mobile_image/';
        $mobileFileName = '6494d8aa-9397-463a-aa92-86ce79da32bd';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($desktopFileName))
            ->once()
            ->getMock()
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($mobileFileName))
            ->once()
            ->getMock();

        Uuid::setFactory($factoryMock);

        $this->actingAs($user);
        $response = $this->put(route('business-card.store'), [
            'desktop_image' => UploadedFile::fake()->image('desktop.jpg'),
            'mobile_image' => UploadedFile::fake()->image('mobile.jpg'),
        ]);

        $response->assertSuccessful();
        $this->assertDatabaseHas(
            (new UserBusinessCard())->getTable(),
            [
                'user_id' => $user->getKey(),
                'desktop_image' => "{$desktopFileName}.jpg",
                'mobile_image' => "{$mobileFileName}.jpg",
            ]
        );
        Storage::disk(config('filesystems.cloud'))->assertExists("{$desktopFilePath}{$desktopFileName}.jpg");
        Storage::disk(config('filesystems.cloud'))->assertExists("{$mobileFilePath}{$mobileFileName}.jpg");
    }

    public function testStoreBusinessCardForExistingBusinessCard(): void
    {
        $oldDesktopFilePath = 'testing/user_business_card/desktop_image/';
        $oldDesktopFileName = '6494d8aa-9396-463a-aa92-86ce79da32bd';
        $desktopFilePath = 'testing/user_business_card/desktop_image/';
        $desktopFileName = '6494d8aa-9397-463a-aa92-86ce79da32be';
        $factoryMock = Mockery::mock(UuidFactoryInterface::class)
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($oldDesktopFileName))
            ->once()
            ->getMock()
            ->shouldReceive('uuid4')
            ->andReturn(Uuid::fromString($desktopFileName))
            ->once()
            ->getMock();
        Uuid::setFactory($factoryMock);
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image(name: 'test.jpg');
        UserBusinessCard::factory()->create([
            'user_id' => $user->getKey(),
            'desktop_image' => $this->app->make(CloudStorage::class)
                ->storeFile(
                    file: $file,
                    model: (new UserBusinessCard()),
                    attributeName: 'desktop_image',
                ),
            'mobile_image' => null,
        ]);

        $this->actingAs($user);
        $response = $this->put(route('business-card.store'), [
            'desktop_image' => UploadedFile::fake()->image('desktop.jpg'),
        ]);

        $response->assertSuccessful();
        $this->assertDatabaseHas(
            (new UserBusinessCard())->getTable(),
            [
                'user_id' => $user->getKey(),
                'desktop_image' => "{$desktopFileName}.jpg",
                'mobile_image' => null,
            ]
        );
        Storage::disk(config('filesystems.cloud'))->assertExists("{$desktopFilePath}{$desktopFileName}.jpg");
        Storage::disk(config('filesystems.cloud'))->assertMissing("{$oldDesktopFilePath}{$oldDesktopFileName}.jpg");
    }
}
