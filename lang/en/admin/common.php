<?php

return [
    'warnings' => [
        'file_replacement' => 'Note adding a file overwrites previously added files',
        'delete' => 'Are you sure you want to delete this item?',
    ],
    'alerts' => [
        'success' => 'Success!',
        'success_save' => 'Changes have been successfully saved',
        'ajax_error' => 'An error occurred while loading the data',
    ],
    'save' => 'Save',
    'reset' => 'Reset',
    'delete' => 'Delete',
    'current_files' => 'Current files:',
    'add_copy_of' => 'Add copy of :fieldDescription',
    'back_to_the_list' => 'Back to the list',
    'actions' => 'Actions',
    'select_option' => 'Select option',
    'export' => 'Export',
    'import' => 'Import',
    'all' => 'All',
    'yes' => 'Yes',
    'no' => 'No',
    'cancel' => 'Cancel',
    'valuation_form' => [
        'warning' => 'After a valuation has been made, the customer will receive a notification email and the validity period of :valuationValidTime days will be calculated. The validity period is only relevant for containers.',
        'price_points_discount' => 'Discount for points',
        'price' => 'To be paid',
        'promotion_code_id' => 'Promotion code',
        'promotion_code_id_choose_code' => 'Choose code',
        'message' => 'Comment (email) - optional',
        'notify' => 'Change order status to priced and send notification to user',
        'submit' => 'Save valuation and send info to customer',
        'company_id' => 'Company',
        'company_id_placeholder' => 'Choose company from the list',
    ],
    'invoice_form' => [
        'is_company' => 'Company',
        'company_name' => 'Company name',
        'name' => 'First and last name',
        'city' => 'City',
        'zip_code' => 'Zip code',
        'address' => 'Address',
        'phone' => 'Phone',
        'nip' => 'Tax ID',
        'item_name' => 'Invoice item name',
        'country_id' => 'Country',
    ],
    'messages_form' => [
        'messages' => 'Messages',
        'type_message' => 'Type message...',
        'send' => 'Send',
    ],
    'documents_form' => [
        'id_card' => 'ID card',
        'id_card_valid_until' => 'ID card expiration date',
        'terms' => 'Terms',
        'power_of_attorney' => 'Power of Attorney',
        'liability' => 'Liability',
        'declaration' => 'Declaration',
        'comment' => 'Note',
        'delete_document' => 'Delete document',
    ],
    'images_input' => [
        'download' => 'Download',
        'image_manager_input' => 'Select or drag :type',
    ],
];
