<?php

return [
    'index' => [
        'name' => 'Vehicles',
        'table' => [
            'columns' => [
                'id' => 'ID',
                'user' => 'User',
                'vehicle' => 'Vehicle',
                'vin_number' => 'VIN',
                'pickup_location' => 'Pickup from',
                'buyer_number' => 'Buyer number',
                'lot_number' => 'Lot Number',
                'payment_date' => 'Receipt after',
                'receipt_date' => 'Receipt by',
                'comment' => 'Notes',
                'created_at' => 'Date added',
                'actions' => 'Actions',
                'shipping_line' => 'Shipping line',
                'container_number' => 'Container number',
                'destination_agency' => 'Agency',
                'status' => 'Status',
                'seller_type' => 'Receipt from',
                'title_file' => 'Title File',
                'hazmat_document' => 'Hazmat',
                'parcels' => 'Parcels',
                'bill_of_landing' => 'BL',
                'bill_of_sale' => 'BS',
                'collection_date' => 'Collection date',
                'driver_company_name' => 'Company',
                'payments' => 'Payments',
                'transport_price' => 'Price',
                'images_collection' => 'Pickup photos',
                'title_status' => 'Title',
                'driver_comment' => 'Comments',
                'w9' => 'W9',
                'paid_at' => 'Paid',
                'delivery_location_name' => 'Delivery to',
                'eta' => 'ETA',
                'paid_status' => 'Paid',
                'released_status' => 'Released',
                'release_to' => 'Release to',
                'released_at' => 'Release date',
                'title_tracking' => 'Title tracking',
                'completed_at' => 'Completed date',
                'terminal_paid_at' => 'Shipment paid',
                'container_booking_number' => 'Booking Number',
                'vehicle_length' => 'Length',
                'company_name' => 'Dealer',
                'driver_email' => 'Driver email',
                'driver_name' => 'Driver name',
                'last_date' => 'Pickup date',
                'recipients_amount' => 'Number of pickups',
                'vehicle_type' => 'Vehicle type',
                'avg_price' => 'Avg price',
                'terminal' => 'Terminal',
                'phone_number' => 'Phone number',
                'images_for_agency' => 'Images',
                'keys' => 'Keys',
                'vehicle_damages' => 'Damages',
            ],
            'data' => [
                'no_vehicle_documents' => 'Lack of user completed documents',
                'title_file' => 'Title',
                'bill_of_sale' => 'Invoice',
                'bill_of_landing' => 'BL',
                'hazmat_document' => 'Hazmat',
                'yes' => 'Yes',
                'no' => 'No',
                'w9' => 'W9',
                'present' => 'Present',
                'absent' => 'Missing',
                'do_not_release' => 'DO NOT RELEASE',
            ],
            'additional' => [
                'sent_for_agency' => [
                    'parcels_help' => 'If you see packages, release them to the designated person, check the photos and release the vehicle as in the photos, if a key is present, the vehicle must be released with the key. In case you have any questions, please contact at:',
                ],
            ],
            'no_data' => 'No data',
        ],
        'filters' => [
            'title_status' => 'Title status',
            'images_from_terminal' => 'Terminal photos',
            'pickup_images' => 'Pickup photos',
            'drivers_paid' => 'Paid',
            'terminal_paid_at' => 'Paid',
            'paid' => 'Paid',
            'completed_status' => 'Completed status',
            'bill_of_landing' => 'Bill of Landing',
            'company' => 'Dealer',
            'options' => [
                'present' => 'Present',
                'absent' => 'Missing',
                'to_be_paid' => 'To be paid',
                'paid' => 'Paid',
                'uncompleted' => 'Uncompleted',
                'completed' => 'Completed',
            ],
            'vehicles_length_sum' => 'Total length of vehicles:',
            'inch' => 'in',
            'show_form' => 'Show form',
            'w9_status' => 'W9 Status',
            'status' => 'Status',
            'vehicle_damages' => [
                'status' => [
                    'none' => 'None',
                    'processing' => 'Processing',
                    'resolved' => 'Resolved',
                ],
            ],
        ],
        'forms' => [
            'company' => 'Company',
            'bill_of_sale' => 'Bill of sale',
            'bill_of_landing' => 'Bill of Lading',
            'title_file' => 'Title File',
            'mark_title_status' => 'Mark Title status for selected',
            'mark_selected' => 'Mark selected',
            'title_tracking_number' => 'Title tracking number',
            'created_at_from' => 'Created from',
            'created_at_to' => 'Created to',
            'title_tracking_number_confirm' => [
                'title' => 'Unpaid vehicles have been selected!',
                'confirmation' => 'Are you sure you want to assign a tracking number to them?',
            ],
            'options' => [
                'present' => 'Present',
                'absent' => 'Missing',
                'released' => 'Released',
                'to_be_paid' => 'To be paid',
                'paid' => 'Paid',
                'uncompleted' => 'Uncompleted',
                'completed' => 'Completed',
                'processing' => 'Processing',
                'resolved' => 'Resolved',
            ],
        ],
    ],
    'edit' => [
        'header' => [
            'copy' => 'Copy',
            'vin' => 'VIN',
            'editing_vehicle' => 'Editing vehicle with ID: :id',
            'go_back' => 'Return to list',
            'order_number' => 'Order number: :id',
            'deposit' => 'Deposit: :amount',
            'account_type' => 'Account type: :type',
            'vehicle_link' => 'Vehicle link',
            'auction_link' => ':auction link',
            'user_proof_of_payment_link' => 'Proof of payment',
            'user_proof_of_payment_missing' => 'Proof of payment missing',
            'track_container' => 'Track container',
            'dealer_platform_link' => 'Dealer platform',
        ],
        'tabs' => [
            'order' => 'Order',
            'shipment' => 'Shipment',
            'valuation' => 'Valuation',
            'release' => 'Release',
            'invoice' => 'Invoice data',
            'driver' => 'Driver',
            'messages' => 'Messages',
            'documents' => 'Documents',
            'tracking' => 'Tracking',
            'parcels' => 'Parcels',
            'drivers' => 'List of drivers',
        ],
        'order_form' => [
            'select_placeholder' => 'Select from list',
            'status' => 'Status',
            'terminal' => 'Terminal',
            'vehicle_length' => 'Vehicle length',
            'vehicle_length_help' => 'Vehicle length in inches without decimal point',
            'vehicle_length_google' => 'Check vehicle length in Google',
            'collection_date' => 'Collection Date',
            'user_pay_for_vehicle' => 'I pay auction (self-pay)',
            'carrierwise_pay_for_vehicle' => 'I pay AuctionCars (AuctionCars pays)',
            'type_of_service' => 'Select type of service',
            'vehicle_price' => 'Amount to pay',
            'bid_to_price' => 'Bid to',
            'buy_now_price' => 'Buy now',
            'bought_for' => 'I bought for',
            'auction_url' => 'Link to vehicle',
            'auction_url_placeholder' => 'Enter online auction url',
            'vehicle_description' => 'Vehicle details',
            'vehicle_description_placeholder' => 'Enter year / model / brand of vehicle',
            'vin_number' => 'Enter VIN number',
            'vin_number_placeholder' => 'Enter VIN number',
            'no_vin' => 'No VIN number',
            'no_vin_help' => 'A vehicle without a VIN number may be cut into pieces and shipped',
            'vehicle_type' => 'Vehicle type',
            'lot_number' => 'Lot Number',
            'lot_number_placeholder' => 'Enter Lot Number',
            'buyer_number' => 'Buyer Number',
            'buyer_number_placeholder' => 'Enter Buyer Number',
            'gate_pass_pin' => 'Gate Pass Pin',
            'gate_pass_pin_placeholder' => 'Enter Gate Pass Pin',
            'payment_date' => 'When the vehicle will be ready for pick up',
            'payment_finished' => 'Seller has not yet received payment for vehicle purchase',
            'payment_finished_help' => 'Let us know when the vehicle is ready for pick up',
            'receipt_date' => 'Receive vehicle by',
            'no_receipt_date' => 'Not applicable',
            'seller_type' => 'Collection from',
            'vehicle_auction_location_id' => 'Location',
            'vehicle_auction_location_id_placeholder' => 'Select location from list',
            'vehicle_auction_location_company' => 'Company name',
            'vehicle_auction_location_company_placeholder' => 'Enter company name',
            'vehicle_auction_location_seller_name' => 'Seller name',
            'vehicle_auction_location_seller_name_placeholder' => 'Enter seller name',
            'vehicle_auction_location_address' => 'Collection address',
            'vehicle_auction_location_address_placeholder' => 'Enter vehicle pickup address',
            'vehicle_auction_location_phone' => 'Phone',
            'vehicle_auction_location_phone_placeholder' => 'Enter vendor phone number',
            'vehicle_auction_location_email' => 'Email',
            'vehicle_auction_location_email_placeholder' => 'Enter seller\'s Email address',
            'vehicle_delivery_location_id' => 'Deliver vehicle to',
            'vehicle_delivery_location_id_do_not_ship' => 'Do not ship',
            'marine_insurance' => 'Shipping Protection Plan',
            'marine_insurance_with' => 'I am ordering protection plan',
            'marine_insurance_without' => 'Cancel protection plan',
            'insurance_amount' => 'Protection plan value',
            'is_electric_or_hybrid' => 'Hybrid or Electric Vehicle',
            'is_electric_or_hybrid_is' => 'Hybrid or Electric',
            'is_electric_or_hybrid_is_not' => 'Conventional',
            'is_premium_loading' => 'Vehicle loading',
            'is_premium_loading_is' => 'Premium loading',
            'is_premium_loading_is_not' => 'Standard loading',
            'garbage_removal' => 'Garbage removal (additional cost $ :price)',
            'yes' => 'Yes',
            'no' => 'No',
            'moisture_absorber' => 'Moisture absorber (additional cost $ :price)',
            'comment' => 'Comments',
            'comment_placeholder' => 'You may provide additional information about the vehicle or transaction',
            'admin_comment' => 'Admin Comment',
            'insurance_estimated_amount' => 'Estimated amount to be paid:',
            'transfer_question' => 'Are you sure you want to transfer this vehicle to another user?',
            'user_id' => 'Transfer to user',
            'send_title_to_customs' => 'Show a copy of title to customs agency',
            'send_title_to_customs_label' => 'Send title to customs agency',
        ],
        'shipment_form' => [
            'select_placeholder' => 'Select from the list',
            'status' => 'Status',
            'vehicle_shipping_line_id' => 'Shipping Line',
            'container_number' => 'Container Number',
            'container_number_placeholder' => 'Enter the container number',
            'container_booking_number' => 'Container Booking Number',
            'container_booking_number_placeholder' => 'Enter the container\'s booking number',
            'eta' => 'ETA',
            'vehicle_destination_agency_id' => 'Destination Agency',
            'keys' => 'Keys',
            'title_status' => 'Title',
            'present' => 'Present',
            'absent' => 'Missing',
            'images_collection' => 'Collection photos',
            'images_terminal' => 'Photos from the terminal',
            'images_shipping' => 'Shipping photos',
            'bill_of_sale' => 'Bill of Sale',
            'title_file' => 'Title',
            'bill_of_landing' => 'Bill of Landing',
            'hazmat_document' => 'Hazmat document',
            'images_user' => 'User photos',
        ],
        'valuation_form' => [
            'city_id' => 'City',
            'city_id_placeholder' => 'Choose city',
            'vehicle_type' => 'Vehicle type',
            'port' => 'Shipping from port',
            'port_placeholder' => 'Select a port',
            'price' => 'Price:',
            'broker_fee' => 'Broker fee:',
            'status_id' => 'Status',
            'price_purchase' => 'Vehicles Sales',
            'price_purchase_negative' => 'Vehicles Sales (-)',
            'price_payment' => 'Vehicles Payment',
            'price_ocean_shipping' => 'Ocean shipping',
            'price_landing_shipping' => 'Landing shipping',
            'price_cleaning' => 'Cleaning Fee',
            'price_tax' => 'Transaction Fee',
            'price_insurance' => 'Shipping protection plan',
            'price_garbage_removal' => 'Garbage Removal',
            'price_moisture_absorber' => 'Moisture absorber',
            'price_commission' => 'Storage Fee',
            'price_administration_fee' => 'Administration Fee',
            'price_discount' => 'Discount',
            'price_hidden' => 'Purchase cost (hidden)',
        ],
        'release_form' => [
            'vehicle_details' => 'Vehicle details:',
            'name' => 'First and last name',
            'name_placeholder' => 'Enter first and last name',
            'phone' => 'Phone',
            'phone_placeholder' => 'Enter phone number',
            'email' => 'Email',
            'email_placeholder' => 'Enter email address',
            'released' => 'Released',
            'released_confirm' => 'Vehicle is released',
        ],
        'driver_form' => [
            'driver_id' => 'Driver',
            'transport_price' => 'Transport price',
            'paid_at' => 'Date of payment',
            'comment' => 'Comments',
            'delete' => 'Delete assigned driver',
            'vehicle_damages' => 'Damages',
            'yes' => 'Yes',
            'no' => 'No',
        ],
        'tracking_form' => [
            'title_tracking_number' => 'Title Tracking Number',
        ],
        'parcels_form' => [
            'currently_assigned' => 'Currently assigned parcels to the vehicle:',
            'no_parcels_currently_assigned' => 'No parcels currently assigned to the vehicle',
            'assigned_parcels' => 'Assigned parcels',
            'change_assignment' => 'Here you can change assigned parcels to a vehicle',
        ],
    ],
];
