<?php

return [
    'how_to_assign_a_package_to_the_warehouse_by_yourself' => 'How to assign a package to the warehouse by yourself?',
    'pricing' => 'Pricing',
    'the_shipping_fee_for_the_package_is' => 'The shipping fee for the package is :price',
    'compare_delivery_terms_and_prices_between_warehouses' => 'Compare delivery terms and prices between warehouses',
    'check' => 'Check',
    'terms' => 'Terms',
    'send_a_package_to_the_warehouse' => 'Send a package to the warehouse',
    'by_writing_ocean_drive_and_the_customer_number_on_the_package' => 'By writing OceanDrive and the customer number on the package',
    'by_providing_name_surname_and_customer_number_on_the_package' => 'By providing name, surname and customer number on the package',
    'add_the_package_to_the_warehouse' => 'Add the package to the warehouse',
    'after_receiving_the_tracking_number_from_the_sender' => 'After receiving the tracking number from the sender',
    'add' => 'Add',
    'packages_added_to_the_warehouse' => 'Packages added to the warehouse',
    'add_it_to_the_vehicle_that_is_in_the_warehouse' => 'Add it to the vehicle that is in :name',
    'can_be_assigned_for_shipping' => 'Can be assigned for shipping',
    'send' => 'Send',
    'delivered_packages_to_warehouse_stock' => 'We add packages in the client panel',
    'check_in_the_client_panel' => 'After receiving the shipments',
    'add_all_packages_to_the_vehicle' => 'After adding all packages',
    'no_possibility_to_add_packages' => 'Add packages to the selected vehicle',
];
