<?php

return [
    'shipment' => [
        'parcels' => [
            'sender' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'state' => 'State',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'Company',
                'company_name' => 'Company name',
            ],
            'recipient' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'Company',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
                'country' => 'Country',
            ],
            'form' => [
                'city' => 'City',
                'commercial' => 'Commercial',
                'customs_value' => 'Declared value for the Customs Office',
                'customs_contains' => 'Declared content for the Customs Office',
                'shipping_type' => 'Type of transport',
                'comment_user' => 'Additional comments',
                'discount_code' => 'Discount code',
                'insurance' => 'Shipping insurance',
            ],
        ],
        'vehicles' => [
            'recipient' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'I want to enter my company details',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
            ],
            'form' => [
                'city' => 'City',
                'shipping_type' => 'Type of transport',
                'comment_user' => 'Additional comments',
                'discount_code' => 'Discount code',
            ],
        ],
    ],
    'shopping' => [
        'parcels' => [
            'shop' => [
                'login' => 'Login',
                'password' => 'Password',
                'url' => 'Store URL',
                'amount' => 'Quantity',
                'price' => 'Price ($)',
                'product_url' => 'Product URL',
                'attributes' => 'Attributes (color, size, model etc.)',
                'prepaid_card_type' => 'Prepaid card type',
                'prepaid_card_amount' => 'Prepaid card top-up amount',
                'gift_card_amount' => 'Gift card with value',
            ],
            'purchaser' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'I want to enter my company details',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
            ],
            'scenarios' => [
                'online_cart' => 'Shopping cart in store',
                'links' => 'Purchase in a link shop',
                'gift_card' => 'Purchase a gift card',
                'prepaid_card' => 'Buying a Predpaid card',
                'unknown' => 'unknown',
            ],
        ],
        'vehicles' => [
            'shop' => [
                'product_url' => 'Link to auction or website',
                'price' => 'Price ($)',
                'desc' => 'Description',
            ],
            'purchaser' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'I want to enter my company details',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
            ],
        ],
    ],
    'shipping' => [
        'parcels' => [
            'from' => [
                'type' => 'Location type',
                'postcode' => 'Code',
                'city' => 'City',
                'state' => 'State',
            ],
            'items' => [
                'item_type' => 'Shipping type',
                'dangerous' => 'Dangerous material',
                'length' => 'Length in inches',
                'width' => 'Width in inches',
                'height' => 'Height in inches',
                'weight' => 'Weight in pounds',
                'url' => 'Link to auction or website',
                'files' => 'Product photo',
                'desc' => 'Additional description',
            ],
            'shipper' => [
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'contact_person' => 'Name and surname of the contact person',
                'phone' => 'Phone',
                'phone_prefix' => 'Internal',
                'email' => 'Email',
                'hours_from' => 'Pickup hours from',
                'hours_to' => 'Pickup hours to',
                'days_from' => 'Days of the week from',
                'days_to' => 'Days of the week to',
                'articles_desc' => 'Article descriptions',
                'comment' => 'Additional information',
            ],
            'purchaser' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'I want to enter my company details',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
            ],
        ],
    ],
    'pickup' => [
        'vehicles' => [
            'shipper' => [
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'contact_person' => 'Name and surname of the contact person',
                'phone' => 'Phone',
                'phone_prefix' => 'Internal',
                'email' => 'Email',
                'hours_from' => 'Pickup hours from',
                'hours_to' => 'Pickup hours to',
                'days_from' => 'Days of the week from',
                'days_to' => 'Days of the week to',
                'comment' => 'Additional information',
            ],
            'vehicles' => [
                'type' => 'Vehicle type',
                'loading_possibility' => 'Loading possibility',
                'year' => 'Year',
                'brand' => 'Brand',
                'model' => 'Model',
                'efficiency' => 'Roadworthy vehicle',
                'url' => 'Link to auction or website',
                'comment' => 'Additional Notes',
                'files' => 'Attachments',
            ],
            'purchaser' => [
                'first_name' => 'Name',
                'last_name' => 'Surname',
                'address' => 'Address',
                'city' => 'City',
                'postcode' => 'Code',
                'phone' => 'Phone',
                'is_company' => 'I want to enter my company details',
                'company_name' => 'Company name',
                'company_address' => 'Company address',
                'company_city' => 'Company city',
                'company_postcode' => 'Company code',
                'nip' => 'NIP',
                'REGON' => 'REGON',
            ],
        ],
    ],
    'types' => [
        'parcels' => 'Parcels',
        'old-vehicles' => 'Vehicles (old version)',
        'containers' => 'Containers',
        'vehicles' => 'Vehicles',
        'vehicle-reports' => 'Vehicle History Report',
        'other' => 'Other',
        'deposit' => 'Deposit',
    ],
    'show' => [
        'go_back_to_list' => 'Go back to the list',
        'order_details' => 'Order details',
        'type' => 'Type',
        'order_number' => 'Order number',
        'length_in_inches' => 'Length in inches',
        'width_in_inches' => 'Width in inches',
        'height_in_inches' => 'Height in inches',
        'weight_in_pounds' => 'Weight in pounds',
        'number_of_packages' => 'Number of packages',
        'shipping_cost' => 'Shipping cost',
        'service' => 'Service',
        'insurance' => 'Insurance',
        'purchase_cost' => 'Purchase cost',
        'transport_cost' => 'Transport cost',
        'tax' => 'Tax',
        'commission' => 'Commission',
        'to_pay' => 'To pay',
        'status' => 'Status',
        'date_added' => 'Date added',
        'print_order' => 'Print order',
    ],
    'tooltips' => [
        'is_paid' => 'Order has already been paid.',
        'fulfill_transactional_after_date' => 'The possibility of payment is available only for orders added after 24.05.2019.',
        'without_price' => 'The order has not yet been priced. Please be patient.',
        'has_previously_deadlined_payment' => 'You should pay the oldest order first.',
        'no_founds' => 'Insufficient funds on the account.',
        'no_founds_for_company' => 'Insufficient funds on the company account.',
    ],
    'alerts' => [
        'your_order_has_been_accepted' => 'Your order has been accepted.',
        'your_order_has_been_deleted' => 'Your order has been deleted.',
        'you_cannot_delete_order' => 'You cannot delete an order that is in progress. Please contact the administrator.',
    ],
    'new_order' => 'New order',
    'order_details' => 'Order details #:id',
    'the_user_deleted_the_job' => 'The user deleted the job',
    'order_the_order_has_been_deleted_by_the_user' => 'Order :id has been deleted by the user',
    'select_the_warehouse_to_which_you_want_to_order_the_shipment' => 'Select the warehouse to which you want to order the shipment',
    'pay_order' => 'Pay order',
];
