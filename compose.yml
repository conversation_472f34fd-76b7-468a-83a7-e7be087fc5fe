networks:
  frontend:
  backend:

volumes:
  mysql-data:
  redis-data:
  redis_socket:
  mailpit-data:

secrets:
  host_ssh_key:
    file: ~/.ssh/id_rsa

services:
  nginx:
    build:
      context: ./docker/nginx
    volumes:
      - ./:/var/www/auction:cached
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:cached
      - ./docker/nginx/sites/:/etc/nginx/sites-available:cached
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d:cached
      - ./docker/certs/auction.test.pem:/etc/ssl/certs/auction.test.pem:cached
      - ./docker/certs/auction.test-key.pem:/etc/ssl/private/auction.test-key.pem:cached
    networks:
      - frontend
      - backend
    ports:
      - "80:80"
      - "443:443"
  mysql:
    image: mysql:8.3
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: R00tP@ssword67!
      MYSQL_USER: dev
      MYSQL_PASSWORD: c@rrierWiSe123!
    command:
      - --default-authentication-plugin=mysql_native_password
      - --explicit_defaults_for_timestamp
      - --max_allowed_packet=100M
    volumes:
      - ./docker/mysql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d:cached
      - mysql-data:/var/lib/mysql
    networks:
      - backend
    ports:
      - "3306:3306"
  redis:
    image: redis:7-alpine
    volumes:
      - redis-data:/data
      - redis_socket:/var/run/redis
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - backend
    command: /bin/sh -c "mkdir -p /var/run/redis && chmod 777 /var/run/redis && redis-server /usr/local/etc/redis/redis.conf"
  mailpit:
    image: axllent/mailpit
    networks:
      - backend
    ports:
      - "8025:8025"
    volumes:
      - mailpit-data:/data
    environment:
      TZ: Europe/Warsaw
      MP_DATA_FILE: /data/mailpit.db
  auction:
    build:
      context: ./
      args:
        - START_SUPERVISOR=true
    volumes:
      - ./:/var/www/auction:cached
      - redis_socket:/var/run/redis
      - ./docker/certs/auction.test.pem:/etc/ssl/certs/auction.test.pem:cached
      - ./docker/certs/auction.test-key.pem:/etc/ssl/private/auction.test-key.pem:cached
    depends_on:
      - redis
    env_file:
      - ./.env.docker
    networks:
      - frontend
      - backend
    secrets:
      - host_ssh_key
    ports:
      - "5173:5173"
