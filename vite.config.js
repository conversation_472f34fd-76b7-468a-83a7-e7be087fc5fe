import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue2';
import path from 'path';
import inject from '@rollup/plugin-inject';
import fs from 'fs';

const sslKeyPath = '/etc/ssl/private/auction.test-key.pem';
const sslCertPath = '/etc/ssl/certs/auction.test.pem';

let httpsConfig;
if (fs.existsSync(sslKeyPath) && fs.existsSync(sslCertPath)) {
  httpsConfig = {
    key: fs.readFileSync(sslKeyPath),
    cert: fs.readFileSync(sslCertPath),
  };
}

export default defineConfig({
  root: './',
  plugins: [
    vue(),
    inject({
      include: ['**/*.js', '**/*.vue'],
      $: 'jquery',
      jQuery: 'jquery',
    }),
    laravel({
      input: [
        'resources/js/new/web.js',
        'resources/sass/new/web.scss',
        'resources/js/app.js',
        'resources/sass/app.scss',
        'resources/js/validation.js',
        'resources/js/vehicle-form.js',
        'public/vendor/jsvalidation/js/jsvalidation.js',
        'node_modules/jquery-steps/build/jquery.steps.min.js',
        'resources/admin/js/admin.js',
        'resources/admin/sass/admin.scss',
      ],
      refresh: true,
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        quietDeps: true,
        silenceDeprecations: ['import', 'mixed-decls', 'color-functions', 'global-builtin']
      }
    }
  },
  build: {
    // Fix select2 on build
    commonjsOptions: { transformMixedEsModules: true },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'resources/js'),
      '@admin': path.resolve(__dirname, 'resources/admin/js'),
      // Load the full version of Vue
      vue: 'vue/dist/vue.js',
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    https: httpsConfig,
    hmr: {
      host: 'auction.test',
    },
  },
});
