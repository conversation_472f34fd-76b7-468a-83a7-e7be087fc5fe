SHELL=sh
.DEFAULT_GOAL := default

APP_NAME=auction
COMPOSE_FILE = docker compose -f compose.yml
DOCKER_RUN = ${COMPOSE_FILE} exec

default: ## Build and install whole project
	${MAKE} certs
	${MAKE} vhosts
	${MAKE} build
	$(MAKE) install
	${MAKE} build-fe

help:
	@printf "\n%s\n________________________________________________\n" $(shell basename ${APP_NAME})
	@printf "\n\033[32mAvailable commands:\n\033[0m"
	@fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep  | sed -e 's/\\$$//' | sed -e 's/##//' | awk 'BEGIN {FS = ":"}; {printf "\033[33m%s:\033[0m%s\n", $$1, $$2}'

certs:
	mkcert -install
	mkcert -cert-file docker/certs/auction.test.pem -key-file docker/certs/auction.test-key.pem auction.test

vhosts:
	if ! echo "$$(uname -s)" | grep -q "MINGW"; then \
		if grep -q "auction.test" /etc/hosts; then \
			echo "Virtual host exists"; \
		else echo '127.0.0.1	auction.test' | sudo tee -a /etc/hosts; \
		fi; \
	fi

build: ## Build/Rebuild docker images
	${COMPOSE_FILE} build --no-cache --pull --parallel
	${COMPOSE_FILE} up -d --force-recreate

start: ## Start docker containers
	${COMPOSE_FILE} up -d

stop: ## Stop docker containers
	${COMPOSE_FILE} stop

restart: ## Restart docker containers
	${COMPOSE_FILE} restart

remove: ## Remove docker containers
	${COMPOSE_FILE} rm -f --stop ${APP_NAME}

shell: ## Run container shell
	${DOCKER_RUN} ${APP_NAME} sh

install: ## Install dependencies
	${DOCKER_RUN} ${APP_NAME} composer install
	${DOCKER_RUN} ${APP_NAME} npm install

test: ## Run tests
	${DOCKER_RUN} ${APP_NAME} php artisan test --parallel

fe-lint: ## Run eslint and stylelint
	${DOCKER_RUN} ${APP_NAME} npm run lint

fe-lint-fix: ## Run eslint, stylelint and prettier with fix
	${DOCKER_RUN} ${APP_NAME} npm run lint:fix

lint: ## Run pint, static analysis, eslint and prettier
	${MAKE} pint
	${MAKE} analyze
	${MAKE} fe-lint

analyze: ## Run static analysis
	${DOCKER_RUN} ${APP_NAME} ./vendor/bin/phpstan analyse --memory-limit=512M -c phpstan.neon

pint: ## Run static analysis
	${DOCKER_RUN} ${APP_NAME} ./vendor/bin/pint

ps:	## List status of containers
	@${COMPOSE_FILE} ps

logs: ## Stream logs from containers
	@${COMPOSE_FILE} logs -f --tail 30 -t

restart-supervisor: ## Restart supervisor
	${DOCKER_RUN} ${APP_NAME} supervisorctl restart all

watch: ## Run npm watch
	${DOCKER_RUN} ${APP_NAME} npm run dev

build-fe: ## Build assets
	${DOCKER_RUN} ${APP_NAME} npm run build

ide-helper:	## Generate ide-helper files
	${DOCKER_RUN} ${APP_NAME} php artisan ide-helper:generate
	${DOCKER_RUN} ${APP_NAME} php artisan ide-helper:meta
	${DOCKER_RUN} ${APP_NAME} php artisan ide-helper:models --write-mixin
