<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AjaxController;
use App\Http\Controllers\Admin\CompaniesController;
use App\Http\Controllers\Admin\CostsController;
use App\Http\Controllers\Admin\DepositsController;
use App\Http\Controllers\Admin\DriversController;
use App\Http\Controllers\Admin\InvoicesController;
use App\Http\Controllers\Admin\MessagesController;
use App\Http\Controllers\Admin\NoticesController;
use App\Http\Controllers\Admin\OrdersController;
use App\Http\Controllers\Admin\ParcelsController;
use App\Http\Controllers\Admin\PaymentsController;
use App\Http\Controllers\Admin\PointsController;
use App\Http\Controllers\Admin\PromotionCodesController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\ShopsController;
use App\Http\Controllers\Admin\TasksController;
use App\Http\Controllers\Admin\UserCommissionsController;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\UserVehiclesStatisticsController;
use App\Http\Controllers\Admin\VehicleCalculatorsController;
use App\Http\Controllers\Admin\VehicleDeliveryLocationInformationController;
use App\Http\Controllers\Admin\VehicleDriversController;
use App\Http\Controllers\Admin\VehicleParcelsController;
use App\Http\Controllers\Admin\VehiclePriceListController;
use App\Http\Controllers\Admin\VehicleReportsController;
use App\Http\Controllers\Admin\VehiclesController;
use App\Http\Controllers\Admin\VehiclesExportController;
use App\Http\Controllers\Admin\VehicleTransportPriceCalculationController;
use App\Http\Controllers\Admin\VehicleUserDocumentsController;

Route::get('/', [AdminController::class, 'index'])->name('home');

Route::get('/set-locale/{language}', [AdminController::class, 'setLocale'])->name('set-locale');

Route::prefix('users')
    ->name('users.')
    ->middleware('can:users.has-access')
    ->group(function () {
        Route::post('datatable', [UsersController::class, 'datatable'])->name('datatable');
        Route::get('{users}/tab/{tab?}', [UsersController::class, 'show'])->name('show');
        Route::get('log-in/{userId}', [UsersController::class, 'logIn'])->name('log-in');
        Route::get('{user}/documents/destroy/{type}', [UsersController::class, 'destroyDocument'])->name('documents.destroy');
        Route::get('vehicles-statistics', [UserVehiclesStatisticsController::class, 'index'])->name('vehicles-statistics');
        Route::post('vehicles-statistics/datatable', [UserVehiclesStatisticsController::class, 'datatable'])->name('vehicles-statistics.datatable');
        Route::get('commissions', [UserCommissionsController::class, 'index'])->name('commissions');
        Route::post('commissions/datatable', [UserCommissionsController::class, 'datatable'])->name('commissions.datatable');
        Route::resource('', UsersController::class, ['except' => ['show']])->parameters(['' => 'user']);
    });

Route::prefix('messages')
    ->name('messages.')
    ->middleware('can:messages.has-access')
    ->group(function () {
        Route::post('datatable', [MessagesController::class, 'datatable'])->name('datatable');
        Route::post('mark-archived', [MessagesController::class, 'markArchived'])->name('mark-archived');
        Route::put('assign-workers', [MessagesController::class, 'assignWorkers'])->name('assign-workers');
        Route::get('conversation/{messageableId}', [MessagesController::class, 'conversationByMessageableId'])->name('conversation');
        Route::resource('', MessagesController::class)->parameters(['' => 'message']);
    });

Route::prefix('shops')
    ->name('shops.')
    ->middleware('can:shops.has-access')
    ->group(function () {
        Route::post('datatable', [ShopsController::class, 'datatable'])->name('datatable');
        Route::resource('', ShopsController::class)->parameters(['' => 'shop']);
    });

Route::prefix('parcels')
    ->name('parcels.')
    ->middleware('can:parcels.has-access')
    ->group(function () {
        Route::post('datatable', [ParcelsController::class, 'datatable'])->name('datatable');
        Route::resource('', ParcelsController::class)->parameters(['' => 'parcel']);
    });

Route::middleware('can:vehicles.has-access')
    ->group(function () {
        Route::prefix('vehicles')
            ->name('vehicles.')
            ->group(function () {
                Route::resource('.parcels', VehicleParcelsController::class)
                    ->parameters(['' => 'vehicle'])
                    ->only('index', 'store')
                    ->shallow();

                Route::post('drivers/{vehicle}/datatable', [VehicleDriversController::class, 'datatable'])->name('drivers.datatable');
                Route::resource('.drivers', VehicleDriversController::class)
                    ->parameters(['drivers' => 'vehicle'])
                    ->only('show', 'update', 'edit')
                    ->shallow();
                Route::put('vehicle-drivers/update-many', [VehicleDriversController::class, 'updateMany'])->name('vehicle_drivers.update-many');

                Route::resource('.user-documents', VehicleUserDocumentsController::class)
                    ->parameters(['user-documents' => 'vehicle'])
                    ->only('show', 'update')
                    ->middleware('can:vehicle-user-documents.has-access')
                    ->shallow();

                Route::put('mark-vehicles', [VehiclesController::class, 'markVehicles'])->name('mark-vehicles');
                Route::put('update-many', [VehiclesController::class, 'updateMany'])->name('update-many');
                Route::get('export', [VehiclesExportController::class, 'export'])
                    ->middleware('can:vehicles.can-export')
                    ->name('export');
                Route::get('export-customs-agency/{completed?}', [VehiclesExportController::class, 'exportForCustomsAgency'])
                    ->middleware('can:vehicles.can-export-for-customs-agency')
                    ->name('export-for-customs-agency');
                Route::post('datatable/{tab}', [VehiclesController::class, 'datatable'])->name('datatable');

                Route::prefix('{vehicle}')->group(function () {
                    Route::put('transfer', [VehiclesController::class, 'transfer'])->name('transfer')->middleware('can:vehicles.can-transfer,vehicle');
                    Route::get('messages', [VehiclesController::class, 'messages'])->name('messages');
                    Route::get('shipment', [VehiclesController::class, 'shipment'])->name('shipment');
                    Route::get('order', [VehiclesController::class, 'order'])->name('order');
                    Route::get('release', [VehiclesController::class, 'release'])->name('release');
                    Route::get('invoice', [VehiclesController::class, 'invoice'])->name('invoice');
                    Route::get('prices/{pickUpCity}', [VehicleTransportPriceCalculationController::class, 'prices'])->name('prices');
                    Route::get('tracking', [VehiclesController::class, 'tracking'])->name('tracking.show');
                    Route::get('images-for-agency', [VehiclesController::class, 'imagesForAgency'])->name('images-for-agency')->middleware('can:vehicles.can-view-files,vehicle');
                });

                Route::resource('', VehiclesController::class)->parameters(['' => 'vehicle'])->except('show');
            });
    });

Route::middleware('can:vehicle-calculators.has-access')
    ->group(function () {
        Route::resource(
            'vehicle-calculators/delivery-location-information',
            VehicleDeliveryLocationInformationController::class
        );

        Route::prefix('vehicle-calculators')
            ->as('vehicle-calculators.')
            ->group(function () {
                Route::get('price-list/{accountType?}', [VehiclePriceListController::class, 'index'])->name('price-list.index');
                Route::get('price-list/export/{accountType?}', [VehiclePriceListController::class, 'export'])->name('price-list.export');
                Route::post('datatable/{type}', [VehicleCalculatorsController::class, 'datatable'])->name('datatable');

                Route::prefix('delivery/{type?}')->group(function () {
                    Route::get('/', [VehicleCalculatorsController::class, 'deliveryPrices'])->name('delivery-prices');
                    Route::put('/', [VehicleCalculatorsController::class, 'updateDeliveryPrices'])->name('update-delivery-prices');
                });

                Route::prefix('{type}/{city}')->group(function () {
                    Route::get('/', [VehicleCalculatorsController::class, 'edit'])->name('edit');
                    Route::put('/', [VehicleCalculatorsController::class, 'update'])->name('update');
                    Route::delete('/', [VehicleCalculatorsController::class, 'destroy'])->name('destroy');
                });

                Route::get('{type?}', [VehicleCalculatorsController::class, 'index'])->name('index');
            });
    });

Route::prefix('orders')
    ->name('orders.')
    ->middleware('can:orders.has-access')
    ->group(function () {
        Route::post('datatable', [OrdersController::class, 'datatable'])->name('datatable');
        Route::post('valuation/{order}', [OrdersController::class, 'postValuation'])->name('post-valuation');
        Route::post('{id}/set-important-status', [OrdersController::class, 'setImportantStatus'])->name('set-important-status');
        Route::get('export', [OrdersController::class, 'export'])->middleware('can:orders.can-generate-report')->name('export');
        Route::get('{id}/invoice', [OrdersController::class, 'invoice'])->name('invoice');
        Route::resource('', OrdersController::class)->parameters(['' => 'order'])->except('show');
    });

Route::prefix('tasks')
    ->name('tasks.')
    ->middleware('can:tasks.has-access')
    ->group(function () {
        Route::get('indexTasks', [TasksController::class, 'indexTasks'])->name('index-tasks');
        Route::post('uploadImage/{id}', [TasksController::class, 'uploadImage'])->name('upload-image');
        Route::post('storeOrder', [TasksController::class, 'storeOrder'])->name('store-order');
        Route::post('markCompleted/{id}', [TasksController::class, 'markCompleted'])->name('mark-completed');
        Route::resource('', TasksController::class)->parameters(['' => 'task']);
    });

Route::prefix('payments')
    ->name('payments.')
    ->middleware('can:transactions.has-access')
    ->group(function () {
        Route::post('datatableUsers', [PaymentsController::class, 'datatableUsers'])->name('datatable-users');
        Route::post('datatableOrders', [PaymentsController::class, 'datatableOrders'])->name('datatable-orders');
        Route::post('datatable', [PaymentsController::class, 'datatable'])->name('datatable');
        Route::get('orders', [PaymentsController::class, 'orders'])->name('orders');
        Route::get('users', [PaymentsController::class, 'users'])->name('users');
        Route::get('/pay/{orderId}', [PaymentsController::class, 'orderPay'])->name('pay');
        Route::get('reports', [PaymentsController::class, 'reports'])->name('reports');
        Route::resource('', PaymentsController::class)->parameters(['' => 'transaction']);
    });

Route::prefix('deposits')
    ->name('deposits.')
    ->middleware('can:deposits.has-access')
    ->group(function () {
        Route::post('datatable', [DepositsController::class, 'datatable'])->name('datatable');
        Route::resource('', DepositsController::class)->parameters(['' => 'deposit']);
    });

Route::prefix('points')
    ->name('points.')
    ->middleware('can:points.has-access')
    ->group(function () {
        Route::post('datatable', [PointsController::class, 'datatable'])->name('datatable');
        Route::resource('', PointsController::class)->parameters(['' => 'point']);
    });

Route::prefix('invoices')
    ->name('invoices.')
    ->middleware('can:invoices.has-access')
    ->group(function () {
        Route::post('{orderId}', [InvoicesController::class, 'update'])->name('update');
    });

Route::prefix('promotion-codes')
    ->name('promotion-codes.')
    ->middleware('can:promotion-codes.has-access')
    ->group(function () {
        Route::post('datatable', [PromotionCodesController::class, 'datatable'])->name('datatable');
        Route::resource('', PromotionCodesController::class)->parameters(['' => 'promotionCode']);
    });

Route::prefix('costs')
    ->name('costs.')
    ->middleware('can:costs.has-access')
    ->group(function () {
        Route::post('datatable', [CostsController::class, 'datatable'])->name('datatable');
        Route::get('export', [CostsController::class, 'export'])->name('export')->middleware('can:costs.can-export');
        Route::post('import', [CostsController::class, 'import'])->name('import')->middleware('can:costs.can-import');
        Route::put('update-many', [CostsController::class, 'updateMany'])->name('update-many')->middleware('can:costs.can-edit');
        Route::resource('', CostsController::class)->parameters(['' => 'cost']);
    });

Route::prefix('notices')
    ->name('notices.')
    ->middleware('can:notices.has-access')
    ->group(function () {
        Route::post('datatable', [NoticesController::class, 'datatable'])->name('datatable');
        Route::resource('', NoticesController::class)->parameters(['' => 'notice']);
    });

Route::prefix('drivers')
    ->name('drivers.')
    ->middleware('can:drivers.has-access')
    ->group(function () {
        Route::post('datatable', [DriversController::class, 'datatable'])->name('datatable');
        Route::get('export', [DriversController::class, 'export'])->name('export');
        Route::resource('', DriversController::class)->parameters(['' => 'driver']);
    });

Route::prefix('vehicle-reports')
    ->name('vehicle-reports.')
    ->middleware('can:vehicle-reports.has-access')
    ->group(function () {
        Route::post('datatable', [VehicleReportsController::class, 'datatable'])->name('datatable');
        Route::resource('', VehicleReportsController::class)->only('index');
    });

Route::prefix('settings')
    ->name('settings.')
    ->middleware('can:settings.has-access')
    ->group(function () {
        Route::get('edit', [SettingsController::class, 'edit'])->name('edit');
        Route::put('update', [SettingsController::class, 'update'])->name('update');
    });

Route::prefix('companies')
    ->name('companies.')
    ->middleware('can:companies.has-access')
    ->group(function () {
        Route::post('datatable', [CompaniesController::class, 'datatable'])->name('datatable');
        Route::resource('', CompaniesController::class)->except('destroy')->parameters(['' => 'company']);
    });

/**
 * AJAX
 */
Route::name('ajax.')
    ->group(function () {
        Route::get('select2-users', [AjaxController::class, 'users'])->name('users');
        Route::get('select2-cities', [AjaxController::class, 'cities'])->name('cities');
        Route::get('user-billing-data', [AjaxController::class, 'userBillingData'])->name('user-billing-data');
        Route::get('drivers-select', [AjaxController::class, 'drivers'])->name('drivers');
        Route::get('vehicles-select/{user}', [AjaxController::class, 'vehicles'])->name('vehicles');
        Route::get('drivers-exists', [AjaxController::class, 'driverExists'])->name('drivers.exists');
    });
