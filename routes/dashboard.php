<?php

use App\Http\Controllers\Auth\ActivateController;
use App\Http\Controllers\Auth\ConfirmPasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\BusinessCardController;
use App\Http\Controllers\CalculatorController;
use App\Http\Controllers\ChartController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DealerClientsController;
use App\Http\Controllers\DealersController;
use App\Http\Controllers\DepositsController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\ImagesController;
use App\Http\Controllers\InvoicesController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\MessagesController;
use App\Http\Controllers\NotificationsController;
use App\Http\Controllers\OrderFilesController;
use App\Http\Controllers\OrdersController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\ParcelsController;
use App\Http\Controllers\ParcelVehiclesController;
use App\Http\Controllers\PaymentsController;
use App\Http\Controllers\PointsController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\ShopsController;
use App\Http\Controllers\SitemapController;
use App\Http\Controllers\UserVehiclesController;
use App\Http\Controllers\VehicleReportsController;
use App\Http\Controllers\VehiclesController;
use App\Http\Controllers\WarehouseController;
use App\Http\Middleware\DashboardSetLocale;

// Redirect to default language
Route::get('/', [LanguageController::class, 'redirectToLocale'])
    ->name('locale.redirect');

$locales = config('app.available_locales');

foreach ($locales as $locale) {
    Route::group([
        'prefix' => $locale,
        'as' => "{$locale}.",
        'middleware' => DashboardSetLocale::class,
    ], function () use ($locale) {
        Route::get('/', [PagesController::class, 'home'])->name('pages.home');

        // Auction Cars
        Route::get(__('web/routes.auto_auctions', [], $locale), [PagesController::class, 'autoAuctions'])->name('pages.auto_auctions');
        Route::get(__('web/routes.car_haulers', [], $locale), [PagesController::class, 'carHaulers'])->name('pages.car_haulers');
        Route::get(__('web/routes.car_importers', [], $locale), [PagesController::class, 'carImporters'])->name('pages.car_importers');

        // Auth routes
        Route::get('login', LoginController::class)->middleware('guest')->name('login');
        Route::post('logout', [LoginController::class, 'logout'])->name('logout');

        Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [RegisterController::class, 'register'])->name('register.post');

        Route::get('password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
        Route::post('password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
        Route::get('password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
        Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');

        Route::get('password/confirm', [ConfirmPasswordController::class, 'showConfirmForm'])->name('password.confirm');
        Route::post('password/confirm', [ConfirmPasswordController::class, 'confirm']);

        Route::get('activate/{email}/{token}', [ActivateController::class, 'activateUser'])->name('auth.activate');

        // SEO routes
        Route::get(__('routes.car-cost', [], $locale), [PagesController::class, 'carCost'])->name('articles.car-cost');
        Route::get(__('routes.car-import', [], $locale), [PagesController::class, 'carImport'])->name('articles.car-import');
        Route::get(__('routes.package-form-usa', [], $locale), [PagesController::class, 'packageFromUsa'])->name('articles.package-from-usa');
        Route::get(__('routes.package-cost', [], $locale), [PagesController::class, 'packageCost'])->name('articles.package-cost');
        Route::get(__('routes.shopping-in-usa', [], $locale), [PagesController::class, 'shopingInUsa'])->name('articles.shopping-in-usa');
        Route::get(__('routes.container-transport', [], $locale), [PagesController::class, 'containerTransport'])->name('articles.container-transport');
        Route::get(__('routes.shipping-from-usa', [], $locale), [PagesController::class, 'shippingFromUsa'])->name('articles.shipping-from-usa');
        Route::get(__('routes.import-cars-from-usa', [], $locale), [PagesController::class, 'importCarsFromUsa'])->name('articles.import-car-from-usa');
        Route::get(__('routes.motorcycle-import', [], $locale), [PagesController::class, 'motorcycleImport'])->name('articles.motorcycle-import');
        Route::get(__('routes.air-shipments-from-usa', [], $locale), [PagesController::class, 'airShipmentsFromUsa'])->name('articles.air-shipments-from-usa');
        Route::get(__('routes.cosmetics-from-usa', [], $locale), [PagesController::class, 'cosmeticsFromUsa'])->name('articles.cosmetics-from-usa');
        Route::get(__('routes.antique-cars', [], $locale), [PagesController::class, 'antiqueCars'])->name('articles.antique-cars');
        Route::get(__('routes.track-container', [], $locale), [PagesController::class, 'trackContainer'])->name('articles.track-container');

        // Calculator routes
        Route::group(['prefix' => __('routes._calculators', [], $locale), 'as' => 'calculators.'], function () use ($locale) {
            Route::get('/', [CalculatorController::class, 'index'])->name('index');
            Route::get(__('routes.calculators.parcels', [], $locale), [CalculatorController::class, 'parcels'])->name('parcels');
            Route::get(__('routes.calculators._vehicles', [], $locale), [CalculatorController::class, 'vehicles'])->name('vehicles');
            Route::get('vehicles/data', [CalculatorController::class, 'vehicleCalculation'])->name('vehicle-calculation');
            Route::get(__('routes.calculators.containers', [], $locale), [CalculatorController::class, 'containers'])->name('containers');
            Route::get(__('routes.calculators.shopping', [], $locale), [CalculatorController::class, 'shopping'])->name('shopping');
            Route::get(__('routes.calculators.consolidation', [], $locale), [CalculatorController::class, 'consolidation'])->name('consolidation');
            Route::get(__('routes.calculators.exchange', [], $locale), [CalculatorController::class, 'exchanges'])->name('exchange');
            Route::get(__('routes.calculators.vehicles.customs', [], $locale), [CalculatorController::class, 'customs'])->name('customs');
        });

        Route::get(__('routes.shops', [], $locale), [ShopsController::class, 'index'])->name('shops.index');
        Route::get(__('routes.shops/{categoryId}-{categorySlug}', [], $locale), [ShopsController::class, 'index'])->name('shops.category');

        Route::get(__('routes.strategic-partner', [], $locale), [PagesController::class, 'strategicPartner'])->name('pages.strategic-partner');
        Route::post('strategic-partner', [PagesController::class, 'postStrategicPartner'])->name('pages.strategic-partner.post');
        Route::get(__('routes.contact', [], $locale), [PagesController::class, 'contact'])->name('pages.contact');
        Route::post(__('routes.contact', [], $locale), [PagesController::class, 'postContact'])->name('pages.contact.post');

        Route::group(['middleware' => 'auth'], function () {
            // Dashboard routes
            Route::get('dashboard', [DashboardController::class, 'index'])->name('profile.dashboard');

            Route::group(['as' => 'settings.', 'prefix' => 'settings'], function () {
                Route::get('/', [SettingsController::class, 'settings'])->name('index');
                Route::post('/', [SettingsController::class, 'postSettings'])->name('post');
                Route::get('invoice', [SettingsController::class, 'invoice'])->name('invoice');
                Route::post('invoice', [SettingsController::class, 'postInvoiceSettings'])->name('invoice.post');
                Route::get('commissions', [SettingsController::class, 'commissions'])->name('commissions');
                Route::get('price-list/export', [SettingsController::class, 'exportPriceList'])->name('price-list.export');
                Route::get('price-list', [SettingsController::class, 'priceList'])->name('price-list.index');
                Route::get('title-types/{status?}', [SettingsController::class, 'titleTypes'])->name('title-types');
            });

            Route::get('chart-data', [ChartController::class, 'data'])->name('chart.data');

            Route::prefix('files/{modelAlias}/{id}/{attributeName}')
                ->name('files.')
                ->group(function () {
                    Route::get('{fileName}/{encrypted?}', [FileController::class, 'show'])->name('show');
                    Route::get('', [FileController::class, 'showMultiple'])->name('show-multiple');
                    Route::delete('', [FileController::class, 'remove'])->name('remove');
                });

            Route::prefix('images/{modelAlias}/{id}/{attributeName}')
                ->name('images.')
                ->group(function () {
                    Route::get('', [ImagesController::class, 'index'])->name('index');
                    Route::post('', [ImagesController::class, 'store'])->name('store');
                    Route::post('order', [ImagesController::class, 'order'])->name('order');
                });

            Route::group(['middleware' => 'verified'], function () {
                Route::group(['middleware' => 'suspended'], function () {
                    Route::post('warehouse-authorize-contact', [PagesController::class, 'warehouseAuthorizeContact'])->name('pages.warehouse-authorize-contact');

                    Route::prefix('vehicles')
                        ->name('vehicles.')
                        ->group(function () {
                            Route::put('set-customs-agency-status/{vehicle?}', [VehiclesController::class, 'setCustomsAgencyStatus'])->name('set-customs-agency-status');
                            Route::get('files-download/{vehicleId}/{type}', [VehiclesController::class, 'downloadFiles'])->name('files-download');
                            Route::get('validate-create/{vehicleId?}', [VehiclesController::class, 'validateCreate'])->name('validate-create');

                            Route::resource('.parcels', ParcelVehiclesController::class)
                                ->parameters(['' => 'vehicle'])
                                ->middleware('can:vehicles.can-assign-parcels,vehicle')
                                ->only(['create', 'store']);

                            Route::resource('', VehiclesController::class)
                                ->parameters(['' => 'vehicle'])
                                ->names(['index' => 'index']);
                        });

                    Route::resource('vehicle-reports', VehicleReportsController::class)
                        ->only(['index', 'create', 'store'])
                        ->middleware('can:vehicle-reports.has-access')
                        ->names([
                            'index' => 'vehicle-reports.index',
                            'create' => 'vehicle-reports.create',
                            'store' => 'vehicle-reports.store',
                        ]);

                    // Parcels additional routes
                    Route::prefix('parcels')
                        ->name('parcels.')
                        ->group(function (): void {
                            Route::get('/', [ParcelsController::class, 'index'])->name('index');
                            Route::get('create/choose-warehouse', [ParcelsController::class, 'chooseWarehouse'])->name('choose-warehouse');
                            Route::get('sent', [ParcelsController::class, 'sent'])->name('sent');
                            Route::get('tracking-info/{id}', [ParcelsController::class, 'trackingInfo'])->name('tracking-info');
                            Route::get('address', [WarehouseController::class, 'addresses'])->name('address');
                            Route::get('warehouses-comparison', [WarehouseController::class, 'compare'])->name('warehouses-comparison');
                        });

                    // Orders additional routes
                    Route::get('/orders/{type}/{warehouse?}', [OrdersController::class, 'index'])->name('orders.index');
                    Route::get('/orders/{type}/create/choose-warehouse', [OrdersController::class, 'chooseWarehouse'])->name('orders.choose-warehouse');

                    Route::prefix('orders/{order}/files')
                        ->name('orders.files.')
                        ->middleware('can:orders.can-manage-files,order')
                        ->group(function (): void {
                            Route::post('store-user-invoice', [OrderFilesController::class, 'storeUserInvoice'])->name('store-user-invoice');
                            Route::post('store-user-proof-of-payment', [OrderFilesController::class, 'storeUserProofOfPayment'])->name('store-user-proof-of-payment');
                        });

                    Route::group(['prefix' => 'warehouse/{warehouse}', 'as' => 'warehouse.'], function () {
                        Route::group(['prefix' => '{type}'], function () {
                            Route::resource('orders', OrdersController::class, [
                                'except' => ['index'],
                            ]);
                            Route::get('orders/delete/{id}', [OrdersController::class, 'destroy']);
                        });

                        Route::resource('parcels', ParcelsController::class, [
                            'except' => ['index'],
                        ]);
                    });

                    Route::prefix('dealers')
                        ->name('dealers.')
                        ->group(function () {
                            Route::put('assign-client/vehicle/{vehicle}', [DealersController::class, 'assignClientVehicle'])->name('assign-client.vehicle');
                            Route::put('assign-client/user-vehicle/{userVehicle}', [DealersController::class, 'assignClientUserVehicle'])->name('assign-client.user-vehicle');

                            Route::put('set-share-link-disabled/vehicle/{vehicle}', [DealersController::class, 'setShareLinkDisabledVehicle'])->name('set-share-link-disabled.vehicle');
                            Route::put('set-share-link-disabled/user-vehicle/{userVehicle}', [DealersController::class, 'setShareLinkDisabledUserVehicle'])->name('set-share-link-disabled.user-vehicle');

                            Route::get('share-info/vehicle/{vehicle}', [DealersController::class, 'shareInfoVehicle'])->name('share-info.vehicle');
                            Route::get('share-info/user-vehicle/{userVehicle}', [DealersController::class, 'shareInfoUserVehicle'])->name('share-info.user-vehicle');

                            Route::post('send-shareable-url/vehicle/{vehicle}', [DealersController::class, 'sendShareableUrlVehicle'])->name('send-shareable-url.vehicle');
                            Route::post('send-shareable-url/user-vehicle/{userVehicle}', [DealersController::class, 'sendShareableUrlUserVehicle'])->name('send-shareable-url.user-vehicle');

                            Route::resource('', DealersController::class)->only(['index']);

                            Route::resource('user-vehicles', UserVehiclesController::class)->only(['create', 'store', 'show', 'edit', 'update']);
                        });

                    Route::put('business-card/store', [BusinessCardController::class, 'store'])->name('business-card.store');

                    Route::get('dealer-clients/list', [DealerClientsController::class, 'list'])->name('dealer-clients.list');
                    Route::resource('dealer-clients', DealerClientsController::class)
                        ->parameters(['dealer-clients' => 'dealerClient'])
                        ->only(['index', 'store', 'show', 'update', 'destroy']);
                });

                Route::prefix('payments')
                    ->name('payments.')
                    ->group(function () {
                        Route::get('', [PaymentsController::class, 'index'])->name('index');
                        Route::get('info', [PaymentsController::class, 'info'])->name('info');
                        Route::get('orders', [PaymentsController::class, 'orders'])->name('orders');
                        Route::post('orders/pay/{order}', [PaymentsController::class, 'orderPay'])->name('order-pay');
                        Route::get('orders/pay-with-points/{order}', [PaymentsController::class, 'orderPayWithPoints'])->name('order-pay-with-points');
                        Route::get('orders/pay-modal-info/{order}', [PaymentsController::class, 'payModalInfo'])->middleware('can:orders.can-show,order')->name('order-pay-modal-info');
                        Route::put('mark-completed/{completed}', [PaymentsController::class, 'markCompleted'])->name('mark-completed');
                    });

                Route::resource('points', PointsController::class,
                    ['only' => ['index'], 'names' => ['index' => 'points.index']]);
                Route::get('points/info', [PagesController::class, 'pointsInfo'])->name('points.info');

                Route::get('deposits', [DepositsController::class, 'index'])->name('deposits.index');
                Route::get('invoices/{orderId}', [InvoicesController::class, 'show'])->name('invoices.show');

                Route::group(['prefix' => 'messages', 'as' => 'messages.'], function () {
                    Route::get('orders', [MessagesController::class, 'orders'])->name('orders');
                    Route::get('messages', [MessagesController::class, 'messages'])->name('messages');
                    Route::post('', [MessagesController::class, 'store'])->name('store');
                });

                Route::get('notifications/mark-all-as-read', [NotificationsController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
            });
        });
    });
}

Route::get('sitemap', [SitemapController::class, 'render']);
