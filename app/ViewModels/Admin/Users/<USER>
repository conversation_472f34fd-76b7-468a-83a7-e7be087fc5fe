<?php

declare(strict_types=1);

namespace App\ViewModels\Admin\Users;

use App\Models\Presenters\UserPresenter;
use App\Models\User;
use App\Project\Core\Presenters\BaseViewModel;
use App\Project\Core\Presenters\CollectionPresenter;
use App\Project\Option\Enums\UserOptions;
use App\Project\User\Enums\UserAccountTypes;
use Illuminate\Support\Collection;

/**
 * @property UserPresenter $user
 * @property bool[] $permissions
 * @property CollectionPresenter $warehousesWithPermissions
 * @property CollectionPresenter $countries
 */
class UsersEditViewModel extends BaseViewModel
{
    public function __construct(
        User $user,
        Collection $countries,
        Collection $warehousesWithPermissions,
        array $permissions
    ) {
        parent::__construct(
            compact(
                'user',
                'countries',
                'warehousesWithPermissions',
                'permissions'
            )
        );
    }

    /**
     * @return array<string, string>
     */
    public function vehiclePriceListAccessesTypes(): array
    {
        return array_combine(
            UserAccountTypes::ALL_TYPES,
            array_map(
                fn (string $type): string => UserAccountTypes::humanReadableForAdminPanel($type),
                UserAccountTypes::ALL_TYPES
            )
        );
    }

    /**
     * @return mixed[]
     */
    public function userOptionsToArray(): array
    {
        return $this->user->options->pluck('value', 'key')->toArray();
    }

    /**
     * @return mixed[]
     */
    public function userDocumentsToArray(): array
    {
        return $this->user->documents->keyBy('type')->all();
    }

    /**
     * @return mixed[]
     */
    public function countriesToArray(): array
    {
        return $this->countries->pluck('name', 'id')->toArray();
    }

    /**
     * @return mixed[]
     */
    public function warehousesWithPermissionsToArray(): array
    {
        return $this->warehousesWithPermissions->pluck('name', 'id')
            ->toArray();
    }

    /**
     * @return mixed[]
     */
    public function userPermittedWarehouses(): array
    {
        return $this->user->permittedWarehouses->pluck('id')->toArray();
    }

    public function defaultPaymentDeadlineDaysValue(): int
    {
        return (int) $this->defaultOptionValue(UserOptions::PAYMENT_DEADLINE_DAYS);
    }

    public function defaultOptionValue(string $key): mixed
    {
        return UserOptions::DEFAULTS[$key] ?? null;
    }
}
