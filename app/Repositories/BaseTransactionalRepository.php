<?php

namespace App\Repositories;

use App\Models\BaseTransactionalModel;
use App\Models\User;
use App\Project\Payment\HasCompany;
use App\Project\Payment\UserCompanyBalanceDto;
use App\Project\Transactional\BaseTransactionalDto;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use stdClass;

abstract class BaseTransactionalRepository
{
    abstract protected function transactionModelClass(): string;

    abstract protected function searchColumns(): array;

    public function forUser(User $user, array $filters = []): LengthAwarePaginator
    {
        $tableName = $this->modelInstance()
            ->getTable();

        $query = $this->modelInstance()
            ->newQuery()
            ->where('user_id', $user->id);

        $this->loadTransactionalRelations($query);

        if (isset($filters['amount_type']) && $filters['amount_type'] !== 'all') {
            if ($filters['amount_type'] === 'outgoings') {
                $query->where("{$tableName}.amount", '<', 0);
            } elseif ($filters['amount_type'] === 'income') {
                $query->where("{$tableName}.amount", '>=', 0);
            }
        }

        if (isset($filters['company'])) {
            $query->where('company_id', $filters['company']);
        }

        if (! empty($filters['search'])) {
            $term = '%'.$filters['search'].'%';
            $query->where(function ($q) use ($term, $tableName): void {
                foreach ($this->searchColumns() as $column) {
                    $q->orWhere("{$tableName}.{$column}", 'like', $term);
                }
            });
        }

        return $query->orderBy("{$tableName}.created_at", 'DESC')
            ->orderBy("{$tableName}.id", 'DESC')
            ->paginate(30);
    }

    public function findForUser(User $user, int $id): ?BaseTransactionalModel
    {
        return $this->modelInstance()
            ->newQuery()
            ->where('user_id', $user->id)
            ->find($id);
    }

    protected function loadTransactionalRelations(Builder $query): void
    {
    }

    public function make(BaseTransactionalDto $transactionDto): BaseTransactionalModel
    {
        $transaction = $transactionDto->getTransactionId() ? $this->modelInstance()
            ->newQuery()
            ->findOrFail($transactionDto->getTransactionId()) : $this->modelInstance();
        $transaction->amount = $transactionDto->isCharge() ? (-$transactionDto->getAmount(
        )) : $transactionDto->getAmount();
        $title = $transactionDto->getTitle();
        $transaction->title = $title;
        $transaction->user_id = $transactionDto->getUserId();
        $transaction->created_at = $transactionDto->getCreatedAt() ?? now();

        if ($transactionDto instanceof HasCompany && $transactionDto->getCompanyId()) {
            $transaction->company_id = $transactionDto->getCompanyId(); // @phpstan-ignore-line
        }

        return $transaction;
    }

    public function userTransactionalSummaryAmount(User $user): int
    {
        $sum = $this->modelInstance()
            ->newQuery()
            ->where('user_id', $user->id)
            ->sum('amount');

        return $sum ?? 0;
    }

    public function modelInstance(): BaseTransactionalModel
    {
        $modelName = $this->transactionModelClass();

        return new $modelName;
    }

    /**
     * @return array<UserCompanyBalanceDto>
     */
    public function userTransactionalSummaryBalancesForCompanies(User $user): array
    {
        $balances = $this->modelInstance()
            ->newQuery()
            ->where('user_id', $user->id)
            ->whereNotNull('company_id')
            ->select('company_id', DB::raw('SUM(amount) as balance'))
            ->groupBy('company_id')
            ->toBase()
            ->get();

        return $balances->map(fn (stdClass $balance): UserCompanyBalanceDto => new UserCompanyBalanceDto(
            userId: $user->id,
            companyId: $balance->company_id,
            balance: $balance->balance,
        ))->all();
    }
}
