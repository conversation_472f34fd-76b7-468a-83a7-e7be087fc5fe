<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\VehicleDeliveryLocation;
use App\Models\VehicleDeliveryLocationInformation;

class VehicleDeliverLocationInformationRepository
{
    public function make(
        array $data,
        VehicleDeliveryLocation $vehicleDeliveryLocation,
        ?VehicleDeliveryLocationInformation $vehicleDeliveryLocationInformation = null
    ): VehicleDeliveryLocationInformation {
        $vehicleDeliveryLocationInformation = $vehicleDeliveryLocationInformation ?: new VehicleDeliveryLocationInformation();
        $vehicleDeliveryLocationInformation->vehicleDeliveryLocation()->associate($vehicleDeliveryLocation);
        $vehicleDeliveryLocationInformation->information = $data['information'];
        $vehicleDeliveryLocationInformation->city_id = $data['city_id'];
        $vehicleDeliveryLocationInformation->hide_city_in_calculator = $data['hide_city_in_calculator'];

        return $vehicleDeliveryLocationInformation;
    }
}
