<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Message;
use Illuminate\Database\Eloquent\Collection;

class MessageRepository
{
    /**
     * @return Collection<int, Message>
     */
    public function getConversationByMessageableId(int $messageable): Collection
    {
        /** @var Collection<int, Message> $messages */
        $messages = Message::query()
            ->where('messageable_id', $messageable)
            ->orderBy('created_at')
            ->get();

        return $messages;
    }
}
