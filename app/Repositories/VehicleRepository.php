<?php

namespace App\Repositories;

use App\Models\DealerClient;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\Type;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Warehouse;
use App\Project\DriverVehicle\Enums\VehicleDamageStatus;
use App\Project\Order\OrderDto;
use App\Project\Storage\CloudStorageModelService;
use App\Project\Vehicle\Enums\VehicleStatuses;
use App\Project\Vehicle\Enums\VehicleTabs;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

readonly class VehicleRepository
{
    public function __construct(
        private OrderRepository $orderRepository,
        private VehicleRecipientRepository $recipientRepository,
        private OrderInvoiceRepository $orderInvoiceRepository,
        private CloudStorageModelService $cloudStorageModelService,
    ) {
    }

    public function create(Request $request, User $user): Vehicle
    {
        $vehicle = new Vehicle();
        DB::transaction(
            function () use ($vehicle, $user, $request): void {
                $vehicle->user()->associate($user);

                $order = $this->createOrder($vehicle);
                $vehicle->order()->associate($order);

                $this->bindUserPayForVehicle($request, $vehicle);
                $this->bindPrices($request, $vehicle);
                $this->bindBillOfSale($request, $vehicle);
                $this->bindVinNumber($request, $vehicle);

                if ($request->get(
                    'type_of_service'
                ) !== VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT) {
                    $this->bindPaymentDate($request, $vehicle);
                    $this->bindReceiptDate($request, $vehicle);
                }

                $this->bindSeller($request, $vehicle);
                $this->bindVehicleDescription($request, $vehicle);
                $this->bindInsuranceAmount($request, $vehicle);

                $vehicle->auction_url = $request->get('auction_url');

                $vehicle->vehicle_type = $request->get('vehicle_type');
                $vehicle->lot_number = $request->get('lot_number');
                $vehicle->buyer_number = $request->get('buyer_number');
                $vehicle->gate_pass_pin = $request->get('gate_pass_pin');

                $vehicle->vehicle_delivery_location_id = $request->get('vehicle_delivery_location_id');
                $vehicle->vehicle_destination_agency_id = $request->get('vehicle_destination_agency_id');
                $vehicle->marine_insurance = $request->get('marine_insurance');
                $vehicle->is_electric_or_hybrid = $request->get('is_electric_or_hybrid');
                $vehicle->is_premium_loading = $request->boolean('is_premium_loading');
                $vehicle->comment = $request->get('comment');
                $vehicle->send_title_to_customs = $request->boolean('send_title_to_customs');

                $vehicle->save();

                $recipient = $this->recipientRepository->make($request->get('recipient'));
                $recipient->vehicle()->associate($vehicle);
                $recipient->save();

                $invoice = $this->orderInvoiceRepository->make($request->get('invoice'));
                $invoice->order()->associate($order);
                $invoice->save();
            }
        );

        return $vehicle;
    }

    public function makeForAdmin(
        Request $request,
        User $user,
        Vehicle $vehicle
    ): Vehicle {
        DB::transaction(
            function () use ($request, $user, $vehicle): void {
                $vehicle->user()->associate($user);
                $this->bindReleasedAt($request, $vehicle);
                $this->bindUserPayForVehicle($request, $vehicle, false);
                $this->bindPrices($request, $vehicle, false);
                $this->bindBillOfSale($request, $vehicle);
                $this->bindTitleFile($request, $vehicle);
                $this->bindBillOfLanding($request, $vehicle);
                $this->bindHazmatDocument($request, $vehicle);
                $this->bindVinNumber($request, $vehicle, false);
                $this->bindPaymentDate($request, $vehicle, false);
                $this->bindReceiptDate($request, $vehicle, false);
                $this->bindSeller($request, $vehicle, false);
                $this->bindVehicleDescription($request, $vehicle, false);
                $this->bindInsuranceAmount($request, $vehicle, false);

                $easyBind = [
                    'vehicle_terminal_id',
                    'vehicle_shipping_line_id',
                    'container_number',
                    'container_booking_number',
                    'vehicle_destination_agency_id',
                    'vehicle_delivery_location_id',
                    'released',
                    'auction_url',
                    'vehicle_type',
                    'lot_number',
                    'buyer_number',
                    'gate_pass_pin',
                    'marine_insurance',
                    'is_electric_or_hybrid',
                    'is_premium_loading',
                    'garbage_removal',
                    'moisture_absorber',
                    'comment',
                    'keys',
                    'title_tracking_number',
                    'status',
                    'admin_comment',
                    'title_status',
                    'vehicle_length',
                    'send_title_to_customs',
                ];

                foreach ($easyBind as $value) {
                    if ($request->exists($value)) {
                        $vehicle->{$value} = $request->get($value) === '' ? null : $request->get($value);
                    }
                }

                if ($request->exists('eta')) {
                    $vehicle->eta = ($eta = $request->get('eta')) ? Carbon::createFromFormat('d.m.Y', $eta)->startOfDay( // @phpstan-ignore-line
                    ) : null;
                }

                if ($request->exists('collection_date')) {
                    if ($collectionDate = $request->get('collection_date')) {
                        $vehicle->collection_date = Carbon::createFromFormat('d.m.Y', $collectionDate)->startOfDay(); // @phpstan-ignore-line
                    } else {
                        $vehicle->collection_date = null;
                    }
                }

                $vehicle->save();

                if ($request->exists('recipient')) {
                    $recipient = $this->recipientRepository->make($request->get('recipient'), $vehicle->recipient);
                    $recipient->vehicle()->associate($vehicle);
                    $recipient->save();
                }

                if ($request->exists('company_id')) {
                    $vehicle->order->company_id = $request->get('company_id');
                    $vehicle->order->save();
                }
            }
        );

        return $vehicle;
    }

    private function createOrder(Vehicle $vehicle): Order
    {
        /** @var Warehouse $warehouse */
        $warehouse = Warehouse::where('slug', Warehouse::POLAMER_SLUG)->firstOrFail();
        /** @var Type $type */
        $type = Type::where('slug', Type::VEHICLES_SLUG)->firstOrFail();
        /** @var OrderStatus $status */
        $status = OrderStatus::where('slug', OrderStatus::WAITING_SLUG)->firstOrFail();

        $orderDto = new OrderDto(
            $vehicle->user_id,
            $type->id,
            $status->id,
            '',
            '',
            null,
            $warehouse->id,
            null,
            0,
            $vehicle->garbage_removal ? config('payments.prices.vehicle_garbage_removal') : 0,
            $vehicle->moisture_absorber ? config('payments.prices.vehicle_moisture_absorber') : 0
        );

        return $this->orderRepository->createOrderFromDto($orderDto);
    }

    private function bindReleasedAt(Request $request, Vehicle $vehicle): void
    {
        if ($request->exists('released') && $vehicle->status !== VehicleStatuses::PROVIDED) {
            $released = (bool) $request->get('released');
            $vehicle->released_at = $released ?
                ($vehicle->released_at ?? Carbon::now()) :
                null;
        }

        if ($request->exists('status')) {
            if ($request->get('status') === VehicleStatuses::PROVIDED) {
                $vehicle->released_at = $vehicle->released_at ?: Carbon::now();

                return;
            }

            $vehicle->released_at = $vehicle->released ? $vehicle->released_at : null;
        }
    }

    private function bindVehicleDescription(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('vehicle_description'))) {
            return;
        }

        $vehicle->vehicle_description = mb_strtoupper((string) $request->get('vehicle_description'));
    }

    private function bindUserPayForVehicle(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('type_of_service'))) {
            return;
        }

        $typeOfService = $request->get('type_of_service');
        $vehicle->type_of_service = $typeOfService;
        $vehicle->user_pay_for_vehicle = false;
    }

    private function bindInsuranceAmount(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('insurance_amount') || ! $request->exists('marine_insurance'))) {
            return;
        }

        $marineInsurance = (int) $request->get('marine_insurance');
        $vehicle->insurance_amount = null;
        if ($marineInsurance === 0) {
            return;
        }
        if (! $request->get('insurance_amount')) {
            return;
        }
        $vehicle->insurance_amount = string_to_price($request->get('insurance_amount'));
    }

    private function bindPrices(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('type_of_service'))) {
            return;
        }

        $typeOfService = $request->get('type_of_service');
        $vehicle->bid_to_price = null;
        $vehicle->buy_now_price = null;
        $vehicle->bought_for = null;
        if ($typeOfService === VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT) {
            $vehicle->bid_to_price = $request->get('bid_to_price') ? string_to_price(
                $request->get('bid_to_price')
            ) : null;
            $vehicle->buy_now_price = $request->get('buy_now_price') ? string_to_price(
                $request->get('buy_now_price')
            ) : null;
        }
    }

    private function bindBillOfSale(Request $request, Vehicle $vehicle): void
    {
        $this->cloudStorageModelService->privateStoreFiles(
            request: $request,
            model: $vehicle,
            formKey: 'bill_of_sale',
            attributeName: 'bill_of_sale',
        );
    }

    private function bindTitleFile(Request $request, Vehicle $vehicle): void
    {
        $this->cloudStorageModelService->privateStoreFiles(
            request: $request,
            model: $vehicle,
            formKey: 'title_file',
            attributeName: 'title_file',
        );
    }

    private function bindBillOfLanding(Request $request, Vehicle $vehicle): void
    {
        $this->cloudStorageModelService->privateStoreFiles(
            request: $request,
            model: $vehicle,
            formKey: 'bill_of_landing',
            attributeName: 'bill_of_landing',
        );
    }

    private function bindHazmatDocument(Request $request, Vehicle $vehicle): void
    {
        $this->cloudStorageModelService->privateStoreFiles(
            request: $request,
            model: $vehicle,
            formKey: 'hazmat_document',
            attributeName: 'hazmat_document',
        );
    }

    private function bindVinNumber(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('no_vin') || ! $request->exists('vin_number'))) {
            return;
        }

        $noVin = $request->get('no_vin');
        $vehicle->vin_number = null;
        if (! $noVin) {
            $vehicle->vin_number = $request->get('vin_number');
        }
    }

    private function bindPaymentDate(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('payment_finished') || ! $request->exists('payment_date'))) {
            return;
        }

        $paymentFinished = $request->get('payment_finished');
        $vehicle->payment_date = null;
        if ($paymentFinished) {
            return;
        }
        if (! $request->get('payment_date')) {
            return;
        }
        $vehicle->payment_date = Carbon::createFromFormat('d.m.Y', $request->get('payment_date')) // @phpstan-ignore-line
            ->startOfDay();
    }

    private function bindReceiptDate(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('no_receipt_date') || ! $request->exists('receipt_date'))) {
            return;
        }

        $noReceiptDate = $request->get('no_receipt_date');
        $vehicle->receipt_date = null;
        if ($noReceiptDate) {
            return;
        }
        if (! $request->get('receipt_date')) {
            return;
        }
        $vehicle->receipt_date = Carbon::createFromFormat('d.m.Y', $request->get('receipt_date')) // @phpstan-ignore-line
            ->startOfDay();
    }

    private function bindSeller(Request $request, Vehicle $vehicle, bool $forceBind = true): void
    {
        if (! $forceBind && (! $request->exists('seller_type'))) {
            return;
        }

        $sellerType = $request->get('seller_type');
        $vehicle->seller_type = $sellerType;
        $vehicle->vehicle_auction_location_id = null;
        $vehicle->vehicle_auction_location_company = null;
        $vehicle->vehicle_auction_location_seller_name = null;
        $vehicle->vehicle_auction_location_address = null;
        $vehicle->vehicle_auction_location_phone = null;
        $vehicle->vehicle_auction_location_email = null;
        if ($sellerType === 'copart' || $sellerType === 'iaa') {
            $vehicle->vehicle_auction_location_id = $request->get('vehicle_auction_location_id');
        } else {
            $vehicle->vehicle_auction_location_company = $request->get('vehicle_auction_location_company');
            $vehicle->vehicle_auction_location_seller_name = $request->get('vehicle_auction_location_seller_name');
            $vehicle->vehicle_auction_location_address = $request->get('vehicle_auction_location_address');
            $vehicle->vehicle_auction_location_phone = $request->get('vehicle_auction_location_phone');
            $vehicle->vehicle_auction_location_email = $request->get('vehicle_auction_location_email');
        }
    }

    /**
     * @return mixed[]
     */
    public function countByTabs(User $user, bool $forAdminPanel = false): array
    {
        $cacheKey = 'vehicle_count_by_tabs_'.$user->id.'_'.($forAdminPanel ? 'admin' : 'user');
        $cached = Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }
        $managedTerminalsIds = ($forAdminPanel && $user->isTerminalManager()) ? $user->managedVehicleTerminals->pluck(
            'id'
        )->all() : [];
        $managedDestinationAgenciesIds = ($forAdminPanel && $user->isCustomsAgency()) ? $user->managedDestinationAgencies->pluck(
            'id'
        )->all() : [];
        $tabs = VehicleTabs::forUser($user, $forAdminPanel);

        $counts = array_combine(
            array_keys($tabs),
            array_map(
                function ($tab, $statuses) use (
                    $managedTerminalsIds,
                    $managedDestinationAgenciesIds,
                    $forAdminPanel,
                    $user
                ) {
                    $query = Vehicle::whereIn('vehicles.status', $statuses)
                        ->when(
                            ! empty($managedTerminalsIds),
                            fn (Builder $query) => $query->whereIn('vehicles.vehicle_terminal_id', $managedTerminalsIds)
                        )->when(
                            ! empty($managedDestinationAgenciesIds),
                            fn (Builder $query) => $query->whereIn(
                                'vehicles.vehicle_destination_agency_id',
                                $managedDestinationAgenciesIds
                            )
                        )->when(
                            $tab === VehicleTabs::AUDIT,
                            fn (Builder $query) => $query->where(
                                'type_of_service',
                                VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT
                            )
                        )
                        ->when(
                            VehicleTabs::shouldFilterByNewCompanies($tab),
                            fn (Builder $query) => $query->deliveredByNewCompanies() // @phpstan-ignore-line
                        )
                        ->when(
                            $tab === VehicleTabs::VEHICLE_DAMAGES,
                            fn (Builder $query) => $query->whereHas('driverVehicles', function (Builder $query) {
                                $query->whereNotIn('vehicle_damages_status', [VehicleDamageStatus::NONE, VehicleDamageStatus::RESOLVED]);
                            })
                        );

                    if (! $forAdminPanel) {
                        $query->where('user_id', $user->id);
                    }

                    return $query->count();
                },
                array_keys($tabs),
                $tabs
            )
        );

        Cache::put($cacheKey, $counts, $forAdminPanel ? now()->addMinutes(5) : now()->addMinute());

        return $counts;
    }

    public function forUser(User $user, array $filters = [], bool $searchWithDealerClient = false): Builder
    {
        $query = Vehicle::where('user_id', $user->id)
            ->with(
                [
                    'order',
                    'order.invoice',
                    'order.status',
                    'shippingLine',
                    'destinationAgency',
                    'deliveryLocation',
                    'terminal',
                    'terminal.city',
                    'terminal.city.state',
                    'parcels',
                    'dealerClient',
                ]
            );

        if (! empty($filters['search'])) {
            $term = '%'.$filters['search'].'%';
            $query->withColumn('order', 'id', 'order_id')
                ->where(
                    function (Builder $query) use ($term): void {
                        $columns = [
                            'vin_number',
                            'container_number',
                            'order_id',
                            'vehicle_description',
                            'buyer_number',
                            'lot_number',
                        ];
                        array_walk(
                            $columns,
                            function (string $column) use ($query, $term): void {
                                $query->orWhere($column, 'like', $term);
                            }
                        );
                    }
                );

            if ($searchWithDealerClient) {
                $query->orWhereHas(
                    'dealerClient',
                    function (Builder $query) use ($term): void {
                        $query->where(function (Builder $query) use ($term): void {
                            $query->orWhere('first_name', 'like', $term)
                                ->orWhere('last_name', 'like', $term)
                                ->orWhere('email', 'like', $term)
                                ->orWhere('phone_number', 'like', $term);
                        });
                    }
                );
            }
        }

        if (! empty($filters['tab'])) {
            $statuses = VehicleTabs::statusesForTab($filters['tab']);
            $query->whereIn('status', $statuses);
        }

        if (isset($filters['customs_agency_status'])) {
            if ($filters['customs_agency_status'] === 'none') {
                $query->whereNull('customs_agency_status');
            } else {
                $query->where('customs_agency_status', $filters['customs_agency_status']);
            }
        }

        if (in_array($filters['tab'] ?? null, [VehicleTabs::SENT, VehicleTabs::PROVIDED])) {
            if ($filters['tab'] === VehicleTabs::SENT) {
                $query->orderBy('eta', 'ASC')
                    ->orderBy('id', 'ASC');
            }

            if ($filters['tab'] === VehicleTabs::PROVIDED) {
                $query->orderBy('eta', 'DESC')
                    ->orderBy('id', 'DESC');
            }
        } else {
            $query->orderBy('created_at', 'DESC')
                ->orderBy('id', 'DESC');
        }

        return $query;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function findForUser(User $user, int $id): Vehicle
    {
        /** @var Vehicle $vehicle */
        $vehicle = Vehicle::where('user_id', $user->id)->findOrFail($id);

        return $vehicle;
    }

    public function forDealerClient(DealerClient $dealerClient): Builder
    {
        return Vehicle::where('dealer_client_id', $dealerClient->getKey())
            ->where('share_link_disabled', false)
            ->orderBy('id', 'desc');
    }
}
