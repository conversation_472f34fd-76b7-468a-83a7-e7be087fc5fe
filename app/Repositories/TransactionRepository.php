<?php

namespace App\Repositories;

use App\Models\BaseTransactionalModel;
use App\Models\Transaction;
use App\Models\User;
use App\Project\Payment\TransactionDto;
use App\Project\Transactional\BaseTransactionalDto;
use Illuminate\Database\Eloquent\Builder;

/**
 * @method Transaction findForUser(User $user, int $id)
 */
class TransactionRepository extends BaseTransactionalRepository
{
    /**
     * @param TransactionDto $transactionDto
     * @return Transaction
     */
    public function make(BaseTransactionalDto $transactionDto): BaseTransactionalModel
    {
        /** @var Transaction $transaction */
        $transaction = parent::make($transactionDto);
        $title = $transactionDto->getTitle() !== null ? trim($transactionDto->getTitle()) : null;
        $transaction->template_id = empty($title) ? $transactionDto->getTemplateId() : null;
        $transaction->order_id = $transactionDto->getOrderId();
        $transaction->company_id = $transactionDto->getCompanyId();
        $transaction->paid_at = $transactionDto->getPaidAt() ?? now(); // @phpstan-ignore-line

        return $transaction;
    }

    protected function loadTransactionalRelations(Builder $query): void
    {
        parent::loadTransactionalRelations($query);

        $query->with(['order', 'order.status', 'order.type', 'order.warehouse']);
    }

    protected function transactionModelClass(): string
    {
        return Transaction::class;
    }

    protected function searchColumns(): array
    {
        return [
            'order_id',
            'title',
            'amount',
            'created_at',
        ];
    }
}
