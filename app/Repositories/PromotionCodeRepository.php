<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Order;
use App\Models\PromotionCode;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class PromotionCodeRepository
{
    public function getValid(): Collection
    {
        $now = Carbon::now();

        return PromotionCode::where('active', 1)
            ->where('valid_from', '<', $now)
            ->where('valid_to', '>', $now)
            ->get();
    }

    public function findValid(string $promotionCode): ?PromotionCode
    {
        $now = Carbon::now();

        return PromotionCode::where('code', $promotionCode)
            ->where('active', 1)
            ->where('valid_from', '<', $now)
            ->where('valid_to', '>', $now)
            ->first();
    }

    public function hasUserUsedCode(User $user, PromotionCode $promotionCode): bool
    {
        return Order::where('user_id', $user->getKey())
            ->where('promotion_code_id', $promotionCode->getKey())
            ->exists();
    }
}
