<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\DealerClient;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class DealerClientRepository
{
    public function forUser(User $user, array $filters = []): LengthAwarePaginator
    {
        $query = DealerClient::where('user_id', $user->id)
            ->withCount('vehicles');

        if (isset($filters['search']) && ! empty($filters['search'])) {
            $term = '%'.$filters['search'].'%';
            $query->where(
                function (Builder $query) use ($term): void {
                    $columns = [
                        'first_name',
                        'last_name',
                        'email',
                    ];
                    array_walk(
                        $columns,
                        function (string $column) use ($query, $term): void {
                            $query->orWhere($column, 'like', $term);
                        }
                    );
                }
            );
        }

        return $query->paginate(30);
    }

    public function make(array $data, User $user, ?DealerClient $dealerClient = null): DealerClient
    {
        $dealerClient = $dealerClient ?: new DealerClient();
        $dealerClient->email = $data['email'];
        $dealerClient->first_name = $data['first_name'] ?? null;
        $dealerClient->last_name = $data['last_name'] ?? null;
        $dealerClient->phone_number = $data['phone_number'] ?? null;
        $dealerClient->user_id = $user->id;

        return $dealerClient;
    }
}
