<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Notice;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class NoticeRepository
{
    public function forUser(User $user, int $limit = 26): Collection
    {
        return Notice::where('show_to_all_users', true)
            ->orWhereHas('users', function (Builder $query) use ($user): void {
                $query->where('users.id', $user->getKey());
            })->orderBy('created_at', 'DESC')
            ->orderBy('id', 'DESC')
            ->limit($limit)
            ->get();
    }
}
