<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Notification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class NotificationRepository
{
    public function forUser(User $user, int $limit = 30): Collection
    {
        return Notification::where('user_id', $user->getKey())
            ->orderBy('created_at', 'DESC')
            ->orderBy('id', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function markAllAsRead(User $user): void
    {
        Notification::where('user_id', $user->getKey())
            ->whereNull('displayed_at')
            ->update(['displayed_at' => Carbon::now()]);
    }
}
