<?php

declare(strict_types=1);

namespace App\Observers;

use App\Models\Driver;
use App\Models\DriverVehicle;
use App\Notifications\TransportPaid;

class TransportPaidDriverNotificationObserver
{
    public function created(DriverVehicle $driverVehicle): void
    {
        $this->notifyDriver($driverVehicle);
    }

    public function updated(DriverVehicle $driverVehicle): void
    {
        $this->notifyDriver($driverVehicle);
    }

    private function notifyDriver(DriverVehicle $driverVehicle): void
    {
        if ($driverVehicle->driver->email === null) {
            return;
        }

        if ($driverVehicle->paid_at === null || ! $driverVehicle->isDirty('paid_at')) {
            return;
        }

        /** @var Driver $driver */
        $driver = $driverVehicle->driver;

        $driver->notify(
            (new TransportPaid($driverVehicle))->locale('en')
        );
    }
}
