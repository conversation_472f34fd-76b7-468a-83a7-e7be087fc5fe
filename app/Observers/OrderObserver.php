<?php

namespace App\Observers;

use App\Models\Order;
use App\Project\Invoice\InvoiceService;
use App\Project\Payment\TransactionsService;
use App\Project\Point\PointsService;

readonly class OrderObserver
{
    public function __construct(
        private TransactionsService $transactionsService,
        private PointsService $pointsService,
        private InvoiceService $invoiceService
    ) {
    }

    public function created(Order $order): void
    {
        $this->transactionsService->manageOrder($order);
        $this->pointsService->manageOrder($order);
    }

    public function updated(Order $order): void
    {
        $this->transactionsService->manageOrder($order);
        $this->invoiceService->manageOrder($order);
        $this->pointsService->manageOrder($order);
    }

    public function deleted(Order $order): void
    {
        $this->transactionsService->deleteTransactionForOrder($order);
        $this->pointsService->deletePointsForOrder($order);
    }
}
