<?php

namespace App\Providers;

use App\Models\Warehouse;
use App\Policies\WarehousePolicy;
use App\Project\Core\Gates\AdminPanelAccessGate;
use App\Project\Core\Gates\BasicGate;
use App\Project\Core\Gates\CostsGate;
use App\Project\Core\Gates\DealerClientsGate;
use App\Project\Core\Gates\DealersGate;
use App\Project\Core\Gates\DriversGate;
use App\Project\Core\Gates\DriverVehicleGate;
use App\Project\Core\Gates\InvoicesGate;
use App\Project\Core\Gates\MessagesGate;
use App\Project\Core\Gates\OrdersGate;
use App\Project\Core\Gates\ParcelsGate;
use App\Project\Core\Gates\PointsGate;
use App\Project\Core\Gates\TasksGate;
use App\Project\Core\Gates\TransactionsGate;
use App\Project\Core\Gates\UserBusinessCardsGate;
use App\Project\Core\Gates\UserVehiclesGate;
use App\Project\Core\Gates\VehicleReportsGate;
use App\Project\Core\Gates\VehiclesGate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /** @var string[] */
    protected $policies = [
        Warehouse::class => WarehousePolicy::class,
    ];

    public function boot(): void
    {
        Gate::define('pa.has-access', AdminPanelAccessGate::class.'@hasAccess');
        Gate::define('pa.can-see-charts', BasicGate::class.'@isSuperAdmin');
        Gate::define('pa.can-see-counts', BasicGate::class.'@isSuperAdmin');

        Gate::define('users.has-access', BasicGate::class.'@isSuperAdmin');
        Gate::define('users.can-view-documents', BasicGate::class.'@isSuperAdmin');
        Gate::define('users.can-remove-documents', BasicGate::class.'@isSuperAdmin');
        Gate::define('users.can-view-personal-information', BasicGate::class.'@isSuperAdmin');

        Gate::define('messages.has-access', MessagesGate::class.'@hasAccess');
        Gate::define('messages.can-archive', MessagesGate::class.'@canArchive');

        Gate::define('shops.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('parcels.has-access', ParcelsGate::class.'@hasAccess');
        Gate::define('parcels.can-delete', ParcelsGate::class.'@canDelete');
        Gate::define('parcels.can-view-files', ParcelsGate::class.'@canViewFiles');
        Gate::define('parcels.can-manage-images', ParcelsGate::class.'@canManageImages');

        Gate::define('vehicles.has-access', VehiclesGate::class.'@hasAccess');
        Gate::define('vehicles.can-show', VehiclesGate::class.'@canShow');
        Gate::define('vehicles.can-show-in-pa', VehiclesGate::class.'@canShowInPa');
        Gate::define('vehicles.can-delete', BasicGate::class.'@isSuperAdmin');
        Gate::define('vehicles.has-access-to-valuation', BasicGate::class.'@isSuperAdmin');
        Gate::define('vehicles.has-access-to-release', BasicGate::class.'@isSuperAdmin');
        Gate::define('vehicles.has-access-to-messages', VehiclesGate::class.'@hasAccessToMessages');
        Gate::define('vehicles.can-view-files', VehiclesGate::class.'@canViewFiles');
        Gate::define('vehicles.can-remove-files', VehiclesGate::class.'@canRemoveFiles');
        Gate::define('vehicles.can-export', BasicGate::class.'@isSuperAdmin');
        Gate::define('vehicles.can-export-for-customs-agency', VehiclesGate::class.'@canExportForCustomsAgency');
        Gate::define('vehicles.can-show-recipients-info', VehiclesGate::class.'@canShowRecipientsInfo');
        Gate::define('vehicles.can-manage-images', VehiclesGate::class.'@canManageImages');
        Gate::define('vehicles.can-assign-parcels', VehiclesGate::class.'@canAssignParcels');
        Gate::define('vehicles.can-transfer', VehiclesGate::class.'@canTransfer');

        Gate::define('user-vehicles.can-view-files', UserVehiclesGate::class.'@canViewFiles');
        Gate::define('user-vehicles.can-remove-files', UserVehiclesGate::class.'@canRemoveFiles');
        Gate::define('user-vehicles.can-manage-images', UserVehiclesGate::class.'@canManageImages');
        Gate::define('user-vehicles.can-show', UserVehiclesGate::class.'@canShow');
        Gate::define('user-vehicles.cen-edit', UserVehiclesGate::class.'@canEdit');

        Gate::define('vehicle-user-documents.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('vehicle-reports.has-access', VehicleReportsGate::class.'@hasAccess');
        Gate::define('vehicle-reports.can-pay', VehicleReportsGate::class.'@canPay');
        Gate::define('vehicle-reports.can-pay-with-points', VehicleReportsGate::class.'@canPayWithPoints');
        Gate::define('vehicle-reports.can-pay-with-any-company', VehicleReportsGate::class.'@canPayWithAnyCompany');
        Gate::define('vehicle-reports.can-show', VehicleReportsGate::class.'@canShow');

        Gate::define('vehicle-calculators.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('orders.has-access', OrdersGate::class.'@hasAccess');
        Gate::define('orders.can-show', OrdersGate::class.'@canShow');
        Gate::define('orders.edit-details', OrdersGate::class.'@canEditDetails');
        Gate::define('orders.can-change-status', OrdersGate::class.'@canChangeStatus');
        Gate::define('orders.can-generate-report', OrdersGate::class.'@canGenerateReport');
        Gate::define('orders.can-create', OrdersGate::class.'@canCreate');
        Gate::define('orders.can-delete', OrdersGate::class.'@canDelete');
        Gate::define('orders.can-pay', OrdersGate::class.'@canPay');
        Gate::define('orders.can-pay-with-points', OrdersGate::class.'@canPayWithPoints');
        Gate::define('orders.can-pay-in-any-form', OrdersGate::class.'@canPayInAnyForm');
        Gate::define('orders.can-manage-files', OrdersGate::class.'@canManageFiles');

        Gate::define('tasks.has-access', TasksGate::class.'@hasAccess');

        Gate::define('transactions.has-access', TransactionsGate::class.'@hasAccess');
        Gate::define('transactions.can-delete', TransactionsGate::class.'@canDelete');
        Gate::define('transactions.can-edit', TransactionsGate::class.'@canEdit');

        Gate::define('points.has-access', PointsGate::class.'@hasAccess');
        Gate::define('points.can-delete', PointsGate::class.'@canDelete');
        Gate::define('points.can-edit', PointsGate::class.'@canEdit');

        Gate::define('invoices.has-access', InvoicesGate::class.'@hasAccess');
        Gate::define('invoices.can-show', InvoicesGate::class.'@canShow');

        Gate::define('deposits.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('promotion-codes.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('costs.has-access', CostsGate::class.'@hasAccess');
        Gate::define('costs.can-create', CostsGate::class.'@canCreate');
        Gate::define('costs.can-edit', CostsGate::class.'@canEdit');
        Gate::define('costs.can-delete', CostsGate::class.'@canDelete');
        Gate::define('costs.can-export', CostsGate::class.'@canExport');
        Gate::define('costs.can-import', CostsGate::class.'@canImport');

        Gate::define('notices.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('drivers.has-access', DriversGate::class.'@hasAccess');
        Gate::define('drivers.can-delete', BasicGate::class.'@isSuperAdmin');

        Gate::define('driver-vehicles.can-manage-images', DriverVehicleGate::class.'@canManageImages');

        Gate::define('settings.has-access', BasicGate::class.'@isSuperAdmin');

        Gate::define('dealers.can-assign-client', DealersGate::class.'@canAssignClient');

        Gate::define('dealer-clients.can-show', DealerClientsGate::class.'@canShow');
        Gate::define('dealer-clients.can-edit', DealerClientsGate::class.'@canEdit');
        Gate::define('dealer-clients.can-delete', DealerClientsGate::class.'@canDelete');

        Gate::define('user-business-cards.can-view-files', UserBusinessCardsGate::class.'@canViewFiles');
        Gate::define('user-business-cards.can-remove-files', UserBusinessCardsGate::class.'@canRemoveFiles');

        Gate::define('companies.has-access', BasicGate::class.'@isSuperAdmin');
    }
}
