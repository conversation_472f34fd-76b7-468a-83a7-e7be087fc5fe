<?php

namespace App\Providers;

use App;
use App\Http\Middleware\AdminPanelSetLocale;
use App\Models\Company;
use App\Models\Country;
use App\Models\Driver;
use App\Models\Message;
use App\Models\Notice;
use App\Models\Order;
use App\Models\Parcel;
use App\Models\Shop;
use App\Models\Task;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleReport;
use App\Project\Notification\Notification;
use App\Project\Notification\NotificationsService;
use App\Project\Vehicle\Enums\VehicleStatuses;
use App\Repositories\PromotionCodeRepository;
use Illuminate\Contracts\Routing\UrlGenerator as UrlGeneratorContract;
use Illuminate\Cookie\CookieValuePrefix;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Application;
use Illuminate\Pagination\Paginator;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Request;
use Spatie\Translatable\Facades\Translatable;

class AppServiceProvider extends ServiceProvider
{
    public function boot(UrlGenerator $url): void
    {
        Vite::macro('image', fn (string $asset): string => Vite::asset("resources/images/{$asset}"));

        Translatable::fallback(fallbackAny: true);

        /** @var string $requestLocale */
        $requestLocale = Request::segment(1) ?? config('app.default_locale');

        if (in_array($requestLocale, config('app.available_locales'), true)) {
            App::setLocale($requestLocale);
        }

        $url->defaults(['locale' => $requestLocale]);

        // For dashboard generated links in PA
        $this->app->rebinding('request', function (Application $app): void {
            $app->get(UrlGeneratorContract::class)
                ->defaults(['locale' => request()->hasCookie(AdminPanelSetLocale::COOKIE_KEY) ?
                    CookieValuePrefix::remove(decrypt(request()->cookie(AdminPanelSetLocale::COOKIE_KEY), false)) : config('app.default_locale')]);
        });

        if (app()->environment(['production', 'staging'])) {
            $url->forceScheme('https');
        }

        view()->composer(['layouts.landing'], function ($view): void {
            $countries = Country::all();
            $ordersSum = Cache::remember(
                'orders_sum',
                6000,
                function (): float {
                    $sum = Order::sum('price') / 100;

                    return round($sum * 1.1);
                }
            );

            $view->with(compact('countries', 'ordersSum'));
        });

        view()->composer([
            'admin.users._form',
            'calculators.parcels',
        ], function ($view): void {
            $countries = Country::query()->get()->pluck('name', 'id');

            $view->with(compact('countries'));
        });

        view()->composer(['admin.partials.nav'], function ($view): void {
            $notifications = Cache::remember(
                'admin_notifications',
                600,
                function (): array {
                    $notifications = [];
                    $notifications['parcels'] = Parcel::count();
                    $notifications['vehicles'] = Vehicle::where(['status' => VehicleStatuses::ADDED])->count();
                    $notifications['orders'] = Order::count();
                    $notifications['users'] = User::count();
                    $notifications['messages'] = DB::query()
                        ->fromSub(
                            query: Message::where(['archived' => 0])
                                ->groupBy(['messageable_id', 'messageable_type'])
                                ->where(function (Builder $query): void {
                                    $query->has('parcels')
                                        ->orHas('orders')
                                        ->orHas('vehicles');
                                })->getQuery(),
                            as: 'count'
                        )->count();
                    $notifications['shops'] = Shop::count();
                    $notifications['tasks'] = Task::whereNull('completed_at')->count();
                    $notifications['promotion_codes'] = $this->app->make(PromotionCodeRepository::class)->getValid(
                    )->count();
                    $notifications['notices'] = Notice::count();
                    $notifications['drivers'] = Driver::count();
                    $notifications['vehicle_reports'] = VehicleReport::count();
                    $notifications['companies'] = Company::count();

                    return $notifications;
                }
            );

            $view->with(compact('notifications'));
        });

        view()->composer(['partials.header'], function ($view): void {
            if (! auth()->check()) {
                return;
            }

            /** @var NotificationsService $notificationsService */
            $notificationsService = $this->app->make(NotificationsService::class);
            $notifications = $notificationsService->forUser(auth()->user());
            $unreadNotificationsCount = $notifications->filter(
                fn (Notification $notification): bool => $notification->displayedAt() === null
            )->count();

            $view->with(compact('notifications', 'unreadNotificationsCount'));
        });

        Paginator::useBootstrapThree();
    }
}
