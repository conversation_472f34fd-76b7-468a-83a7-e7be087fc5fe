<?php

namespace App\Providers;

use App\Models\DriverVehicle;
use App\Models\Order;
use App\Models\Vehicle;
use App\Observers\DestinationAgencyVehicleProvidedObserver;
use App\Observers\OrderObserver;
use App\Observers\TransportPaidDriverNotificationObserver;
use App\Observers\VehicleStatusChangedUserNotificationObserver;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Order::observe(OrderObserver::class);
        Vehicle::observe([
            DestinationAgencyVehicleProvidedObserver::class,
            VehicleStatusChangedUserNotificationObserver::class,
        ]);
        DriverVehicle::observe(TransportPaidDriverNotificationObserver::class);
    }
}
