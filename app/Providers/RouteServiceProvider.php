<?php

declare(strict_types=1);

namespace App\Providers;

use App\Http\Middleware\AdminPanelSetLocale;
use App\Http\Middleware\SetLocaleFromHeader;
use App\Http\Middleware\VerifyApiAccess;
use App\Models\City;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        parent::boot();

        Route::model('city', City::class);
    }

    public function map(Router $router): void
    {
        $router->group([
            'middleware' => 'web',
        ], function (): void {
            require base_path('routes/dashboard.php');
        });

        $router->group([
            'as' => 'admin.',
            'prefix' => 'admin',
            'middleware' => ['web', 'access-pa', AdminPanelSetLocale::class],
        ], function (): void {
            require base_path('routes/admin.php');
        });

        $router->group([
            'as' => 'api.',
            'prefix' => 'api',
            'middleware' => ['api', VerifyApiAccess::class, SetLocaleFromHeader::class],
        ], function (): void {
            require base_path('routes/api.php');
        });
    }
}
