<?php

namespace App\Models;

use App\Models\Traits\JsonableTrait;
use App\Project\Core\Eloquent\BaseModel;
use Crypt;

/**
 * @mixin IdeHelperOrderShopping
 */
class OrderShopping extends BaseModel
{
    /**
     * @var string
     */
    final public const ONLINE_CART = 'online_cart';

    /**
     * @var string
     */
    final public const SHOP_LINKS = 'links';

    /**
     * @var string
     */
    final public const GIFT_CARD = 'gift_card';

    /**
     * @var string
     */
    final public const PREPAID_CARD = 'prepaid_card';

    // For old order shopping items
    /**
     * @var string
     */
    final public const UNKNOWN = 'unknown';

    /**
     * @var string[]
     */
    protected $fillable = [
        'shop',
        'purchaser',
        'scenario',
    ];

    protected array $moneyShopAttributes = [
        'prepaid_card_amount',
        'gift_card_amount',
    ];

    use JsonableTrait;

    /**
     * @return mixed
     */
    public function getShopAttribute()
    {
        $value = $this->_fromJson($this->attributes['shop']);
        if (is_array($value)) {
            if (isset($value['login']) && ! empty($value['login'])) {
                $value['login'] = Crypt::decrypt($value['login']);
            }

            if (isset($value['password']) && ! empty($value['password'])) {
                $value['password'] = Crypt::decrypt($value['password']);
            }

            foreach ($this->moneyShopAttributes as $moneyShopAttribute) {
                if (! isset($value[$moneyShopAttribute])) {
                    continue;
                }

                $value[$moneyShopAttribute] = '$ '.price_to_string($value[$moneyShopAttribute], 2, '.', '');
            }
        }

        return $value;
    }

    public function setShopAttribute($value): void
    {
        if (is_array($value)) {
            if (isset($value['login']) && ! empty($value['login'])) {
                $value['login'] = Crypt::encrypt($value['login']);
            }

            if (isset($value['password']) && ! empty($value['password'])) {
                $value['password'] = Crypt::encrypt($value['password']);
            }

            foreach ($this->moneyShopAttributes as $moneyShopAttribute) {
                if (! isset($value[$moneyShopAttribute])) {
                    continue;
                }

                $value[$moneyShopAttribute] = string_to_price($value[$moneyShopAttribute]);
            }
        }

        $this->attributes['shop'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getPurchaserAttribute()
    {
        return $this->_fromJson($this->attributes['purchaser']);
    }

    public function setPurchaserAttribute($value): void
    {
        $this->attributes['purchaser'] = json_encode($value, JSON_THROW_ON_ERROR);
    }
}
