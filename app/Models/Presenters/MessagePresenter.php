<?php

declare(strict_types=1);

namespace App\Models\Presenters;

use App\Models\Message;
use App\Project\Core\Presenters\BaseModelPresenter;

/**
 * @property Message $model
 * @mixin Message
 */
class MessagePresenter extends BaseModelPresenter
{
    public function messageableToArray(): array
    {
        $messageable = $this->messageable;
        $description = match (true) {
            $messageable instanceof OrderPresenter => $messageable->parcels->implode('description', ', '),
            $messageable instanceof ParcelPresenter => $messageable->description,
            $messageable instanceof VehiclePresenter => $messageable->vehicle_description,
            default => '',
        };
        $order = $messageable instanceof OrderPresenter ? $messageable : $messageable->order;

        return [
            'id' => $messageable->getKey(),
            'date' => (string) $messageable->created_at,
            'type' => $messageable instanceof ParcelPresenter ? 'parcels' : $order->type->slug,
            'number' => $messageable instanceof ParcelPresenter ? $messageable->getKey() : $order->getFormattedId(),
            'messageableId' => $this->messageable_id,
            'messageableType' => $this->messageable_type,
            'link' => $messageable->dashboardShowUrl(),
            'description' => $description,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getKey(),
            'content' => $this->content,
            'type' => $this->type,
            'date' => (string) $this->created_at,
        ];
    }
}
