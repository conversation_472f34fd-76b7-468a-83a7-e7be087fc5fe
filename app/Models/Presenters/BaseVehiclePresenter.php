<?php

declare(strict_types=1);

namespace App\Models\Presenters;

use App\Models\UserVehicle;
use App\Models\Vehicle;
use App\Project\Core\Presenters\BaseModelPresenter;
use App\Project\Storage\CloudStorage;

/**
 * @mixin Vehicle|UserVehicle
 * @property Vehicle|UserVehicle $model
 * @method Vehicle|UserVehicle model()
 */
abstract class BaseVehiclePresenter extends BaseModelPresenter
{
    abstract public function displayId(): int;

    abstract public function vehicleDescription(): string;

    abstract public function terminalDisplayFormat(): ?string;

    abstract public function shareInfoUrl(): string;

    abstract public function sendShareableUrl(): string;

    abstract public function setShareLinkDisabledUrl(): string;

    abstract public function assignClientUrl(): string;

    abstract public function editUrl(): ?string;

    /**
     * @return array<string>
     */
    abstract public function imagesForDealerPlatform(): array;

    abstract public function statusForVehicleResource(): string;

    abstract public function statusDisplayFormat(?string $locale = null): string;

    public function imagesUrls(): array
    {
        $images = $this->model->allImages();

        return array_map(
            fn (string $image, string $directory): string => CloudStorage::viewFileRoute(
                fileName: $image,
                model: $this->model,
                attributeName: $directory
            ),
            array_keys($images),
            $images,
        );
    }

    public function thumbnailImageUrl(): ?string
    {
        return $this->model->thumbnailImageUrl();
    }

    public function shareableUrl(): ?string
    {
        return $this->share_uuid ? config('dealer.domain').$this->share_uuid : null;
    }

    public function vinShort(): ?string
    {
        return $this->vin_number ? substr((string) $this->vin_number, -6) : null;
    }
}
