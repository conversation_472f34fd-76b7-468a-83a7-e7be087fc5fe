<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Interfaces\HasCloudFiles;
use App\Models\Presenters\CityPresenter;
use App\Models\Presenters\DriverPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use App\Project\Driver\Enums\TaxNumberType;
use Carbon\Carbon;
use Gate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;

/**
 * @mixin IdeHelperDriver
 */
class Driver extends BaseModel implements HasCloudFiles, PresentableModel
{
    use Notifiable;

    protected $casts = [
        'tax_number_type' => TaxNumberType::class,
    ];

    public function addressDisplayName(): string
    {
        return implode('<br/>', array_filter([
            $this->address,
            $this->city ? (new CityPresenter($this->city))->displayFormat() : null,
            $this->zip_code,
        ]));
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('drivers.has-access');
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('drivers.has-access');
    }

    public function isFilePublic(string $attributeName): bool
    {
        return false;
    }

    public function vehicles(): BelongsToMany
    {
        return $this->belongsToMany(Vehicle::class, 'driver_vehicles')
            ->using(DriverVehicle::class)
            ->withPivot(['transport_price', 'paid_at', 'vehicle_damages_status', 'vehicle_damages_images', 'comment'])
            ->withTimestamps();
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * @param Carbon[] $years
     */
    public function scopeWithProfitForYears(Builder $query, array $years): Builder
    {
        foreach ($years as $year) {
            $query->withColumn(
                'vehicles',
                'transport_price',
                "profit_{$year->format('Y')}",
                function (Builder $query) use ($year): void {
                    $query->select(DB::raw('SUM(transport_price)'))
                        ->whereBetween('paid_at', [$year, $year->copy()->endOfYear()]);
                }
            );
        }

        return $query;
    }

    public function presenterClass(): string
    {
        return DriverPresenter::class;
    }
}
