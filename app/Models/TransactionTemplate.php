<?php

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use Spatie\Translatable\HasTranslations;

/**
 * @mixin IdeHelperTransactionTemplate
 */
class TransactionTemplate extends BaseModel
{
    use HasTranslations;

    /**
     * @var string
     */
    final public const ORDER_PAYMENT_TEMPLATE_SLUG = 'order_template';

    /**
     * @var string
     */
    final public const ORDER_POINTS_TEMPLATE_SLUG = 'points_order_template';

    /**
     * @var string
     */
    final public const ORDER_DISCOUNT_TEMPLATE_SLUG = 'points_order_discount_template';

    /**
     * @var string
     */
    final public const USER_REGISTRATION_POINTS_TEMPLATE_SLUG = 'points_user_registration_template';

    /**
     * @var string
     */
    final public const TRANSACTION_TYPE = 'transaction';

    /**
     * @var string
     */
    final public const POINTS_TYPE = 'points';

    /**
     * @var string
     */
    final public const DEPOSIT_TYPE = 'deposit';

    public array $translatable = ['title'];
}
