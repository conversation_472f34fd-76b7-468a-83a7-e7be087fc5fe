<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Interfaces\HasCloudFiles;
use App\Project\Core\Eloquent\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Gate;

/**
 * @mixin IdeHelperCost
 */
class Cost extends BaseModel implements HasCloudFiles
{
    use SoftDeletes;

    public function formattedAmount(): string
    {
        return '$ '.price_to_string($this->amount);
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('costs.has-access');
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('costs.has-access');
    }

    public function isFilePublic(string $attributeName): bool
    {
        return false;
    }
}
