<?php

namespace App\Models\Interfaces;

use App\Models\Message;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property Collection|Message[] $messages
 * @property User $user
 * @mixin Model
 */
interface Messageable extends DashboardShowable
{
    public function messages(): MorphMany;

    public function user(): BelongsTo;

    public function conversationUrl(): string;
}
