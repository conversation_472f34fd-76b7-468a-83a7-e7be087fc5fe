<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Presenters\UserBalancePresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use App\Project\Transactional\TransactionalType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperUserBalance
 */
class UserBalance extends BaseModel implements PresentableModel
{
    protected $casts = [
        'type' => TransactionalType::class,
    ];

    public function hasDebit(): bool
    {
        return $this->balance < 0;
    }

    public function formattedBalance(): string
    {
        return '$ '.price_to_string($this->balance);
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return BelongsTo<Company, self>
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function presenterClass(): string
    {
        return UserBalancePresenter::class;
    }
}
