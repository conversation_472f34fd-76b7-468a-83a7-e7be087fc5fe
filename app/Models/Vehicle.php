<?php

namespace App\Models;

use App\Models\Interfaces\HasCloudFiles;
use App\Models\Interfaces\Messageable;
use App\Models\Presenters\VehiclePresenter;
use App\Models\Traits\HasMessageUsersRelation;
use App\Models\Traits\MessageableTrait;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use App\Project\Storage\CloudStorage;
use App\Project\Vehicle\Enums\VehicleSellerTypes;
use App\Project\Vehicle\Enums\VehicleStatuses;
use Gate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperVehicle
 */
class Vehicle extends BaseModel implements HasCloudFiles, Messageable, PresentableModel
{
    use HasMessageUsersRelation, MessageableTrait, SoftDeletes;

    protected $casts = [
        'bill_of_sale' => 'array',
        'bill_of_landing' => 'array',
        'hazmat_document' => 'array',
        'title_file' => 'array',
        'images' => 'array',
        'images_collection' => 'array',
        'images_shipping' => 'array',
        'images_user' => 'array',
        'picked_from_customs_agency' => 'boolean',
        'user_pay_for_vehicle' => 'boolean',
        'is_electric_or_hybrid' => 'boolean',
        'shared_to_client' => 'boolean',
        'share_link_disabled' => 'boolean',
        'released' => 'boolean',
        'payment_date' => 'datetime',
        'receipt_date' => 'datetime',
        'eta' => 'datetime',
        'collection_date' => 'datetime',
        'terminal_paid_at' => 'datetime',
        'released_at' => 'datetime',
        'customs_agency_completed_at' => 'datetime',
        'tracking_completed_at' => 'datetime',
        'send_title_to_customs' => 'boolean',
        'is_premium_loading' => 'boolean',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function terminal(): BelongsTo
    {
        return $this->belongsTo(VehicleTerminal::class, 'vehicle_terminal_id');
    }

    public function shippingLine(): BelongsTo
    {
        return $this->belongsTo(VehicleShippingLine::class, 'vehicle_shipping_line_id');
    }

    public function destinationAgency(): BelongsTo
    {
        return $this->belongsTo(VehicleDestinationAgency::class, 'vehicle_destination_agency_id');
    }

    public function deliveryLocation(): BelongsTo
    {
        return $this->belongsTo(VehicleDeliveryLocation::class, 'vehicle_delivery_location_id');
    }

    public function auction(): BelongsTo
    {
        return $this->belongsTo(VehicleAuctionLocation::class, 'vehicle_auction_location_id');
    }

    public function recipient(): HasOne
    {
        return $this->hasOne(VehicleRecipient::class);
    }

    public function driverVehicles(): HasMany
    {
        return $this->hasMany(DriverVehicle::class);
    }

    public function parcels(): HasMany
    {
        return $this->hasMany(Parcel::class);
    }

    public function dealerClient(): BelongsTo
    {
        return $this->belongsTo(DealerClient::class);
    }

    /**
     * @param Builder<Vehicle> $query
     * @return Builder<Vehicle>
     */
    public function scopeDeliveredByNewCompanies(Builder $query): Builder
    {
        $columnName = $this->getTable().'.order_id';

        return $query->where($columnName, '>=', Order::NEW_COMPANIES_FROM_ID)
            ->whereNotIn($columnName, Order::NEW_COMPANIES_ADDITIONAL_IDS);
    }

    public function getPickupLocationString(): ?string
    {
        if ($this->auction) {
            return $this->auction->getPickupLocationString();
        }

        return $this->vehicle_auction_location_company.' '.$this->vehicle_auction_location_address;
    }

    public function sellerTypeHumanReadable(): ?string
    {
        return $this->seller_type === null ? null : VehicleSellerTypes::humanReadable($this->seller_type);
    }

    public function deliveryLocationDisplayName(): string
    {
        return $this->deliveryLocation->name ?? __('vehicles.delivery_locations.do_not_ship');
    }

    public function thumbnailImageUrl(): ?string
    {
        $images = $this->allImages();
        if (empty($images)) {
            return null;
        }

        return CloudStorage::viewFileRoute(
            fileName: array_key_first($images),
            model: $this,
            attributeName: $images[array_key_first($images)]
        );
    }

    public function hasImages(): bool
    {
        return ! empty($this->allImages());
    }

    /**
     * @return array<string, string>
     */
    public function allImages(): array
    {
        return array_filter(
            [...array_fill_keys($this->images_collection ?? [], 'images_collection'), ...array_fill_keys($this->images ?? [], 'images'), ...array_fill_keys($this->images_shipping ?? [], 'images_shipping')]
        );
    }

    public function isReleased(): bool
    {
        return $this->released || $this->status === VehicleStatuses::PROVIDED;
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('vehicles.can-view-files', $this);
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('vehicles.can-remove-files', $this);
    }

    public function isFilePublic(string $attributeName): bool
    {
        return in_array($attributeName, ['images', 'images_collection']);
    }

    public function presenterClass(): string
    {
        return VehiclePresenter::class;
    }

    public function dashboardShowUrl(): ?string
    {
        return route('vehicles.show', $this);
    }

    public function description(): string
    {
        $description = $this->vehicle_description;
        $description .= ' - '.mb_strtoupper($this->deliveryLocationDisplayName());

        if (! empty($this->vin_number)) {
            $description .= ' '.__('admin/vehicles.edit.header.vin').': '.$this->vin_number;
        }

        if ($this->vehicle_destination_agency_id && $this->destinationAgency) {
            $description .= ' - '.$this->destinationAgency->name;
        }

        if ($this->is_electric_or_hybrid) {
            $description .= ' - HYBRID';
        }

        if ($this->is_premium_loading) {
            $description .= ' - PREMIUM LOADING (TWO CARS FLAT)';
        }

        return $description;
    }

    public function conversationUrl(): string
    {
        return route('admin.vehicles.messages', $this);
    }
}
