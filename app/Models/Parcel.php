<?php

namespace App\Models;

use App\Models\Interfaces\HasCloudFiles;
use App\Models\Interfaces\Messageable;
use App\Models\Presenters\ParcelPresenter;
use App\Models\Traits\HasMessageUsersRelation;
use App\Models\Traits\MessageableTrait;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Gate;

/**
 * @mixin IdeHelperParcel
 */
class Parcel extends BaseModel implements HasCloudFiles, Messageable, PresentableModel
{
    use HasMessageUsersRelation, MessageableTrait;

    protected $casts = [
        'images' => 'array',
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'warehouse_id',
        'user_id',
        'order_number',
        'tracking_number',
        'sender',
        'carrier',
        'added_by_admin',
        'send_back',
        'description',
        'vehicle_id',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function canAuthUserEdit(): bool
    {
        return ! $this->order_id && ! $this->added_by_admin && $this->user_id = auth()->user()->id;
    }

    public function canAuthUserDelete(): bool
    {
        return ! $this->order_id && ! $this->added_by_admin && $this->user_id = auth()->user()->id;
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('parcels.can-view-files', $this);
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return false;
    }

    public function isFilePublic(string $attributeName): bool
    {
        return false;
    }

    public function presenterClass(): string
    {
        return ParcelPresenter::class;
    }

    public function dashboardShowUrl(): ?string
    {
        return route('warehouse.parcels.show', [$this->warehouse->slug, $this->id]);
    }

    public function description(): string
    {
        return $this->tracking_number ?? '';
    }

    public function conversationUrl(): string
    {
        return route('admin.parcels.edit', $this).'#messages';
    }
}
