<?php

namespace App\Models;

use App\Models\Traits\JsonableTrait;
use App\Project\Core\Eloquent\BaseModel;

/**
 * @mixin IdeHelperOrderShipping
 */
class OrderShipping extends BaseModel
{
    use JsonableTrait;

    /**
     * @var string[]
     */
    protected $fillable = [
        'from',
        'items',
        'shipper',
        'purchaser',
    ];

    /**
     * @return mixed
     */
    public function getFromAttribute()
    {
        return $this->_fromJson($this->attributes['from']);
    }

    public function setFromAttribute($value): void
    {
        $this->attributes['from'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getItemsAttribute()
    {
        return $this->_fromJson($this->attributes['items']);
    }

    public function setItemsAttribute($value): void
    {
        $this->attributes['items'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getShipperAttribute()
    {
        return $this->_fromJson($this->attributes['shipper']);
    }

    public function setShipperAttribute($value): void
    {
        $this->attributes['shipper'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getPurchaserAttribute()
    {
        return $this->_fromJson($this->attributes['purchaser']);
    }

    public function setPurchaserAttribute($value): void
    {
        $this->attributes['purchaser'] = json_encode($value, JSON_THROW_ON_ERROR);
    }
}
