<?php

namespace App\Models;

use App\Models\Presenters\OrderPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use <PERSON><PERSON>\Translatable\HasTranslations;

/**
 * @mixin IdeHelperOrderStatus
 */
class OrderStatus extends BaseModel implements PresentableModel
{
    use HasTranslations;

    /**
     * @var string
     */
    final public const WAITING_SLUG = 'oczekuje-na-wycene';

    /**
     * @var string
     */
    final public const PRICED_SLUG = 'wycenione';

    /**
     * @var string
     */
    final public const PAID_SLUG = 'oplacone';

    /**
     * @var string
     */
    final public const IN_PROGRESS_SLUG = 'w-trakcie-realizacji';

    /**
     * @var string
     */
    final public const FINISHED_SLUG = 'zakonczone';

    /**
     * @var string[]
     */
    final public const UNPAID_STATUSES = [
        self::WAITING_SLUG,
        self::PRICED_SLUG,
        self::IN_PROGRESS_SLUG,
    ];

    /**
     * @var string[]
     */
    final public const PAID_STATUSES = [
        self::PAID_SLUG,
        self::FINISHED_SLUG,
    ];

    /**
     * @var string[]
     */
    final public const INVOICE_GENERATE_STATUSES = [
        self::PRICED_SLUG,
        self::PAID_SLUG,
        self::FINISHED_SLUG,
    ];

    public array $translatable = ['name'];

    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(new class implements Scope
        {
            public function apply(Builder $builder, Model $model): void
            {
                $builder->orderBy($model->getTable().'.order', 'ASC');
            }

            public function remove(Builder $builder, Model $model): void
            {
            }
        }
        );
    }

    public function isUnpaid(): bool
    {
        return in_array($this->slug, self::UNPAID_STATUSES);
    }

    public function isPaid(): bool
    {
        return in_array($this->slug, self::PAID_STATUSES);
    }

    public function shouldGenerateInvoice(): bool
    {
        return in_array($this->slug, self::INVOICE_GENERATE_STATUSES);
    }

    public function isWaitingForPricing(): bool
    {
        return $this->slug === self::WAITING_SLUG;
    }

    /**
     * @param Builder<OrderStatus> $query
     * @return Builder<OrderStatus>
     */
    public function scopePaid(Builder $query, bool $isPaid = true): Builder
    {
        return $isPaid ?
            $query->whereIn('slug', self::PAID_STATUSES) :
            $query->whereNotIn('slug', self::PAID_STATUSES);
    }

    public function presenterClass(): string
    {
        return OrderPresenter::class;
    }
}
