<?php

namespace App\Models;

use App\Models\Presenters\CityPresenter;
use App\Models\Presenters\CollectionPresenters\CitiesCollectionPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\ModelWithCollectionPresenter;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @mixin IdeHelperCity
 */
class City extends BaseModel implements ModelWithCollectionPresenter, PresentableModel
{
    public function vehicleCalculationPrices(): MorphMany
    {
        return $this->morphMany(VehicleCalculationPrice::class, 'calculable');
    }

    public function belongingToVehicleCalculationPrices(): HasMany
    {
        return $this->hasMany(VehicleCalculationPrice::class, 'city_id');
    }

    public function vehicleDeliveryLocationInformation(): HasMany
    {
        return $this->hasMany(VehicleDeliveryLocationInformation::class);
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function presenterClass(): string
    {
        return CityPresenter::class;
    }

    public function collectionPresenterClass(): string
    {
        return CitiesCollectionPresenter::class;
    }
}
