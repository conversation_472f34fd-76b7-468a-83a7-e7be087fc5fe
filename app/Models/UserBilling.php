<?php

namespace App\Models;

use App\Models\Presenters\UserBillingPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperUserBilling
 */
class UserBilling extends BaseModel implements PresentableModel
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function presenterClass(): string
    {
        return UserBillingPresenter::class;
    }
}
