<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Presenters\DealerClientPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperDealerClient
 */
class DealerClient extends BaseModel implements PresentableModel
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class);
    }

    public function presenterClass(): string
    {
        return DealerClientPresenter::class;
    }
}
