<?php

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperShopCategory
 */
class ShopCategory extends BaseModel
{
    public function shops(): HasMany
    {
        return $this->hasMany(Shop::class);
    }

    public function translations(): HasMany
    {
        return $this->hasMany(ShopCategoryTranslation::class);
    }

    public function getSlugAttribute(): ?string
    {
        $translation = $this->translations->where('locale', app()->getLocale())->first();

        return $translation->slug ?? null;
    }

    public function getNameAttribute(): ?string
    {
        $translation = $this->translations->where('locale', app()->getLocale())->first();

        return $translation->name ?? null;
    }

    /**
     * @return mixed
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }
}
