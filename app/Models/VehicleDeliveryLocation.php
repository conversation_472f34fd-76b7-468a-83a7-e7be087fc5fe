<?php

namespace App\Models;

use App\Models\Presenters\VehicleDeliveryLocationPresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Scope;

/**
 * @mixin IdeHelperVehicleDeliveryLocation
 */
class VehicleDeliveryLocation extends BaseModel implements PresentableModel
{
    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(
            new class implements Scope
            {
                public function apply(Builder $builder, Model $model): void
                {
                    $builder->orderBy($model->getTable().'.name', 'ASC');
                }
            }
        );
    }

    public function vehicleCalculationPrice(): MorphMany
    {
        return $this->morphMany(VehicleCalculationPrice::class, 'calculable');
    }

    public function vehicleDeliveryLocationInformation(): HasMany
    {
        return $this->hasMany(VehicleDeliveryLocationInformation::class);
    }

    public function vehicleDestinationAgencies(): BelongsToMany
    {
        return $this->belongsToMany(VehicleDestinationAgency::class, 'vehicle_delivery_location_destination_agency')
            ->withTimestamps();
    }

    public function presenterClass(): string
    {
        return VehicleDeliveryLocationPresenter::class;
    }
}
