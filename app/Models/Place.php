<?php

namespace App\Models;

use App\Models\Traits\HasCountryTrait;
use App\Project\Core\Eloquent\BaseModel;

/**
 * @mixin IdeHelperPlace
 */
class Place extends BaseModel
{
    use HasCountryTrait;

    /**
     * @var string[]
     */
    protected $fillable = [
        'name',
        'address',
        'postcode',
        'city',
        'country_id',
        'lat',
        'lon',
    ];

    public static array $rules = [
        'lat' => 'required',
        'lon' => 'required',
    ];

    /**
     * Get shorthand details for place switcher
     */
    public function getShorthandAttribute(): string
    {
        return $this->name.', '.$this->city;
    }
}
