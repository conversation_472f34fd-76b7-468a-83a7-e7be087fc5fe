<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Interfaces\HasCloudFiles;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Storage\CloudStorage;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Gate;

/**
 * @mixin IdeHelperVehicleReport
 */
class VehicleReport extends BaseModel implements HasCloudFiles
{
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function filePath(): string
    {
        return CloudStorage::viewFileRoute(
            fileName: $this->file_name,
            model: $this,
            attributeName: 'file_name',
        );
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('vehicle-reports.can-show', [$this]);
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return false;
    }

    public function isFilePublic(string $attributeName): bool
    {
        return false;
    }
}
