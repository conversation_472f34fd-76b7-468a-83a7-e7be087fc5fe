<?php

namespace App\Models;

use App\Models\Traits\JsonableTrait;
use App\Project\Core\Eloquent\BaseModel;

/**
 * @mixin IdeHelperOrderPickup
 */
class OrderPickup extends BaseModel
{
    use JsonableTrait;

    /**
     * @var string[]
     */
    protected $fillable = [
        'shipper',
        'vehicles',
        'purchaser',
    ];

    /**
     * @return mixed
     */
    public function getShipperAttribute()
    {
        return $this->_fromJson($this->attributes['shipper']);
    }

    public function setShipperAttribute($value): void
    {
        $this->attributes['shipper'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getVehiclesAttribute()
    {
        return $this->_fromJson($this->attributes['vehicles']);
    }

    public function setVehiclesAttribute($value): void
    {
        $this->attributes['vehicles'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getPurchaserAttribute()
    {
        return $this->_fromJson($this->attributes['purchaser']);
    }

    public function setPurchaserAttribute($value): void
    {
        $this->attributes['purchaser'] = json_encode($value, JSON_THROW_ON_ERROR);
    }
}
