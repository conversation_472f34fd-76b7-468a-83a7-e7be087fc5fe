<?php

namespace App\Models;

use App\Models\Traits\JsonableTrait;
use App\Project\Core\Eloquent\BaseModel;

/**
 * @mixin IdeHelperOrderShipment
 */
class OrderShipment extends BaseModel
{
    use JsonableTrait;

    /**
     * @var array<string, string>
     */
    final public const SHIPPING_TYPES = [
        'Morska pod drzwi' => 'Morska pod drzwi',
        'Lotnicza pod drzwi' => 'Lotnicza pod drzwi',
        'Lotnicza z odbiorem w magazynie w Warszawie' => 'Lotnicza z odbiorem w magazynie w Warszawie',
        'Morska z odbiorem w magazynie w Warszawie' => 'Morska z odbiorem w magazynie w Warszawie',
    ];

    /**
     * @var string[]
     */
    protected $fillable = [
        'sender',
        'recipient',
        'form',
    ];

    /**
     * @return mixed
     */
    public function getSenderAttribute()
    {
        return $this->_fromJson($this->attributes['sender']);
    }

    public function setSenderAttribute($value): void
    {
        $this->attributes['sender'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getRecipientAttribute()
    {
        return $this->_fromJson($this->attributes['recipient']);
    }

    public function setRecipientAttribute($value): void
    {
        $this->attributes['recipient'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getFormAttribute()
    {
        return $this->_fromJson($this->attributes['form']);
    }

    public function setFormAttribute($value): void
    {
        $this->attributes['form'] = json_encode($value, JSON_THROW_ON_ERROR);
    }

    /**
     * @return mixed
     */
    public function getFilesAttribute()
    {
        return $this->_fromJson($this->attributes['files']);
    }

    public function setFilesAttribute($value): void
    {
        $this->attributes['files'] = json_encode($value, JSON_THROW_ON_ERROR);
    }
}
