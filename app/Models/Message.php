<?php

namespace App\Models;

use App\Models\Presenters\MessagePresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property Order|Parcel|Vehicle $messageable
 * @mixin IdeHelperMessage
 */
class Message extends BaseModel implements PresentableModel
{
    final public const QUESTION_TYPE = 'question';
    final public const ANSWER_TYPE = 'answer';

    /**
     * @var string[]
     */
    protected $fillable = [
        'content',
        'type',
        'author_id',
    ];

    public function messageable(): MorphTo
    {
        return $this->morphTo();
    }

    public function parcels(): BelongsTo
    {
        return $this->belongsTo(Parcel::class, 'messageable_id')
            ->where('messages.messageable_type', Parcel::class);
    }

    public function orders(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'messageable_id')
            ->where('messages.messageable_type', Order::class);
    }

    public function vehicles(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class, 'messageable_id')
            ->where('messages.messageable_type', Vehicle::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function isQuestion(): bool
    {
        return $this->type === self::QUESTION_TYPE;
    }

    public function isAnswer(): bool
    {
        return $this->type === self::ANSWER_TYPE;
    }

    public function scopeForUser(Builder $query, User $user): void
    {
        $query->where(function (Builder $query) use ($user): void {
            $query->where('author_id', $user->getKey())
                ->orWhere('type', Message::ANSWER_TYPE);
        });
    }

    public function presenterClass(): string
    {
        return MessagePresenter::class;
    }
}
