<?php

declare(strict_types=1);

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperPromotionCode
 */
class PromotionCode extends BaseModel
{
    /**
     * @var string
     */
    final public const MULTIPLE_ADDITION_TYPE = 'multiply';

    protected $casts = [
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
