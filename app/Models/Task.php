<?php

declare(strict_types=1);

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperTask
 */
class Task extends BaseModel
{
    protected $casts = [
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * @return array<string, mixed>
     */
    public function transform(): array
    {
        return [
            'id' => $this->id,
            'order' => $this->order,
            'title' => $this->title,
            'content' => $this->content,
            'completed_at' => $this->completed_at ? (string) $this->completed_at : null,
            'due_date' => $this->due_date ? $this->due_date->toIso8601String() : null,
        ];
    }

    public function images(): HasMany
    {
        return $this->hasMany(TaskImage::class);
    }
}
