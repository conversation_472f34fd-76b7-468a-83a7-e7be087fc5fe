<?php

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use App\Project\Transactional\TransactionTitlePresenter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property User $user
 * @property string|null $title
 */
abstract class BaseTransactionalModel extends BaseModel
{
    use SoftDeletes;

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(TransactionTemplate::class);
    }

    public function title(): string
    {
        return (new TransactionTitlePresenter($this))->title();
    }

    public function hasPositiveAmount(): bool
    {
        return $this->amount >= 0;
    }

    public function formattedAmount(): string
    {
        return '$ '.price_to_string($this->amount);
    }
}
