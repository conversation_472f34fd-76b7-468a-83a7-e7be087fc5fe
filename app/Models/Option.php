<?php

declare(strict_types=1);

namespace App\Models;

use App\Project\Core\Eloquent\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;

/**
 * @mixin IdeHelperOption
 */
class Option extends BaseModel
{
    /** @var string[] */
    protected $casts = [
        'value' => 'array',
    ];

    public static function syncOptions(array $options, ?Model $optionable = null, array $defaultValues = []): void
    {
        $filteredOptions = array_filter(
            $options,
            fn (mixed $value, string $key): bool => ($defaultValues[$key] ?? null) !== $value,
            ARRAY_FILTER_USE_BOTH
        );
        $optionsToRemove = array_diff_key($options, $filteredOptions);
        DB::transaction(function () use ($filteredOptions, $optionsToRemove, $optionable): void {
            foreach ($filteredOptions as $key => $value) {
                $option = self::where('key', $key)
                    ->when(
                        $optionable,
                        fn (Builder $query, Model $optionable): Builder => $query->where('optionable_type', $optionable::class)
                            ->where('optionable_id', $optionable->getKey()),
                        fn (Builder $query): Builder => $query->whereNull('optionable_type')
                            ->whereNull('optionable_id')
                    )
                    ->first() ?: new self();
                $option->key = $key;
                $option->value = $value;
                $option->optionable()->associate($optionable);
                $option->save();
            }

            foreach ($optionsToRemove as $key => $value) {
                $option = self::where('key', $key)
                    ->when(
                        $optionable,
                        fn (Builder $query, Model $optionable): Builder => $query->where('optionable_type', $optionable::class)
                            ->where('optionable_id', $optionable->getKey()),
                        fn (Builder $query): Builder => $query->whereNull('optionable_type')
                            ->whereNull('optionable_id')
                    )
                    ->first();
                $option?->delete();
            }
        });
    }

    public function optionable(): MorphTo
    {
        return $this->morphTo();
    }
}
