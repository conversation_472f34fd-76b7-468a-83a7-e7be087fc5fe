<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Interfaces\DashboardShowable;
use App\Models\Interfaces\HasCloudFiles;
use App\Models\Presenters\UserVehiclePresenter;
use App\Project\Core\Eloquent\BaseModel;
use App\Project\Core\Presenters\PresentableModel;
use App\Project\Storage\CloudStorage;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Gate;

/**
 * @mixin IdeHelperUserVehicle
 */
class UserVehicle extends BaseModel implements DashboardShowable, HasCloudFiles, PresentableModel
{
    protected $casts = [
        'eta' => 'datetime',
        'images_collection' => 'array',
        'images_terminal' => 'array',
        'shared_to_client' => 'boolean',
        'share_link_disabled' => 'boolean',
    ];

    protected $fillable = [
        'brand',
        'model',
        'year',
        'vin_number',
        'status',
        'keys',
        'container_number',
        'eta',
        'port_id',
        'vehicle_shipping_line_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function port(): BelongsTo
    {
        return $this->belongsTo(Port::class);
    }

    public function shippingLine(): BelongsTo
    {
        return $this->belongsTo(VehicleShippingLine::class, 'vehicle_shipping_line_id');
    }

    public function dealerClient(): BelongsTo
    {
        return $this->belongsTo(DealerClient::class);
    }

    public function thumbnailImageUrl(): ?string
    {
        $images = $this->allImages();
        if (empty($images)) {
            return null;
        }

        return CloudStorage::viewFileRoute(
            fileName: array_key_first($images),
            model: $this,
            attributeName: $images[array_key_first($images)]
        );
    }

    /**
     * @return array<string, string>
     */
    public function allImages(): array
    {
        return array_filter(
            [
                ...array_fill_keys($this->images_collection ?? [], 'images_collection'),
                ...array_fill_keys($this->images_terminal ?? [], 'images_terminal'),
            ]
        );
    }

    public function canViewFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('user-vehicles.can-view-files', $this);
    }

    public function canRemoveFile(string $attributeName, string $fileName): bool
    {
        return Gate::allows('user-vehicles.can-remove-files', $this);
    }

    public function isFilePublic(string $attributeName): bool
    {
        return true;
    }

    public function presenterClass(): string
    {
        return UserVehiclePresenter::class;
    }

    public function dashboardShowUrl(): ?string
    {
        return route('dealers.user-vehicles.show', $this);
    }
}
