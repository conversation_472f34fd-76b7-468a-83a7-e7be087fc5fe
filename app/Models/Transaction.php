<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Presenters\TransactionPresenter;
use App\Project\Core\Presenters\PresentableModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperTransaction
 */
class Transaction extends BaseTransactionalModel implements PresentableModel
{
    protected $casts = [
        'user_completed' => 'boolean',
        'paid_at' => 'datetime',
    ];

    /**
     * @return BelongsTo<Company, self>
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function presenterClass(): string
    {
        return TransactionPresenter::class;
    }
}
