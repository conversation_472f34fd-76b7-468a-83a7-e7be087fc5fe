<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule): void
    {
        if ($this->app->environment('production')) {
            $schedule->command('backup:monitor')->daily()->at('05:00');
            $schedule->command('backup:clean')->daily()->at('05:30');
            $schedule->command('backup:run')->daily()->at('06:00');
        }

        $schedule->command('payments:send-reminders')->dailyAt('12:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
    }
}
