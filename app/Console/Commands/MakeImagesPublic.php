<?php

namespace App\Console\Commands;

use App\Models\UserBusinessCard;
use App\Models\UserVehicle;
use App\Models\Vehicle;
use App\Project\Storage\CloudStorageModelsMapper;
use Illuminate\Console\Command;
use Illuminate\Contracts\Filesystem\Cloud;

class MakeImagesPublic extends Command
{
    protected $signature = 'app:make-images-public';

    protected $description = 'Make selected images public.';

    public function handle(Cloud $cloud): void
    {
        $vehicleAlias = CloudStorageModelsMapper::alias((new Vehicle()));
        $userVehicleAlias = CloudStorageModelsMapper::alias((new UserVehicle()));
        $userBusinessCardAlias = CloudStorageModelsMapper::alias((new UserBusinessCard()));
        $directories = [
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $vehicleAlias,
                'images'
            ),
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $vehicleAlias,
                'images_collection'
            ),
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $userVehicleAlias,
                'images_collection'
            ),
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $userVehicleAlias,
                'images_terminal'
            ),
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $userBusinessCardAlias,
                'desktop_image'
            ),
            sprintf(
                '%s/%s/%s',
                config('app.env'),
                $userBusinessCardAlias,
                'mobile_image'
            ),
        ];
        foreach ($directories as $directory) {
            $files = $cloud->files($directory);
            $count = count($files);
            $this->info("Making {$count} images in directory {$directory} public...");
            $this->withProgressBar($files, function (string $file) use ($cloud): void {
                $cloud->setVisibility($file, 'public');
            });
            $this->newLine();
        }
    }
}
