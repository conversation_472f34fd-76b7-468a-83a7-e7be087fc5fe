<?php

declare(strict_types=1);

namespace App\Project\Invoice\Dto;

final readonly class BusinessDetails
{
    public function __construct(
        public string $companyName,
        public string $address,
        public string $postCode,
        public string $city,
        public string $country,
    ) {
    }

    /**
     * @return array<string, string>
     */
    public function toInvoiceArray(): array
    {
        return [
            'name' => $this->companyName,
            'location' => $this->address,
            'zip' => $this->postCode,
            'city' => $this->city,
            'country' => $this->country,
        ];
    }
}
