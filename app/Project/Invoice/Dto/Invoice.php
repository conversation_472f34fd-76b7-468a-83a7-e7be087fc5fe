<?php

namespace App\Project\Invoice\Dto;

use Carbon\Carbon;

class Invoice
{
    /**
     * Invoice constructor.
     * @param Item[] $items
     * @param SummaryItem[] $summaryItems
     */
    public function __construct(
        private readonly int $totalPrice,
        private readonly string $invoiceNumber,
        private readonly Carbon $date,
        private readonly array $items,
        private readonly array $summaryItems,
        private readonly Customer $customer,
        private readonly BusinessDetails $businessDetails,
        private readonly OrderInfo $orderInfo,
        private readonly ?string $warrantyAlert,
        private readonly ?Carbon $dueDate = null,
        private readonly bool $isPaid = true
    ) {
    }

    public function getBusinessDetails(): BusinessDetails
    {
        return $this->businessDetails;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    /**
     * @return Item[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * @return SummaryItem[]
     */
    public function getSummaryItems(): array
    {
        return $this->summaryItems;
    }

    public function getInvoiceNumber(): string
    {
        return $this->invoiceNumber;
    }

    public function getOrderInfo(): OrderInfo
    {
        return $this->orderInfo;
    }

    public function getDate(): Carbon
    {
        return $this->date;
    }

    public function getDueDate(): ?Carbon
    {
        return $this->dueDate;
    }

    public function getTotalPrice(): int
    {
        return $this->totalPrice;
    }

    public function getWarrantyAlert(): ?string
    {
        return $this->warrantyAlert;
    }

    public function isPaid(): bool
    {
        return $this->isPaid;
    }
}
