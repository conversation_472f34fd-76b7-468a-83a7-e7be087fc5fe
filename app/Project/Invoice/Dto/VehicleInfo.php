<?php

namespace App\Project\Invoice\Dto;

class VehicleInfo extends OrderInfo
{
    public function __construct(int $id, private ?string $shippingLine, private ?string $containerNumber, private ?string $deliveryLocation)
    {
        parent::__construct($id);
    }

    /**
     * @return mixed[]
     */
    public function additionalInformation(): array
    {
        $items = parent::additionalInformation();

        $items['Shipping Line'] = $this->shippingLine;
        $items['Container'] = $this->containerNumber;
        $items['Delivery'] = $this->deliveryLocation;

        return $items;
    }
}
