<?php

namespace App\Project\Invoice\DataProviders;

use App\Models\Vehicle;
use App\Project\Core\ConvertAccentCharacters;
use App\Project\Invoice\Dto\OrderInfo;
use App\Project\Invoice\Dto\VehicleInfo;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;

class VehicleOrderDataProvider extends InvoiceDataProvider
{
    /**
     * @var array<string, string>
     */
    protected const ORDER_ITEMS = [
        'price_purchase' => 'Vehicle Sales',
        'price_payment' => 'Vehicle Payment',
        'price_ocean_shipping' => 'Ocean shipping',
        'price_landing_shipping' => 'Landing shipping',
        'price_cleaning' => 'Cleaning Fee',
        'price_tax' => 'Transaction Fee',
        'price_insurance' => 'Shipping Protection Plan',
        'price_commission' => 'Storage Fee',
        'price_discount' => 'Discount',
        'price_points_discount' => 'Points Discount',
        'price_garbage_removal' => 'Garbage Removal',
        'price_moisture_absorber' => 'Moisture absorber',
        'price_administration_fee' => 'Administration Fee',
    ];

    /**
     * @var string[]
     */
    protected const NEGATIVE_ITEMS = [
        'price_discount',
        'price_points_discount',
    ];

    public function orderInfo(): OrderInfo
    {
        $vehicle = $this->vehicle();
        $deliverLocation = $vehicle->deliveryLocationDisplayName();
        $shippingLine = $vehicle->shippingLine ? $vehicle->shippingLine->name : null;

        return new VehicleInfo(
            $this->order->id,
            $shippingLine,
            $vehicle->container_number,
            ConvertAccentCharacters::convert($deliverLocation)
        );
    }

    public function orderName(): string
    {
        $vehicle = $this->vehicle();

        return $vehicle->vehicle_description.(empty($vehicle->vin_number) ? '' : ' VIN: '.$vehicle->vin_number);
    }

    public function warrantyAlert(): ?string
    {
        if ($this->vehicle()->type_of_service === VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT) {
            return 'This vehicle is sold “AS IS”, with all faults , obvious or concealed and there are NO WARRANTIES, expressed or implied, including warranties of MERCHANTABILITY or FITNESS FOR PURPOSE';
        }

        return null;
    }

    private function vehicle(): Vehicle
    {
        return $this->order->vehicle;
    }
}
