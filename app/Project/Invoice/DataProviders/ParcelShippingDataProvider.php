<?php

declare(strict_types=1);

namespace App\Project\Invoice\DataProviders;

use App\Project\Invoice\Dto\OrderInfo;

class ParcelShippingDataProvider extends InvoiceDataProvider
{
    /**
     * @var array<string, string>
     */
    protected const ORDER_ITEMS = [
        'price_purchase' => 'Transport',
        'price_tax' => 'Transaction Fee',
        'price_shipping' => 'Ground Shipping',
        'price_commission' => 'Broker Fee',
        'price_points_discount' => 'Points Discount',
    ];

    /**
     * @var string[]
     */
    protected const NEGATIVE_ITEMS = [
        'price_points_discount',
    ];

    public function orderInfo(): OrderInfo
    {
        return new OrderInfo($this->order->id);
    }

    public function orderName(): string
    {
        return $this->order->order_number;
    }

    public function warrantyAlert(): ?string
    {
        return null;
    }
}
