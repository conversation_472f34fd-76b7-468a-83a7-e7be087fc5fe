<?php

declare(strict_types=1);

namespace App\Project\Import;

use Maatwebsite\Excel\Importer;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Import
{
    public function __construct(private readonly Importer $importer)
    {

    }

    public function __invoke(string|UploadedFile $file, ?callable $bindValueCallback = null, ?array $keys = null): array
    {
        $data = $this->importer->toArray(new Sheet($bindValueCallback), $file)[0];

        if (is_array($keys)) {
            $data = array_map(
                fn (array $row): array => array_combine($keys, array_slice($row, 0, count($keys))),
                $data
            );
        }

        return $data;
    }
}
