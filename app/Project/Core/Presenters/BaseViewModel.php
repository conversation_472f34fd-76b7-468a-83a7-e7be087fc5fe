<?php

declare(strict_types=1);

namespace App\Project\Core\Presenters;

use InvalidArgumentException;

abstract class BaseViewModel
{
    public function __construct(protected array $container)
    {
    }

    /**
     * @return CollectionPresenter|BaseModelPresenter|mixed
     */
    public function __get(string $name)
    {
        if (! array_key_exists($name, $this->container)) {
            $className = static::class;

            throw new InvalidArgumentException("Undefined property: {$name} on {$className}");
        }

        $item = $this->container[$name];

        return PresenterInstanceResolver::presenterInstance($item);
    }

    public function __isset(string $name): bool
    {
        return array_key_exists($name, $this->container) && $this->container[$name] !== null;
    }

    protected function mergeContainer(array $toMerge): void
    {
        $this->container = [...$this->container, ...$toMerge];
    }
}
