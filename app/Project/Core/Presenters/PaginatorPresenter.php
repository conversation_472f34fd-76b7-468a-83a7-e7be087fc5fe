<?php

declare(strict_types=1);

namespace App\Project\Core\Presenters;

use ArrayIterator;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * @mixin LengthAwarePaginator
 */
class PaginatorPresenter extends ArrayIterator
{
    public function __construct(private readonly LengthAwarePaginator $paginator)
    {
        parent::__construct($paginator->items());
    }

    public function paginator(): LengthAwarePaginator
    {
        return $this->paginator;
    }

    /**
     * @param string|int $key
     */
    public function offsetGet($key): mixed
    {
        return $this->representableValue(parent::offsetGet($key));
    }

    public function current(): mixed
    {
        return $this->representableValue(parent::current());
    }

    /**
     * @return CollectionPresenter|BaseModelPresenter|mixed
     */
    public function __call(string $method, array $parameters): mixed
    {
        $item = $this->paginator->{$method}(...$parameters);

        return $this->representableValue($item);
    }

    /**
     * @return CollectionPresenter|BaseModelPresenter|mixed
     */
    public function __get(string $name): mixed
    {
        $item = $this->paginator->{$name};

        return $this->representableValue($item);
    }

    /**
     * @return CollectionPresenter|BaseModelPresenter|mixed
     */
    private function representableValue(mixed $item): mixed
    {
        return PresenterInstanceResolver::presenterInstance($item);
    }
}
