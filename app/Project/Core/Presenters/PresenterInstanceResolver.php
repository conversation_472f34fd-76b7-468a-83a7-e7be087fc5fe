<?php

declare(strict_types=1);

namespace App\Project\Core\Presenters;

use App\Project\Core\Eloquent\EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Collection;
use LogicException;

class PresenterInstanceResolver
{
    /**
     * @return CollectionPresenter|BaseModelPresenter|mixed
     */
    public static function presenterInstance(mixed $item)
    {
        if ($item instanceof Model) {
            if (! $item instanceof PresentableModel) {
                $itemClass = $item::class;

                throw new LogicException("Model {$itemClass} should implement PresentableModelInterface");
            }

            $presenterClass = $item->presenterClass();

            return app()->make($presenterClass, ['model' => $item]);
        }

        if ($item instanceof AbstractPaginator) {
            return new PaginatorPresenter($item);
        }

        if ($item instanceof Collection) {
            if ($item instanceof EloquentCollection) {
                $model = $item->relatedModelInstance();

                if (! $model instanceof ModelWithCollectionPresenter) {
                    return new CollectionPresenter($item);
                }

                $collectionPresenterClass = $model->collectionPresenterClass();

                return new $collectionPresenterClass($item);
            }

            return new CollectionPresenter($item);
        }

        return $item;
    }
}
