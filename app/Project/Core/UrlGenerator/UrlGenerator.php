<?php

declare(strict_types=1);

namespace App\Project\Core\UrlGenerator;

use Illuminate\Routing\Exceptions\UrlGenerationException;
use Route;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Throwable;

class UrlGenerator extends \Illuminate\Routing\UrlGenerator
{
    public function route($name, mixed $parameters = [], $absolute = true, ?string $locale = null): string
    {
        $routeNameParts = explode('.', $name);

        $routeNameWithoutLocale = $name;

        if (in_array($routeNameParts[0], config('app.available_locales'), true)) {
            $localeFromRouteName = $routeNameParts[0];
            array_shift($routeNameParts);

            $routeNameWithoutLocale = implode('.', $routeNameParts);
        }

        $locale = $localeFromRouteName ?? ($locale ?: app()->getLocale());

        if (str_starts_with($routeNameWithoutLocale, '.')) {
            $routeNameWithoutLocale = substr($routeNameWithoutLocale, 1);
        }

        $newRouteName = $routeNameWithoutLocale ? "{$locale}.{$routeNameWithoutLocale}" : '';

        try {
            if (Route::has($routeNameWithoutLocale) && ! Route::has($newRouteName)) {
                return $this->toRoute(
                    $this->routes->getByName($routeNameWithoutLocale),
                    $parameters,
                    $absolute
                );
            }

            return $this->toRoute(
                $this->routes->getByName("{$locale}.{$routeNameWithoutLocale}"),
                $parameters,
                $absolute
            );
        } catch (Throwable $e) {
            if ($e instanceof UrlGenerationException || str_contains($e->getMessage(), 'function getDomain()')) {
                throw new RouteNotFoundException("Route [{$name}] not defined.");
            }

            throw $e;
        }
    }
}
