<?php

declare(strict_types=1);

namespace App\Project\Core\UrlGenerator;

use App\Project\Core\UrlGenerator\UrlGenerator as OurUrlGenerator;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\ServiceProvider;

class UrlGeneratorServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->extend('url', fn (UrlGenerator $urlGenerator): OurUrlGenerator => new OurUrlGenerator(
            $this->app->make('router')->getRoutes(),
            $urlGenerator->getRequest(),
            $this->app->make('config')->get('app.asset_url')
        ));
    }

    public function boot(): void
    {
    }
}
