<?php

declare(strict_types=1);

namespace App\Project\Core\Eloquent;

use App\Models\Traits\UseCustomEloquentBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

abstract class BaseModel extends Model
{
    use HasFactory, UseCustomEloquentBuilder;

    public function newCollection(array $models = []): EloquentCollection
    {
        $collection = new EloquentCollection($models);
        $collection->relatedModel(static::class);

        return $collection;
    }
}
