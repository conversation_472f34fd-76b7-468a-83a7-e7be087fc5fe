<?php

declare(strict_types=1);

namespace App\Project\Core\Gates;

use App\Models\DealerClient;
use App\Models\User;
use App\Models\UserVehicle;
use App\Models\Vehicle;

class DealersGate
{
    public function canAssignClient(User $user, Vehicle|UserVehicle $vehicle, ?DealerClient $dealerClient): bool
    {
        if ($vehicle->user_id !== $user->id) {
            return false;
        }

        if ($dealerClient && $dealerClient->user_id !== $user->id) {
            return false;
        }

        return true;
    }
}
