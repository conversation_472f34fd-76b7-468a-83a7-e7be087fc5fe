<?php

declare(strict_types=1);

namespace App\Project\Core\Gates;

use App\Models\User;

class CostsGate
{
    public function hasAccess(User $user): bool
    {
        return $user->isSuperAdmin() || $user->isWorker();
    }

    public function canCreate(User $user): bool
    {
        return $user->isSuperAdmin() || $user->isWorker();
    }

    public function canEdit(User $user): bool
    {
        return $user->isSuperAdmin() || $user->isWorker();
    }

    public function canDelete(User $user): bool
    {
        return $user->isSuperAdmin();
    }

    public function canExport(User $user): bool
    {
        return $user->isSuperAdmin();
    }

    public function canImport(User $user): bool
    {
        return $user->isSuperAdmin();
    }
}
