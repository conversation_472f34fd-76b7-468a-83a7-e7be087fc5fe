<?php

namespace App\Project\Core\Gates;

use App\Models\Transaction;
use App\Models\User;

class TransactionsGate
{
    public function hasAccess(User $user): bool
    {
        return $user->isSuperAdmin();
    }

    public function canEdit(User $user, Transaction $transaction): bool
    {
        if (! $user->isSuperAdmin()) {
            return false;
        }

        return $transaction->order_id === null;
    }

    public function canDelete(User $user, Transaction $transaction): bool
    {
        if (! $user->isSuperAdmin()) {
            return false;
        }

        return $transaction->order_id === null;
    }
}
