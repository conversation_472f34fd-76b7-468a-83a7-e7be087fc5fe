<?php

declare(strict_types=1);

namespace App\Project\Core\Gates;

use App\Models\User;

class MessagesGate
{
    public function hasAccess(User $user): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->isWarehouseManager() || $user->isWorker();
    }

    public function canArchive(User $user): bool
    {
        return $user->isSuperAdmin();
    }
}
