<?php

namespace App\Project\Core\Gates;

use App\Models\Parcel;
use App\Models\User;

class ParcelsGate
{
    public function hasAccess(User $user): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }
        if ($user->isWorker()) {
            return true;
        }

        return $user->isWarehouseManager();
    }

    public function canDelete(User $user): bool
    {
        return $user->isSuperAdmin();
    }

    public function canViewFiles(User $user, Parcel $parcel): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }
        if ($user->isWorker()) {
            return true;
        }

        if ($user->isWarehouseManager() && ($user->managedWarehouses->find($parcel->warehouse_id) !== null)) {
            return true;
        }

        return $parcel->user_id === $user->getKey();
    }

    public function canManageImages(User $user, Parcel $parcel, string $type): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }
        if ($user->isWorker()) {
            return true;
        }

        if ($user->isWarehouseManager() && ($user->managedWarehouses->find($parcel->warehouse_id) !== null)) {
            return true;
        }

        return false;
    }
}
