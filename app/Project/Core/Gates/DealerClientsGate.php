<?php

declare(strict_types=1);

namespace App\Project\Core\Gates;

use App\Models\DealerClient;
use App\Models\User;

class DealerClientsGate
{
    public function canShow(User $user, DealerClient $dealerClient): bool
    {
        return $this->checkOwnership($user, $dealerClient);
    }

    public function canEdit(User $user, DealerClient $dealerClient): bool
    {
        return $this->checkOwnership($user, $dealerClient);
    }

    public function canDelete(User $user, DealerClient $dealerClient): bool
    {
        return $this->checkOwnership($user, $dealerClient);
    }

    private function checkOwnership(User $user, DealerClient $dealerClient): bool
    {
        return $user->id === $dealerClient->user_id;
    }
}
