<?php

declare(strict_types=1);

namespace App\Project\Core\Gates;

use App\Models\Order;
use App\Models\User;

class InvoicesGate
{
    public function hasAccess(User $user): bool
    {
        return $user->isSuperAdmin();
    }

    public function canShow(User $user, Order $order): bool
    {
        if (! $order->status->shouldGenerateInvoice()) {
            return false;
        }
        if (! $order->invoice) {
            return false;
        }
        if (! $order->invoice->invoice_number) {
            return false;
        }
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $order->user_id === $user->id;
    }
}
