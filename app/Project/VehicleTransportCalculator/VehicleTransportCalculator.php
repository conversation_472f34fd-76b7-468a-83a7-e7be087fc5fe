<?php

declare(strict_types=1);

namespace App\Project\VehicleTransportCalculator;

use App\Models\City;
use App\Models\Points;
use App\Models\Type;
use App\Models\VehicleCalculationPrice;
use App\Models\VehicleDeliveryLocation;
use App\Models\VehicleDeliveryLocationInformation;
use App\Project\VehicleTransportCalculator\Dto\VehicleTransportPrice;
use App\Project\VehicleTransportCalculator\Dto\VehicleTransportPrices;
use App\Project\VehicleTransportCalculator\Enums\VehicleCalculationPriceTypes;
use Closure;
use Illuminate\Support\Collection;

class VehicleTransportCalculator
{
    private static array $cache = [];

    /**
     * @param string[] $types
     */
    public function prices(
        City $from,
        VehicleDeliveryLocation $to,
        array $types = VehicleCalculationPriceTypes::ALL_TYPES,
        ?callable $additionalPriceForTypeCallback = null
    ): VehicleTransportPrices {
        return new VehicleTransportPrices(
            $from,
            array_combine(
                array_values($types),
                array_map(
                    function (string $type) use ($from, $to, $additionalPriceForTypeCallback): array {
                        $additionalPrice = $additionalPriceForTypeCallback ? $additionalPriceForTypeCallback(
                            $type
                        ) : null;

                        return $this->price($from, $to, $type, $additionalPrice);
                    },
                    $types
                )
            )
        );
    }

    /**
     * @return VehicleTransportPrice[]
     */
    public function price(
        City $from,
        VehicleDeliveryLocation $to,
        string $type,
        ?int $additionalPrice = null
    ): array {
        $matchedDatabaseType = VehicleCalculationPriceTypes::matchedDatabaseType($type);
        $fromPrices = $this->fromPrices($from)
            ->where('type', $matchedDatabaseType);
        $toPrices = $this->toPrices($to)
            ->where('type', $matchedDatabaseType);

        return array_filter(
            array_map(
                function (VehicleCalculationPrice $fromPrice) use ($toPrices, $type, $to, $additionalPrice): ?VehicleTransportPrice {
                    /** @var VehicleCalculationPrice $matchedToPrice */
                    $matchedToPrice = $toPrices->firstWhere('city_id', $fromPrice->city_id);
                    if ($matchedToPrice === null) {
                        return null;
                    }

                    $totalPrice = (int) round(
                        $fromPrice->price *
                        VehicleCalculationPriceTypes::landTransportPriceMultiplier($type)
                    ) + $matchedToPrice->price + $additionalPrice;
                    $priceDiscounted = $totalPrice - Points::DISCOUNTS[Type::VEHICLES_SLUG]['discount'];
                    $deliveryInformation = $this->deliveryInformation($fromPrice->city_id, $to);

                    return new VehicleTransportPrice(
                        $this->city($fromPrice->city_id),
                        $totalPrice,
                        $priceDiscounted > 0 ? $priceDiscounted : 0,
                        $totalPrice + $this->calculatePremiumPrice($matchedToPrice->city->state->code),
                        $deliveryInformation?->information
                    );
                },
                $fromPrices->all()
            )
        );
    }

    private function calculatePremiumPrice(string $state): ?int
    {
        return match ($state) {
            'NJ', 'GA' => 35000,
            'TX' => 45000,
            'CA' => 65000,
            default => null,
        };
    }

    /**
     * @return Collection|VehicleCalculationPrice[]
     */
    private function toPrices(VehicleDeliveryLocation $to): Collection
    {
        $key = "to-prices-{$to->getKey()}";

        return $this->fromCache($key, fn () => VehicleCalculationPrice::where('calculable_type', VehicleDeliveryLocation::class)
            ->where('calculable_id', $to->getKey())
            ->orderBy('price', 'ASC')
            ->get());
    }

    /**
     * @return Collection|VehicleCalculationPrice[]
     */
    private function fromPrices(City $from): Collection
    {
        $key = "from-prices-{$from->getKey()}";

        return $this->fromCache($key, fn () => VehicleCalculationPrice::where('calculable_type', City::class)
            ->where('calculable_id', $from->getKey())
            ->orderBy('price', 'ASC')
            ->get());
    }

    private function deliveryInformation(int $cityId, VehicleDeliveryLocation $to): ?VehicleDeliveryLocationInformation
    {
        $key = "delivery-information-{$to->getKey()}-{$cityId}";

        return $this->fromCache($key, fn () => VehicleDeliveryLocationInformation::where(
            'vehicle_delivery_location_id',
            $to->getKey()
        )
            ->where('city_id', $cityId)
            ->first());
    }

    private function city(int $cityId): City
    {
        $key = "city-{$cityId}";

        return $this->fromCache($key, fn () => City::findOrFail($cityId));
    }

    private function fromCache(string $key, Closure $callback): mixed
    {
        if (array_key_exists($key, self::$cache)) {
            return self::$cache[$key];
        }

        $value = $callback();
        self::$cache[$key] = $value;

        return $value;
    }
}
