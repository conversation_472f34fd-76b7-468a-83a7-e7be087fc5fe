<?php

declare(strict_types=1);

namespace App\Project\VehicleTransportCalculator;

use App\Models\Vehicle;
use App\Project\Option\Enums\UserOptions;
use App\Project\Option\Providers\UserOptionsProvider;
use App\Project\Vehicle\Enums\VehicleSellerTypes;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;

class VehicleBrokerFeesCalculator
{
    public function __invoke(Vehicle $vehicle): int
    {
        $userOptionsProvider = (new UserOptionsProvider($vehicle->user));

        $fees = 0;
        if ($vehicle->type_of_service !== VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT) {
            return $fees;
        }

        if ($vehicle->user_pay_for_vehicle === false) {
            $fees += $userOptionsProvider->option(UserOptions::BROKER_FEE_CW_PAYS, 0, true);
        }

        return match ($vehicle->seller_type) {
            VehicleSellerTypes::IAA => $fees + $userOptionsProvider->option(UserOptions::BROKER_FEE_IAA, 0, true),
            VehicleSellerTypes::COPART => $fees + $userOptionsProvider->option(UserOptions::BROKER_FEE_COPART, 0, true),
            default => $fees + $userOptionsProvider->option(UserOptions::BROKER_FEE_OTHER, 0, true),
        };
    }
}
