<?php

declare(strict_types=1);

namespace App\Project\VehicleTransportCalculator;

use App\Project\Option\Enums\GlobalOptions;
use App\Project\Option\Providers\GlobalOptionsProvider;
use App\Project\User\Enums\UserAccountTypes;
use App\Project\User\UserAccountTypeProvider;
use App\Project\VehicleTransportCalculator\Enums\VehicleCalculationPriceTypes;
use Exception;

class VehiclePriceListAccessTypesDataProvider
{
    public function __construct(private readonly GlobalOptionsProvider $globalOptionsProvider, private readonly UserAccountTypeProvider $userAccountTypeProvider)
    {
    }

    public function userPriceAdditionForVehicleType(string $type, string $accountType): int
    {
        return VehicleCalculationPriceTypes::isVehicleType($type) ?
            $this->userPriceAdditionForCar($accountType) :
            $this->userPriceAdditionForAtvAndMotorcycle($accountType);
    }

    /**
     * @throws Exception
     */
    public function userPriceAdditionForCar(string $accountType): int
    {
        $optionName = match ($accountType) {
            UserAccountTypes::STANDARD => GlobalOptions::VEHICLES_PRICES_STANDARD_VEHICLES_ADDITION,
            UserAccountTypes::PREMIUM_LOW => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_VEHICLES_ADDITION,
            UserAccountTypes::PREMIUM_HIGH => GlobalOptions::VEHICLES_PRICES_PREMIUM_HIGH_VOLUME_VEHICLES_ADDITION,
            UserAccountTypes::VIP_LOW => GlobalOptions::VEHICLES_PRICES_VIP_LOW_VOLUME_VEHICLES_ADDITION,
            UserAccountTypes::VIP_HIGH => GlobalOptions::VEHICLES_PRICES_VIP_HIGH_VOLUME_VEHICLES_ADDITION,
            default => throw new Exception("Unknown access type with name {$accountType}"),
        };

        return (int) $this->globalOptionsProvider->option($optionName, 0);
    }

    /**
     * @throws Exception
     */
    public function userPriceAdditionForAtvAndMotorcycle(string $accountType): int
    {
        $optionName = match ($accountType) {
            UserAccountTypes::STANDARD => GlobalOptions::VEHICLES_PRICES_STANDARD_ATV_MOTORCYCLE_ADDITION,
            UserAccountTypes::PREMIUM_LOW => GlobalOptions::VEHICLES_PRICES_PREMIUM_LOW_VOLUME_ATV_MOTORCYCLE_ADDITION,
            UserAccountTypes::PREMIUM_HIGH => GlobalOptions::VEHICLES_PRICES_PREMIUM_HIGH_VOLUME_ATV_MOTORCYCLE_ADDITION,
            UserAccountTypes::VIP_LOW => GlobalOptions::VEHICLES_PRICES_VIP_LOW_VOLUME_ATV_MOTORCYCLE_ADDITION,
            UserAccountTypes::VIP_HIGH => GlobalOptions::VEHICLES_PRICES_VIP_HIGH_VOLUME_ATV_MOTORCYCLE_ADDITION,
            default => throw new Exception("Unknown access type with name {$accountType}"),
        };

        return (int) $this->globalOptionsProvider->option($optionName, 0);
    }

    public function hasAccessToType(string $accountType): bool
    {
        if (
            $accountType === UserAccountTypes::STANDARD &&
            $this->userAccountType() === UserAccountTypes::STANDARD
        ) {
            return true;
        }

        if (
            in_array($accountType, [UserAccountTypes::STANDARD, UserAccountTypes::PREMIUM_LOW]
            ) &&
            $this->userAccountType() === UserAccountTypes::PREMIUM_LOW
        ) {
            return true;
        }
        if (! in_array($accountType, [UserAccountTypes::STANDARD, UserAccountTypes::PREMIUM_HIGH]
        )) {
            return in_array(
                $this->userAccountType(),
                [
                    UserAccountTypes::VIP_HIGH,
                    UserAccountTypes::VIP_LOW,
                ]
            );
        }
        if ($this->userAccountType() !== UserAccountTypes::PREMIUM_HIGH) {
            return in_array(
                $this->userAccountType(),
                [
                    UserAccountTypes::VIP_HIGH,
                    UserAccountTypes::VIP_LOW,
                ]
            );
        }

        return true;
    }

    public function userGainedTypes(): array
    {
        $accountType = $this->userAccountType();

        return match ($accountType) {
            UserAccountTypes::STANDARD => [
                UserAccountTypes::STANDARD,
            ],
            UserAccountTypes::PREMIUM_LOW => [
                UserAccountTypes::STANDARD,
                UserAccountTypes::PREMIUM_LOW,
            ],
            UserAccountTypes::PREMIUM_HIGH => [
                UserAccountTypes::STANDARD,
                UserAccountTypes::PREMIUM_HIGH,
            ],
            UserAccountTypes::VIP_LOW => [
                UserAccountTypes::STANDARD,
                UserAccountTypes::PREMIUM_LOW,
                UserAccountTypes::VIP_LOW,
            ],
            UserAccountTypes::VIP_HIGH => [
                UserAccountTypes::STANDARD,
                UserAccountTypes::PREMIUM_HIGH,
                UserAccountTypes::VIP_HIGH,
            ],
            default => throw new Exception("Unknown access type with name {$accountType}"),
        };
    }

    public function userAvailableTypes(): array
    {
        $accountType = $this->userAccountType();

        return in_array(
            $accountType,
            [UserAccountTypes::VIP_HIGH, UserAccountTypes::PREMIUM_HIGH]
        ) ? [
            UserAccountTypes::STANDARD,
            UserAccountTypes::PREMIUM_HIGH,
            UserAccountTypes::VIP_HIGH,
        ] : [
            UserAccountTypes::STANDARD,
            UserAccountTypes::PREMIUM_LOW,
            UserAccountTypes::VIP_LOW,
        ];
    }

    private function userAccountType(): string
    {
        return ($this->userAccountTypeProvider)();
    }
}
