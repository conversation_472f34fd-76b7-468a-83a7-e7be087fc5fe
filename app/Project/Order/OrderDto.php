<?php

declare(strict_types=1);

namespace App\Project\Order;

readonly class OrderDto
{
    public function __construct(
        private int $userId,
        private int $typeId,
        private int $statusId,
        private string $sender = '',
        private string $orderNumber = '',
        private ?string $description = null,
        private ?int $warehouseId = null,
        private ?int $companyId = null,
        private int $pricePurchase = 0,
        private int $priceGarbageRemoval = 0,
        private int $priceMoistureAbsorber = 0
    ) {
    }

    public function userId(): int
    {
        return $this->userId;
    }

    public function typeId(): int
    {
        return $this->typeId;
    }

    public function statusId(): int
    {
        return $this->statusId;
    }

    public function sender(): string
    {
        return $this->sender;
    }

    public function orderNumber(): string
    {
        return $this->orderNumber;
    }

    public function description(): ?string
    {
        return $this->description;
    }

    public function warehouseId(): ?int
    {
        return $this->warehouseId;
    }

    public function companyId(): ?int
    {
        return $this->companyId;
    }

    public function pricePurchase(): int
    {
        return $this->pricePurchase;
    }

    public function priceGarbageRemoval(): int
    {
        return $this->priceGarbageRemoval;
    }

    public function priceMoistureAbsorber(): int
    {
        return $this->priceMoistureAbsorber;
    }
}
