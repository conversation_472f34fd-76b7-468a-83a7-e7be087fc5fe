<?php

declare(strict_types=1);

namespace App\Project\Cost\Enums;

final class AccountTypes
{
    public const DEBIT_CARD = 'debit_card';
    public const CREDIT_CARD = 'credit_card';

    public const ALL_TYPES = [
        self::DEBIT_CARD,
        self::CREDIT_CARD,
    ];

    public static function forSelect(): array
    {
        return array_combine(
            self::ALL_TYPES,
            array_map(
                fn (string $type): string => self::humanReadable($type),
                self::ALL_TYPES
            )
        );
    }

    public static function humanReadable(string $type): string
    {
        return __("admin/costs.account_types.{$type}");
    }
}
