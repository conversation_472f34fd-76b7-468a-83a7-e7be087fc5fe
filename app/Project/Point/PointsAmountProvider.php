<?php

namespace App\Project\Point;

use App\Models\Order;
use App\Models\Points;
use App\Models\Service;
use App\Models\Type;
use App\Models\Warehouse;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;

class PointsAmountProvider
{
    /**
     * @var int
     */
    final public const PARCEL_SHIPMENT_DEFAULT_POINTS = 2;

    /**
     * @var int
     */
    final public const PARCEL_SHOPPING_POINTS = 2;

    /**
     * @var int
     */
    final public const PARCEL_SHIPPING_POINTS = 2;

    /**
     * @var int
     */
    final public const VEHICLE_TRANSPORT_POINTS = 5;

    /**
     * @var int
     */
    final public const VEHICLE_SHOPPING_POINTS = 10;

    public static function grantedForOrder(Order $order): ?int
    {
        $amount = static::amountForOrder($order);
        if ($amount === null) {
            return $amount;
        }
        if ($order->promotionCode === null) {
            return $amount;
        }

        return $amount * $order->promotionCode->value;
    }

    public static function amountForOrder(Order $order): ?int
    {
        $typeSlug = $order->type->slug;

        if ($typeSlug === Type::PARCELS_SLUG) {
            $serviceSlug = $order->service->slug;

            if ($serviceSlug === Service::SHIPMENT_SLUG) {
                return static::parcelShipmentAmount($order);
            }

            if ($serviceSlug === Service::SHOPPING_SLUG) {
                return static::PARCEL_SHOPPING_POINTS;
            }

            if ($serviceSlug === Service::SHIPPING_SLUG) {
                return static::PARCEL_SHIPPING_POINTS;
            }

            return null;
        }

        if ($typeSlug === Type::VEHICLES_SLUG) {
            $serviceType = $order->vehicle->type_of_service;

            if (in_array($serviceType, [
                VehicleTypesOfServices::BOUGHT_RECEPTION_TRANSPORT,
                VehicleTypesOfServices::TRANSPORT,
            ])) {
                return static::VEHICLE_TRANSPORT_POINTS;
            }

            if ($serviceType === VehicleTypesOfServices::BUY_RECEPTION_TRANSPORT) {
                return static::VEHICLE_SHOPPING_POINTS;
            }

            return null;
        }

        return null;
    }

    public static function discount(Order $order): ?DiscountDto
    {
        if (! isset(Points::DISCOUNTS[$order->type->slug])) {
            return null;
        }

        $discountInfo = Points::DISCOUNTS[$order->type->slug];

        return new DiscountDto($discountInfo['points'], $discountInfo['discount']);
    }

    private static function parcelShipmentAmount(Order $order): ?int
    {
        $warehouse = $order->warehouse;
        $warehouseSlug = $warehouse->slug;
        if ($warehouseSlug === Warehouse::BENSENVILLE_SLUG) {
            return static::PARCEL_SHIPMENT_DEFAULT_POINTS;
        }

        if ($warehouseSlug === Warehouse::POLAMER_SLUG) {
            if ($order->parcels()->count() >= 2) {
                return static::PARCEL_SHIPMENT_DEFAULT_POINTS;
            }

            return null;
        }

        if ($warehouseSlug === Warehouse::MELROSE_PARK_SLUG) {
            $price = $order->price;

            if ($price >= 80000) {
                return 10;
            }

            return ((int) ($price / 20000)) + 2;
        }

        return null;
    }
}
