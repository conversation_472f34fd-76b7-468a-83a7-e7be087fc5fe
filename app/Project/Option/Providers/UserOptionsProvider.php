<?php

declare(strict_types=1);

namespace App\Project\Option\Providers;

use App\Models\Option;
use App\Models\User;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Auth\Guard;

class UserOptionsProvider extends BaseOptionsProvider
{
    public function __construct(private readonly ?User $user = null, private readonly ?Guard $guard = null)
    {
    }

    /**
     * @return mixed[]
     * @throws AuthenticationException
     */
    protected function options(): array
    {
        $user = $this->user ?: optional($this->guard)->user();

        if (! $user) {
            throw new AuthenticationException();
        }

        return $user->options
            ->mapWithKeys(fn (Option $option): array => [$option->key => $option->value])
            ->toArray();
    }
}
