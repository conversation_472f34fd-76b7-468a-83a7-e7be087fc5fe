<?php

declare(strict_types=1);

namespace App\Project\Option\Enums;

use App\Project\User\Enums\UserAccountTypes;

final class UserOptions
{
    public const ACCOUNT_TYPE = 'account_type';
    public const BROKER_FEE_COPART = 'broker_fee_copart';
    public const BROKER_FEE_IAA = 'broker_fee_iaa';
    public const BROKER_FEE_OTHER = 'broker_fee_other';
    public const BROKER_FEE_CW_PAYS = 'broker_fee_cw_pays';
    public const PAYMENT_DEADLINE_DAYS = 'payment_deadline_days';

    public const DEFAULTS = [
        self::ACCOUNT_TYPE => UserAccountTypes::STANDARD,
        self::BROKER_FEE_COPART => null,
        self::BROKER_FEE_IAA => null,
        self::BROKER_FEE_OTHER => null,
        self::BROKER_FEE_CW_PAYS => null,
        self::PAYMENT_DEADLINE_DAYS => 7,
    ];

    public const PRICE_OPTIONS = [
        self::BROKER_FEE_COPART,
        self::BROKER_FEE_IAA,
        self::BROKER_FEE_OTHER,
        self::BROKER_FEE_CW_PAYS,
    ];

    public static function defaultValue(string $key): mixed
    {
        return self::DEFAULTS[$key] ?? null;
    }
}
