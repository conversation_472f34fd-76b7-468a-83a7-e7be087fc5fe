<?php

declare(strict_types=1);

namespace App\Project\Option\ServiceProviders;

use App\Project\Option\Providers\UserOptionsProvider;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class UserOptionsServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /** @var bool */
    public function register(): void
    {
        $this->app->singleton(UserOptionsProvider::class, fn (Application $app): UserOptionsProvider => new UserOptionsProvider(null, $app->make(Guard::class)));
    }

    /**
     * @return array<class-string<UserOptionsProvider>>
     */
    public function provides(): array
    {
        return [UserOptionsProvider::class];
    }
}
