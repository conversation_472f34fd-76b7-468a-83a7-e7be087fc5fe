<?php

namespace App\Project\PackageTrackingData;

use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use JsonSerializable;

class PackageEvent implements JsonSerializable
{
    /**
     * @param DateTime|DateTimeImmutable $eventDate
     */
    public function __construct(private readonly DateTimeInterface $eventDate, private readonly ?string $eventName, private readonly ?string $eventDescription, private readonly ?string $countryCode, private readonly ?string $city, private readonly ?string $status)
    {
    }

    /**
     * @return array<string, null>|array<string, string>
     */
    public function jsonSerialize(): array
    {
        return [
            'eventDate' => $this->eventDate->format('Y-m-d H:i'),
            'eventName' => $this->eventName,
            'eventDescription' => $this->eventDescription,
            'countryCode' => $this->countryCode,
            'city' => $this->city,
            'status' => $this->status,
        ];
    }

    /**
     * @return DateTime|DateTimeImmutable
     */
    public function eventDate(): DateTime
    {
        return $this->eventDate;
    }
}
