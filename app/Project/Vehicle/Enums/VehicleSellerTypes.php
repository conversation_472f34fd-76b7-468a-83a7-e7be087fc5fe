<?php

declare(strict_types=1);

namespace App\Project\Vehicle\Enums;

final class VehicleSellerTypes
{
    /**
     * @var string
     */
    public const COPART = 'copart';

    /**
     * @var string
     */
    public const IAA = 'iaa';

    /**
     * @var string
     */
    public const ADESA = 'adesa';

    /**
     * @var string
     */
    public const IMPACT_AUTO_AUCTIONS = 'impact_auto_actions';

    /**
     * @var string
     */
    public const IRON_PLANET = 'iron_planet';

    /**
     * @var string
     */
    public const KENTUCKIANA = 'kentuckiana';

    /**
     * @var string
     */
    public const MANHEIM = 'manheim';

    /**
     * @var string
     */
    public const NPA = 'npa';

    /**
     * @var string
     */
    public const RICHIE_BROTHERS_AUCTION = 'richie_brothers_auction';

    /**
     * @var string
     */
    public const THE_SALVAGE_CENTER = 'the_salvage_center';

    /**
     * @var string
     */
    public const TRA = 'tra';

    /**
     * @var string
     */
    public const DEALER = 'dealer';

    /**
     * @var string
     */
    public const PRIVATE = 'private';

    /**
     * @var string
     */
    public const OTHER = 'other';

    /**
     * @var string[]
     */
    public const AUCTION_TYPES = [
        self::COPART,
        self::IAA,
        self::ADESA,
        self::IMPACT_AUTO_AUCTIONS,
        self::IRON_PLANET,
        self::KENTUCKIANA,
        self::MANHEIM,
        self::NPA,
        self::RICHIE_BROTHERS_AUCTION,
        self::THE_SALVAGE_CENTER,
        self::TRA,
    ];

    /**
     * @var string[]
     */
    public const OTHER_TYPES = [
        self::DEALER,
        self::PRIVATE,
        self::OTHER,
    ];

    /**
     * @return array<int|string, mixed>
     */
    public static function forSelect(): array
    {
        return array_merge(
            [
                __('vehicles.seller_type_auction') => array_combine(
                    self::AUCTION_TYPES,
                    array_map(
                        fn (string $type): string => self::humanReadable($type),
                        self::AUCTION_TYPES
                    )
                ),
            ],
            array_combine(
                self::OTHER_TYPES,
                array_map(
                    fn (string $type): string => self::humanReadable($type),
                    self::OTHER_TYPES
                )
            )
        );
    }

    public static function humanReadable(string $type): string
    {
        return __("vehicles.seller_types.{$type}");
    }
}
