<?php

declare(strict_types=1);

namespace App\Project\Storage;

use App\Models\Cost;
use App\Models\Driver;
use App\Models\DriverVehicle;
use App\Models\Interfaces\HasCloudFiles;
use App\Models\Order;
use App\Models\Parcel;
use App\Models\UserBusinessCard;
use App\Models\UserDocument;
use App\Models\UserVehicle;
use App\Models\Vehicle;
use App\Models\VehicleReport;
use InvalidArgumentException;

class CloudStorageModelsMapper
{
    /**
     * @var array<string, string>
     */
    final public const MODELS_MAP = [
        Driver::class => 'driver',
        Vehicle::class => 'vehicle',
        UserDocument::class => 'user_document',
        Order::class => 'order',
        VehicleReport::class => 'vehicle_report',
        Parcel::class => 'parcel',
        Cost::class => 'cost',
        UserBusinessCard::class => 'user_business_card',
        UserVehicle::class => 'user_vehicle',
        DriverVehicle::class => 'driver_vehicle',
    ];

    public static function alias(HasCloudFiles $model): string
    {
        $class = $model::class;
        $directory = self::MODELS_MAP[$class] ?? null;

        if ($directory === null) {
            throw new InvalidArgumentException("Unknown storage model in map for {$class}");
        }

        return $directory;
    }

    public static function modelClassInstance(string $alias): HasCloudFiles
    {
        $modelClass = static::modelClass($alias);

        return new $modelClass;
    }

    public static function modelClass(string $alias): string
    {
        $modelClass = array_search($alias, self::MODELS_MAP);

        if (! $modelClass) {
            throw new InvalidArgumentException("Unknown storage model in map for {$modelClass}");
        }

        return $modelClass;
    }
}
