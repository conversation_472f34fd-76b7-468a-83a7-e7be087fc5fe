<?php

declare(strict_types=1);

namespace App\Project\Storage;

use App\Models\Interfaces\HasCloudFiles;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use STS\ZipStream\ZipStream;
use Zip;

class CloudStorageDownloader
{
    public function __construct(private readonly CloudStorage $cloudStorage)
    {
    }

    public function __invoke(HasCloudFiles $model, array $files): Redirector|RedirectResponse|ZipStream
    {
        if (count($files) === 1) {
            return redirect(
                CloudStorage::viewFileRoute(
                    fileName: array_key_first($files),
                    model: $model,
                    attributeName: $files[array_key_first($files)],
                ),
            );
        }

        $date = Carbon::now()->format('d_m_y_h_i_s');
        $zip = Zip::create("files_{$date}.zip");

        foreach ($files as $fileName => $directory) {
            $zip->addRaw(
                content: $this->cloudStorage->fileContent(
                    fileName: $fileName,
                    model: $model,
                    attributeName: $directory,
                ),
                zipPath: $fileName
            );
        }

        return $zip;
    }
}
