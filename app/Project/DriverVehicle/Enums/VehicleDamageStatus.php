<?php

declare(strict_types=1);

namespace App\Project\DriverVehicle\Enums;

use App\Project\Enums\EnumToArrayTrait;

enum VehicleDamageStatus: string
{
    use EnumToArrayTrait;

    case NONE = 'none';
    case PROCESSING = 'processing';
    case RESOLVED = 'resolved';

    public function humanReadable(): string
    {
        return __("admin/vehicles.index.filters.vehicle_damages.status.{$this->value}");
    }

    /**
     * @return array<string, string>
     */
    public static function forSelect(bool $withoutNone = false): array
    {
        return array_combine(
            $withoutNone ? array_map(fn (self $status): string => $status->value, self::withoutNoneStatus()) : self::values(),
            array_map(
                fn (self $status): string => $status->humanReadable(),
                $withoutNone ? self::withoutNoneStatus() : self::cases(),
            )
        );
    }

    /**
     * @return array<int, self>
     */
    private static function withoutNoneStatus(): array
    {
        return array_filter(self::cases(), static fn (self $status): bool => $status !== self::NONE);
    }
}
