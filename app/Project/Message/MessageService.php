<?php

namespace App\Project\Message;

use App\Models\Interfaces\Messageable;
use App\Models\Message;
use Exception;

class MessageService
{
    public function create(Messageable $model, string $content, string $type, int $authorId): Message
    {
        if (! in_array($type, [Message::QUESTION_TYPE, Message::ANSWER_TYPE])) {
            throw new Exception('Invalid type');
        }

        return $model->messages()
            ->create([
                'content' => $content,
                'type' => $type,
                'author_id' => $authorId,
            ]);
    }
}
