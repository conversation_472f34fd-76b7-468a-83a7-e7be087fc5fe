<?php

declare(strict_types=1);

namespace App\Project\Payment;

use App\Project\Transactional\BaseTransactionalDto;
use DateTimeInterface;

class TransactionDto extends BaseTransactionalDto implements HasCompany
{
    public function __construct(
        int $amount,
        int $userId,
        bool $charge,
        ?int $transactionId,
        protected int $companyId,
        ?DateTimeInterface $createdAt = null,
        ?string $title = null,
        protected ?int $orderId = null,
        protected ?int $templateId = null,
        protected ?DateTimeInterface $paidAt = null,
    ) {
        parent::__construct(
            amount: $amount,
            userId: $userId,
            charge: $charge,
            transactionId: $transactionId,
            createdAt: $createdAt,
            title: $title,
        );
    }

    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    public function getTemplateId(): ?int
    {
        return $this->templateId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getPaidAt(): ?DateTimeInterface
    {
        return $this->paidAt;
    }
}
