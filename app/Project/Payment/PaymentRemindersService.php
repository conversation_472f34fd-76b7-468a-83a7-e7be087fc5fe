<?php

namespace App\Project\Payment;

use App\Models\Order;
use App\Models\User;
use App\Repositories\UserRepository;
use Carbon\Carbon;
use Illuminate\Mail\Mailer;
use Illuminate\Mail\Message;
use Illuminate\Support\Collection;

class PaymentRemindersService
{
    /**
     * @var int
     */
    final public const MIN_REMIND_DAYS = 4;

    /**
     * @var int
     */
    final public const MAX_REMIND_DAYS = 30;

    public function __construct(private readonly UserRepository $userRepository, private readonly Mailer $mailer)
    {
    }

    public function sendReminders(): void
    {
        $users = $this->userRepository->findUsersWithNotRemindedOrders(self::MIN_REMIND_DAYS, self::MAX_REMIND_DAYS);

        foreach ($users as $user) {
            $this->sendReminderToUser($user);
            $this->setOrdersAsReminded($user->orders);
        }
    }

    private function sendReminderToUser(User $user): void
    {
        $summaryAmount = $user->orders->sum('price');
        $this->mailer->send(
            'emails.payment-reminder',
            [
                'orders' => $user->orders,
                'summaryAmount' => $summaryAmount,
                'title' => __('email.payment_reminder.title'),
            ],
            function (Message $message) use ($user): void {
                $message->subject(__('email.payment_reminder.subject'))
                    ->to($user->email);
            });
    }

    private function setOrdersAsReminded(Collection $collection): void
    {
        Order::whereIn('id', $collection->pluck('id'))
            ->update(['valuation_reminded_at' => Carbon::now()]);
    }
}
