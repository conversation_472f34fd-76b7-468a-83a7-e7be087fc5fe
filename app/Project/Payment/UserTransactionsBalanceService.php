<?php

declare(strict_types=1);

namespace App\Project\Payment;

use App\Project\Transactional\BaseUserBalanceService;
use App\Project\Transactional\TransactionalType;
use App\Repositories\TransactionRepository;

/**
 * @property TransactionRepository $transactionalRepository
 */
class UserTransactionsBalanceService extends BaseUserBalanceService
{
    public function __construct(TransactionRepository $transactionRepository)
    {
        parent::__construct($transactionRepository);
    }

    protected function balanceColumnName(): string
    {
        return 'account_balance';
    }

    protected function transactionalType(): TransactionalType
    {
        return TransactionalType::TRANSACTION;
    }
}
