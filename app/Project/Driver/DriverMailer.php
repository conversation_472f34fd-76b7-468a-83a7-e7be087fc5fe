<?php

declare(strict_types=1);

namespace App\Project\Driver;

use App\Project\Core\Mailer\BaseMailer;
use App\Project\Core\Mailer\MailerSender;
use App\Project\Core\Mailer\Sender;
use Closure;
use Illuminate\Contracts\Mail\Mailable;

class DriverMailer extends BaseMailer
{
    public function __invoke(
        Mailable|string|array $view,
        array $data = [],
        Closure|string|null $callback = null
    ): void {
        $this->send(
            mailerSender: MailerSender::DRIVER,
            sender: new Sender(config('mail.mailers.driver.address'), config('mail.mailers.driver.name')),
            view: $view,
            data: $data,
            callback: $callback
        );
    }
}
