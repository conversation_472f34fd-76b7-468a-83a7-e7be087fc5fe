<?php

declare(strict_types=1);

namespace App\Project\Safecube;

use App\Project\Safecube\Dto\ContainerTrackDataDto;
use App\Project\Safecube\Dto\CoordinatesDto;
use App\Project\Safecube\Dto\LocationDto;
use App\Project\Safecube\Dto\RouteEventDto;
use App\Project\Safecube\Dto\RouteLocationDto;
use App\Project\Safecube\Dto\RouteLocationEventDto;
use App\Project\Safecube\Dto\RouteSegmentDto;
use App\Project\Safecube\Dto\VesselDto;
use Illuminate\Support\Str;

class SafeCubeDtoFactory
{
    /**
     * @param array<string, mixed> $raw
     */
    public static function createContainerTrackDataDto(array $raw): ContainerTrackDataDto
    {
        $metadata = $raw['metadata'] ?? [];

        $routeSegments = array_map(function (array $segment) {
            return new RouteSegmentDto(
                routeType: $segment['routeType'],
                path: array_map(fn (array $point) => new CoordinatesDto(lat: $point['lat'], lng: $point['lng']), $segment['path']),
            );
        }, $raw['routeData']['routeSegments'] ?? []);

        $locations = array_map(function (array $location) {
            return new LocationDto(
                name: $location['name'],
                state: $location['state'],
                country: $location['country'],
                countryCode: $location['countryCode'],
                locode: $location['locode'],
                timezone: $location['timezone'],
                coordinates: new CoordinatesDto(
                    lat: $location['coordinates']['lat'],
                    lng: $location['coordinates']['lng'],
                ),
            );
        }, $raw['locations'] ?? []);

        $route = array_map(
            function (array $routeData, int|string $routeType) {
                $location = null;
                if (! empty($routeData['location'])) {
                    $location = new LocationDto(
                        name: $routeData['location']['name'],
                        state: $routeData['location']['state'],
                        country: $routeData['location']['country'],
                        countryCode: $routeData['location']['countryCode'],
                        locode: $routeData['location']['locode'],
                        timezone: $routeData['location']['timezone'],
                        coordinates: new CoordinatesDto(
                            lat: $routeData['location']['coordinates']['lat'] ?? null,
                            lng: $routeData['location']['coordinates']['lng'] ?? null,
                        ),
                    );
                }

                return new RouteLocationDto(
                    routeType: (string) $routeType,
                    date: $routeData['date'],
                    predictiveEta: $routeData['predictiveEta'],
                    location: $location,
                );
            },
            $raw['route'] ?? [],
            array_keys($raw['route'] ?? [])
        );

        $routeCoordinates = null;
        if (! empty($raw['routeData']['coordinates'])) {
            $routeCoordinates = new CoordinatesDto(
                lat: $raw['routeData']['coordinates']['lat'],
                lng: $raw['routeData']['coordinates']['lng'],
            );
        }

        $events = self::mapEvents($raw['containers'][0]['events'] ?? []);

        $vessels = array_map(function (array $vessel) {
            return new VesselDto(
                name: $vessel['name'] ?? '',
                imo: $vessel['imo'] ?? null,
            );
        }, $raw['vessels'] ?? []);

        return new ContainerTrackDataDto(
            sealine: $metadata['sealine'] ?? null,
            sealineName: $metadata['sealineName'] ?? null,
            shippingStatus: ! empty($metadata['shippingStatus']) ? __('track-container.statuses.'.Str::lower($metadata['shippingStatus'])) : __('track-container.statuses.unknown'),
            shipmentType: $metadata['shipmentType'] ?? null,
            shipmentNumber: $metadata['shipmentNumber'] ?? null,
            updatedAt: $metadata['updatedAt'] ?? null,
            containerSizeType: $raw['containers'][0]['sizeType'] ?? null,
            routeSegments: $routeSegments,
            locations: $locations,
            route: $route,
            routeCoordinates: $routeCoordinates,
            events: $events,
            vessels: $vessels,
        );
    }

    /**
     * @param array<int, array<string, mixed>> $events
     * @return array<int, RouteEventDto>
     */
    public static function mapEvents(array $events): array
    {
        $grouped = [];

        foreach ($events as $event) {
            $locationKey = json_encode($event['location']);
            $grouped[$locationKey][] = $event;
        }

        $result = [];

        foreach ($grouped as $locationJson => $eventGroup) {
            $locationArray = json_decode((string) $locationJson, true);

            $locationDto = new LocationDto(
                name: $locationArray['name'] ?? '',
                state: $locationArray['state'] ?? '',
                country: $locationArray['country'] ?? '',
                countryCode: $locationArray['countryCode'] ?? '',
                locode: $locationArray['locode'] ?? '',
                timezone: $locationArray['timezone'] ?? '',
                coordinates: new CoordinatesDto(
                    $locationArray['coordinates']['lat'] ?? 0.0,
                    $locationArray['coordinates']['lng'] ?? 0.0
                )
            );

            $eventDtos = array_map(function (array $e): RouteLocationEventDto {
                return new RouteLocationEventDto(
                    type: $e['eventType'] ?? '',
                    description: $e['description'] ?? '',
                    isActual: $e['isActual'] ?? false,
                    date: $e['date'] ?? ''
                );
            }, $eventGroup);

            $result[] = new RouteEventDto($locationDto, $eventDtos);
        }

        return $result;
    }
}
