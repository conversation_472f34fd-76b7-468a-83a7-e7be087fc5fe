<?php

declare(strict_types=1);

namespace App\Project\Safecube\Dto;

readonly class ContainerTrackDataDto
{
    /**
     * @param array<int, RouteSegmentDto> $routeSegments
     * @param array<int, LocationDto> $locations
     * @param array<int, RouteLocationDto> $route
     * @param array<int, RouteEventDto> $events
     * @param array<int, VesselDto> $vessels
     */
    public function __construct(
        public ?string $sealine,
        public ?string $sealineName,
        public ?string $shippingStatus,
        public ?string $shipmentType,
        public ?string $shipmentNumber,
        public ?string $updatedAt,
        public ?string $containerSizeType,
        public array $routeSegments,
        public array $locations,
        public array $route,
        public ?CoordinatesDto $routeCoordinates,
        public array $events,
        public array $vessels,
    ) {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'sealine' => $this->sealine,
            'sealineName' => $this->sealineName,
            'shippingStatus' => $this->shippingStatus,
            'shipmentType' => $this->shipmentType,
            'shipmentNumber' => $this->shipmentNumber,
            'updatedAt' => $this->updatedAt,
            'containerSizeType' => $this->containerSizeType,
            'routeSegments' => array_map(
                fn (RouteSegmentDto $routeSegment): array => $routeSegment->toArray(),
                $this->routeSegments
            ),
            'locations' => array_map(
                fn (LocationDto $location): array => $location->toArray(),
                $this->locations
            ),
            'route' => array_map(
                fn (RouteLocationDto $routeLocation): array => $routeLocation->toArray(),
                $this->route
            ),
            'routeCoordinates' => $this->routeCoordinates ? $this->routeCoordinates->toArray() : [],
            'events' => array_map(
                fn (RouteEventDto $event): array => $event->toArray(),
                $this->events
            ),
            'vessels' => array_map(
                fn (VesselDto $vessel): array => $vessel->toArray(),
                $this->vessels
            ),
        ];
    }
}
