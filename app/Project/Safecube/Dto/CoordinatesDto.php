<?php

declare(strict_types=1);

namespace App\Project\Safecube\Dto;

class CoordinatesDto
{
    public function __construct(
        public ?float $lat,
        public ?float $lng,
    ) {
    }

    /**
     * @return array{lat: float|null, lng: float|null}
     */
    public function toArray(): array
    {
        return [
            'lat' => $this->lat,
            'lng' => $this->lng,
        ];
    }
}
