<?php

declare(strict_types=1);

namespace App\Project\Safecube\Dto;

use Webmozart\Assert\Assert;

class RouteSegmentDto
{
    /**
     * @param array<CoordinatesDto> $path
     */
    public function __construct(
        public string $routeType,
        public array $path,
    ) {
        Assert::allIsInstanceOf($path, CoordinatesDto::class);
    }

    /**
     * @return array{routeType: string, path: array<array{lat: float|null, lng: float|null}>}
     */
    public function toArray(): array
    {
        return [
            'routeType' => $this->routeType,
            'path' => array_map(fn (CoordinatesDto $point) => $point->toArray(), $this->path),
        ];
    }
}
