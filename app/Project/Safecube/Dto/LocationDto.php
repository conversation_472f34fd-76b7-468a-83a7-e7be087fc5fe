<?php

declare(strict_types=1);

namespace App\Project\Safecube\Dto;

class LocationDto
{
    public function __construct(
        public string $name,
        public ?string $state,
        public string $country,
        public string $countryCode,
        public string $locode,
        public string $timezone,
        public ?CoordinatesDto $coordinates,
    ) {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'state' => $this->state,
            'country' => $this->country,
            'countryCode' => $this->countryCode,
            'locode' => $this->locode,
            'timezone' => $this->timezone,
            'coordinates' => $this->coordinates?->toArray(),
        ];
    }
}
