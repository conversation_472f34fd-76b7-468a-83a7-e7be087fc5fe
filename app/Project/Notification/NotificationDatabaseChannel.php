<?php

declare(strict_types=1);

namespace App\Project\Notification;

use Illuminate\Notifications\Notification;

class NotificationDatabaseChannel
{
    public function send(mixed $notifiable, Notification $notification): void
    {
        $notificationModel = $notification->toDatabase($notifiable);

        $notificationModel->notifiable()
            ->associate($notifiable);
        $notificationModel->save();
    }
}
