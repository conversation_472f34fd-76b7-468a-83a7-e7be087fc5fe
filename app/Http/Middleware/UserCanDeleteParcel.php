<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Repositories\ParcelRepository;
use Closure;
use Illuminate\Http\Request;

class UserCanDeleteParcel
{
    public function handle(Request $request, Closure $next)
    {
        $parcelId = $request->route('parcel');
        $parcelRepository = app()->make(ParcelRepository::class);
        $parcel = $parcelRepository->findForUser($parcelId);
        if ($parcel->canAuthUserDelete()) {
            return $next($request);
        }

        abort(404);
    }
}
