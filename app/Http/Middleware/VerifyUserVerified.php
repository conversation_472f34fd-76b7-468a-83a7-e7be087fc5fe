<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class VerifyUserVerified
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->user()->active) {
            return $next($request);
        }

        return redirect()->route('profile.dashboard')->with('danger',
            'Zweryfikuj swoje konto aby korzystać z wszystkich funkcjonalności strony.');
    }
}
