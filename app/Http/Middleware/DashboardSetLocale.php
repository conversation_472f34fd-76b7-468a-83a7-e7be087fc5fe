<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Cookie\Factory as CookieFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\App;

class DashboardSetLocale
{
    final public const COOKIE_KEY = 'dashboard_locale';

    public function __construct(private readonly CookieFactory $cookieFactory, private readonly UrlGenerator $url)
    {
    }

    /**
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $requestLocale = $request->segment(1) ?? config('app.default_locale');

        if (! in_array($requestLocale, config('app.available_locales'), true)) {
            abort(Response::HTTP_NOT_FOUND);
        }

        App::setLocale($requestLocale);

        $this->url->defaults(['locale' => $requestLocale]);

        /** @var Response $response */
        $response = $next($request);

        if (
            method_exists($response, 'withCookie') &&
            $request->cookie(self::COOKIE_KEY) !== $requestLocale
        ) {
            $cookie = $this->cookieFactory->forever(self::COOKIE_KEY, $requestLocale);

            return $response->withCookie($cookie);
        }

        return $response;
    }
}
