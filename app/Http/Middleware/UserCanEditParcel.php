<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Parcel;
use App\Repositories\ParcelRepository;
use Closure;
use Illuminate\Http\Request;

class UserCanEditParcel
{
    public function handle(Request $request, Closure $next)
    {
        $parcelId = $request->route('parcel');
        $parcelRepository = app()->make(ParcelRepository::class);
        /** @var Parcel $parcel */
        $parcel = $parcelRepository->findForUser($parcelId);
        if ($parcel->canAuthUserEdit()) {
            return $next($request);
        }

        abort(404);
    }
}
