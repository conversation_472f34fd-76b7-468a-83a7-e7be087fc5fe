<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Translation\Translator;

class ParcelCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return string[]
     */
    public function rules(): array
    {
        $rules = [];

        foreach ($this->input('parcel', []) as $key => $val) {
            $rules['parcel.'.$key.'.tracking_number'] = 'required|max:32';
            $rules['parcel.'.$key.'.order_number'] = 'nullable|max:32';
            $rules['parcel.'.$key.'.sender'] = 'nullable|max:32';
            $rules['parcel.'.$key.'.description]'] = 'nullable|max:3000';
        }

        return $rules;
    }

    /**
     * @return mixed[][]|Translator[]|string[]|null[]
     */
    public function messages(): array
    {
        $messages = [];

        foreach ($this->input('parcel', []) as $key => $val) {
            $messages['parcel.'.$key.'.tracking_number.required'] = trans('validation.required',
                [
                    'attribute' => trans('validation.attributes.parcel_tracking_number'),
                ]);

            $messages['parcel.'.$key.'.tracking_number.max'] = ucfirst(trans('validation.max.string',
                [
                    'attribute' => trans('validation.attributes.parcel_tracking_number'),
                ]));

            $messages['parcel.'.$key.'.order_number.max'] = ucfirst(trans('validation.max.string',
                [
                    'attribute' => trans('validation.attributes.parcel_order_number'),
                ]));

            $messages['parcel.'.$key.'.sender.max'] = ucfirst(trans('validation.max.string',
                [
                    'attribute' => trans('validation.attributes.parcel_sender'),
                ]));
        }

        return $messages;
    }
}
