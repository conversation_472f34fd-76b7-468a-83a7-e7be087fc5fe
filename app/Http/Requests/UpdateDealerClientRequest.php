<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\DealerClient;
use Illuminate\Validation\Rule;

class UpdateDealerClientRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'email' => [
                'nullable',
                'email',
                'required_without:first_name',
                Rule::unique((new DealerClient())->getTable())
                    ->ignore($this->route('dealerClient'))
                    ->where('user_id', auth()->id()),
            ],
            'first_name' => 'nullable|min:2|required_without:email',
            'last_name' => 'nullable|min:2|required_with:first_name',
            'phone_number' => 'nullable|min:2',
        ];
    }
}
