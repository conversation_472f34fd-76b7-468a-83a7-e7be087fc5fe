<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use App\Models\Company;
use Illuminate\Validation\Rule;

class VehiclesExportRequest extends FormRequest
{
    /**
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_id' => ['required', Rule::exists((new Company())->getTable(), 'id')],
            'from' => 'required|date_format:d.m.Y',
            'to' => 'required|date_format:d.m.Y|after:from',
        ];
    }
}
