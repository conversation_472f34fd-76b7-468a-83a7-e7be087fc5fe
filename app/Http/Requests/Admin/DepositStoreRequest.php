<?php

namespace App\Http\Requests\Admin;

use App\Models\Company;
use App\Models\User;
use Illuminate\Validation\Rule;

class DepositStoreRequest extends BaseTransactionalRequest
{
    /**
     * @return mixed[]
     */
    public function rules(): array
    {
        $rules = parent::rules();

        $rules['user_id'] = ['required', Rule::exists((new User())->getTable(), 'id')];
        $rules['company_id'] = ['required', Rule::exists((new Company())->getTable(), 'id')];
        $rules['created_at'] = 'required|date_format:d.m.Y';

        return $rules;
    }
}
