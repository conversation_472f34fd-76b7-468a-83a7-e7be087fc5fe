<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use App\Rules\MoneyRule;

class SettingsUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array<string, array<string|MoneyRule>>
     */
    public function rules(): array
    {
        return [
            'vehicles_prices_standard_vehicles_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_standard_atv_motorcycle_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_premium_low_volume_vehicles_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_premium_low_volume_atv_motorcycle_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_premium_high_volume_vehicles_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_premium_high_volume_atv_motorcycle_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_vip_low_volume_vehicles_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_vip_low_volume_atv_motorcycle_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_vip_high_volume_vehicles_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_vip_high_volume_atv_motorcycle_addition' => ['nullable', new MoneyRule()],
            'vehicles_prices_default_broker_fee' => ['nullable', new MoneyRule()],
            'vehicles_calculator_note' => 'nullable|array',
            'vehicles_calculator_note.pl' => 'nullable|string',
            'vehicles_calculator_note.en' => 'nullable|string',
            'vehicle_reports_enabled' => ['boolean'],
        ];
    }

    public function priceFields(): array
    {
        return [
            'vehicles_prices_standard_vehicles_addition',
            'vehicles_prices_standard_atv_motorcycle_addition',
            'vehicles_prices_premium_low_volume_vehicles_addition',
            'vehicles_prices_premium_low_volume_atv_motorcycle_addition',
            'vehicles_prices_premium_high_volume_vehicles_addition',
            'vehicles_prices_premium_high_volume_atv_motorcycle_addition',
            'vehicles_prices_vip_low_volume_vehicles_addition',
            'vehicles_prices_vip_low_volume_atv_motorcycle_addition',
            'vehicles_prices_vip_high_volume_vehicles_addition',
            'vehicles_prices_vip_high_volume_atv_motorcycle_addition',
            'vehicles_prices_default_broker_fee',
        ];
    }
}
