<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use App\Models\Cost;
use App\Project\Cost\Enums\CostCategories;
use Illuminate\Validation\Rule;

class UpdateManyCostsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'ids' => 'required|array',
            'ids.*' => ['required', Rule::exists((new Cost())->getTable(), 'id')],
            'category' => ['required', Rule::in(CostCategories::ALL_TYPES)],
        ];
    }
}
