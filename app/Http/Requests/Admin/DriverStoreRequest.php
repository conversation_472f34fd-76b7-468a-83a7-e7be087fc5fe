<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use App\Models\City;
use App\Models\Driver;
use App\Project\Driver\Enums\TaxNumberType;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

class DriverStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var Driver|null $driver */
        $driver = $this->route('driver');

        return [
            'company_name' => [
                'required',
                'min:3',
                'max:255',
                Rule::unique((new Driver())->getTable(), 'company_name')
                    ->when($driver, fn (Unique $unique) => $unique->ignoreModel($driver)),
            ],
            'dot_number' => [
                'nullable',
                'max:255',
                Rule::unique((new Driver())->getTable(), 'company_name')
                    ->when($driver, fn (Unique $unique) => $unique->ignoreModel($driver)),
            ],
            'mc_number' => [
                'nullable',
                'max:255',
                Rule::unique((new Driver())->getTable(), 'company_name')
                    ->when($driver, fn (Unique $unique) => $unique->ignoreModel($driver)),
            ],
            'phone_number' => 'nullable|min:5|max:20',
            'payments' => 'nullable|min:3',
            'tax_number_type' => ['nullable', Rule::in(TaxNumberType::values())],
            'tax_number' => 'nullable|max:255',
            'tax_company_name' => 'nullable|max:255',
            'secondary_company_name' => 'nullable|max:255',
            'email' => 'nullable|email',
            'address' => 'nullable|max:255',
            'zip_code' => 'nullable|max:10',
            'city_id' => ['nullable', Rule::exists((new City())->getTable(), 'id')],
            'comment' => 'nullable',
            'w9' => 'nullable|file',
            'insurance' => 'nullable|file',
            'irs' => 'nullable|file',
            'dot_certificate' => 'nullable|file',
        ];
    }
}
