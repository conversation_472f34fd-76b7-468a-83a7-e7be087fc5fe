<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ParcelsStoreRequest;
use App\Models\Parcel;
use App\Models\User;
use App\Models\Warehouse;
use App\Project\Notification\NotificationsSender;
use App\Project\Storage\CloudStorageModelService;
use App\Repositories\WarehouseRepository;
use Gate;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Yajra\Datatables\Datatables;

class ParcelsController extends Controller
{
    public function __construct(
        private readonly NotificationsSender $notification,
        private readonly WarehouseRepository $warehouseRepository,
        private readonly CloudStorageModelService $cloudStorageModelService,
    ) {
    }

    public function index(): Factory|View
    {
        $warehouses = $this->warehouses();
        $warehouses = $warehouses->pluck('name', 'id');

        return view('admin.parcels.index', compact('warehouses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Factory|View
    {
        $warehouses = $this->warehouses();
        $warehouses = $warehouses->pluck('name', 'id');

        return view('admin.parcels.create', compact('warehouses'));
    }

    public function store(ParcelsStoreRequest $request): RedirectResponse
    {
        $user = User::findOrFail($request->get('user_id'));
        if (! empty($request->get('parcel'))) {
            DB::transaction(function () use ($request, $user): void {
                foreach ($request->get('parcel') as $key => $parcel) {
                    $parcelModel = Parcel::create([
                        'added_by_admin' => 1,
                        'user_id' => $user->getKey(),
                        'warehouse_id' => $request->get('warehouse_id'),
                        'order_number' => $parcel['order_number'],
                        'tracking_number' => $parcel['tracking_number'],
                        'description' => $parcel['description'],
                        'sender' => $parcel['sender'],
                    ]);
                    $this->cloudStorageModelService->privateStoreImages(
                        request: $request,
                        model: $parcelModel,
                        formKey: "parcel.{$key}.images",
                        attributeName: 'images',
                    );
                }
            });

            $this->notification->sendItemNotification(
                $user,
                'W Twoim magazynie pojawiły się nowe paczki ('.count($request->get('parcel')).')',
                'W Twoim magazynie pojawiły się nowe paczki ('.count($request->get('parcel')).')',
                route('parcels.index')
            );

            return redirect()->back()->with(
                'success',
                "W magazynie użytkownika <strong>{$user->email} ({$user->user_code})</strong> zostały dodane ".count(
                    $request->get('parcel')
                ).' nowe paczki'
            );
        }

        return redirect()->back()->with('danger', 'Coś poszło nie tak');
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): void
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id): Factory|View
    {
        $parcel = Parcel::findOrFail($id);
        $warehouses = $this->warehouses();

        $warehouses = $warehouses->pluck('name', 'id');

        return view('admin.parcels.edit', compact('parcel', 'warehouses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        $parcel = Parcel::findOrFail($id);
        $parcel->update($request->all());

        return redirect()->back()->with('success', 'Zmiany zostały zapisane.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): ResponseFactory|Response
    {
        if (Gate::denies('parcels.can-delete')) {
            abort('401');
        }

        $parcel = Parcel::findOrFail($id);

        if ($parcel->order_id) {
            return response(
                ['msg' => 'Ze względów bezpieczeństwa nie można usunąć paczki która została zlecona do wysyłki']
            );
        }

        $parcel->delete();

        return response(['status' => 'ok']);
    }

    public function datatable(Request $request)
    {
        $parcels = Parcel::join('users', 'parcels.user_id', '=', 'users.id')->join(
            'warehouses',
            'parcels.warehouse_id',
            '=',
            'warehouses.id'
        )->select([
            'parcels.id',
            'warehouses.name',
            'users.email',
            'parcels.order_number',
            'parcels.tracking_number',
            'parcels.sender',
            'parcels.order_id',
            'parcels.description',
            'parcels.created_at',
            'parcels.added_by_admin',
            'parcels.user_id',
            'parcels.vehicle_id',
        ])->with('user');

        if ($userId = $request->get('user_id')) {
            $parcels->where('users.id', $userId);
        }

        if ($request->filled('has_images')) {
            $hasImages = (bool) $request->get('has_images');
            $parcels->where(function (Builder $query) use ($hasImages): void {
                if ($hasImages) {
                    $query->whereNotNull('images')
                        ->whereJsonLength('images', '>', 0);
                } else {
                    $query->whereNull('images')
                        ->orWhereJsonLength('images', 0);
                }
            });
        }

        if ($warehouseId = $request->get('warehouse_id')) {
            $parcels->where('parcels.warehouse_id', $warehouseId);
        }

        if (auth()->user()->isWarehouseManager()) {
            $managed = $this->warehouses();
            $parcels->whereIn('parcels.warehouse_id', $managed->pluck('id'));
        }

        return Datatables::of($parcels)
            ->escapeColumns([])
            ->addColumn('action', function ($container): string {
                $actions = '<a href="'.route(
                    'admin.parcels.edit',
                    $container->id
                ).'" class="btn btn-primary"><i class="glyphicon glyphicon-edit"></i> Zarządzaj</a>';

                if (Gate::allows('parcels.can-delete')) {
                    $actions .= '<a href="'.route(
                        'admin.parcels.destroy',
                        $container->id
                    ).'" class="btn btn-danger delete-item" data-text="Czy na pewno chcesz usunąć tą paczkę?"><i class="fa fa-trash fa-fw"></i> Usuń</a>';
                }

                return $actions;
            })
            ->setRowClass(fn ($container): string => $container->added_by_admin == 1 ? 'info' : '')
            ->editColumn('email', function (Parcel $parcel) {
                if (auth()->user()->isWarehouseManager()) {
                    return $parcel->user->getUserCode();
                }

                return $parcel->user->email;
            })
            ->editColumn('order_id', function (Parcel $parcel): string {
                if ($parcel->order_id !== null) {
                    return '<a href="'.route('admin.orders.edit', $parcel->order_id).'">Tak</a>';
                }

                if ($parcel->vehicle_id !== null) {
                    return '<a href="'.route('admin.vehicles.edit', $parcel->vehicle_id).'">Tak</a>';
                }

                return 'Nie';
            })
            ->editColumn('created_at', fn (Parcel $parcel) => $parcel->created_at->format('Y-m-d'))
            ->editColumn('description', '{{ Str::limit("$description", 50) }}')
            ->removeColumn('added_by_admin')
            ->make(true);
    }

    private function warehouses(): Collection
    {
        if (auth()->user()->isWarehouseManager()) {
            return $this->warehouseRepository->getManagedWarehouses(auth()->user());
        }

        return Warehouse::where('has_parcels', 1)->get();
    }
}
