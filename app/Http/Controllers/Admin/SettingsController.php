<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SettingsUpdateRequest;
use App\Models\Option;
use App\Project\Option\Providers\GlobalOptionsProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class SettingsController extends Controller
{
    public function edit(GlobalOptionsProvider $optionsProvider): View
    {
        $options = $optionsProvider->all();

        return view('admin.settings.edit')->with(compact('options'));
    }

    public function update(SettingsUpdateRequest $request): RedirectResponse
    {
        $all = $request->validated();

        $all = array_combine(
            array_keys($all),
            array_map(
                fn (string $key, string|array|null $value): string|int|array|null => in_array($key, $request->priceFields()) ? clearMoneyInput($value) : $value, // @phpstan-ignore-line
                array_keys($all),
                $all
            )
        );

        Option::syncOptions($all);

        return redirect()
            ->route('admin.settings.edit')
            ->with('success', 'Ustawienia zostały zaktualizowany');
    }
}
