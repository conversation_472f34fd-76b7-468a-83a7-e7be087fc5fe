<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Repositories\UserDocumentsRepository;
use Carbon\Carbon;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class VehicleUserDocumentsController extends Controller
{
    public function show(Vehicle $vehicle): Factory|View
    {
        $user = $vehicle->user;
        $documents = $user->documents->keyBy('type')
            ->all();

        return view('admin.vehicles.user-documents')
            ->with(compact('vehicle', 'user', 'documents'));
    }

    public function update(
        UserDocumentsRepository $userDocumentsRepository,
        Vehicle $vehicle,
        Request $request
    ): RedirectResponse {
        $user = $vehicle->user;
        $userDocumentsRepository->make($user, $request);
        $user->id_card_valid_until = empty($request->get('id_card_valid_until')) ?
            null : Carbon::createFromFormat('d.m.Y', $request->get('id_card_valid_until'))->startOfDay();
        $user->comment = $request->get('comment');
        $user->save();

        return redirect()
            ->back()
            ->with('success', 'Pomyślnie zapisano zmiany');
    }
}
