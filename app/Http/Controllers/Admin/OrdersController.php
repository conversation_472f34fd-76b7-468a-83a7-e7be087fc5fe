<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\OrderStoreRequest;
use App\Models\Company;
use App\Models\Country;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\Parcel;
use App\Models\Service;
use App\Models\Type;
use App\Models\User;
use App\Models\Warehouse;
use App\Project\Export\Export;
use App\Project\Invoice\InvoiceService;
use App\Project\Notification\NotificationsSender;
use App\Project\Order\OrderDto;
use App\Project\VehicleReport\VehicleReportService;
use App\Repositories\OrderInvoiceRepository;
use App\Repositories\OrderRepository;
use App\Repositories\PromotionCodeRepository;
use App\Repositories\WarehouseRepository;
use DB;
use Gate;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Request as Input;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Yajra\DataTables\DataTables;

class OrdersController extends Controller
{
    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly NotificationsSender $notification,
        private readonly WarehouseRepository $warehouseRepository,
        private readonly OrderInvoiceRepository $orderInvoiceRepository
    ) {
    }

    public function index(): Factory|View
    {
        $statuses = OrderStatus::pluck('name', 'id')->prepend('- wszystko -', '');

        $typesQuery = Type::where('slug', '!=', Type::VEHICLES_SLUG);
        $warehousesQuery = Warehouse::query();
        $servicesQuery = Service::query();

        if (auth()->user()->isWarehouseManager()) {
            $managed = $this->warehouseRepository->getManagedWarehouses(auth()->user());
            $warehousesQuery->whereIn('id', $managed->pluck('id'));
            $servicesQuery->where('slug', Service::SHIPMENT_SLUG);
            $typesQuery->where('slug', Type::PARCELS_SLUG);
        }

        $types = $typesQuery->pluck('name', 'id')->prepend('- wszystko -', '');
        $warehouses = $warehousesQuery->pluck('name', 'id')->prepend('- wszystko -', '');
        $services = $servicesQuery->pluck('name', 'id')->prepend('- wszystko -', '');
        $companies = Company::all();

        return view(
            'admin.orders.index',
            compact('types', 'services', 'statuses', 'warehouses', 'companies')
        );
    }

    public function create(): View
    {
        if (Gate::denies('orders.can-create')) {
            abort('401');
        }

        $countries = Country::query()->get()->pluck('name', 'id');
        $statuses = OrderStatus::pluck('name', 'id');
        $defaultStatus = OrderStatus::where('slug', OrderStatus::PRICED_SLUG)->firstOrFail();
        $types = Type::whereIn('slug', [Type::DEPOSIT, Type::OTHER])->pluck('name', 'id');

        $users = [];
        if (Input::old('user_id')) {
            $userModel = User::findOrFail(Input::old('user_id'));
            $users[$userModel->getKey()] = $userModel->selectRepresentation();
        }
        $companies = Company::all();

        return view('admin.orders.create')->with(
            compact(
                'countries',
                'statuses',
                'users',
                'types',
                'defaultStatus',
                'companies',
            )
        );
    }

    public function store(OrderStoreRequest $request, InvoiceService $invoiceService): RedirectResponse
    {
        if (Gate::denies('orders.can-create')) {
            abort('401');
        }

        $orderDto = new OrderDto(
            $request->get('user_id'),
            $request->get('type_id'),
            $request->get('status_id'),
            '',
            '',
            null,
            null,
            $request->get('company_id'),
            clearMoneyInput($request->get('price_purchase'))
        );
        $orderInvoice = $this->orderInvoiceRepository->make($request->get('invoice'));
        $order = null;

        DB::transaction(function () use ($orderDto, $orderInvoice, $invoiceService, &$order): void {
            $order = $this->orderRepository->createOrderFromDto($orderDto);
            $order->invoice()->save($orderInvoice);
            $invoiceService->manageOrder($order);
        });

        return redirect()->route('admin.orders.edit', $order)->with('success', 'Zlecenie zostało utworzone');
    }

    public function edit(PromotionCodeRepository $promotionCodeRepository, int $id): Factory|View
    {
        $order = Order::findOrFail($id);
        $statuses = OrderStatus::pluck('name', 'id');
        $pricedStatus = OrderStatus::where('slug', OrderStatus::PRICED_SLUG)->first();
        $promotionCodes = $promotionCodeRepository->getValid()
            ->keyBy('id');
        if ($order->promotionCode) {
            $promotionCodes->put($order->promotionCode->id, $order->promotionCode);
        }

        $promotionCodes = $promotionCodes->pluck('code', 'id');
        $companies = Company::all();

        return view(
            'admin.orders.edit',
            compact('order', 'statuses', 'pricedStatus', 'promotionCodes', 'companies')
        );
    }

    public function update(Request $request, $id): RedirectResponse
    {
        /** @var Order $order */
        $order = Order::findOrFail($id);

        if ($order->status_id != $request->get('status_id')) {
            $status = OrderStatus::findOrFail($request->get('status_id'));

            if (Gate::denies('orders.can-change-status', [$order, $status])) {
                return redirect()->back()->withErrors(
                    'Tylko administrator witryny może zmienić status z '.$order->status->name.' na '.$status->name
                );
            }

            $this->notification->sendStatusNotification(
                $order,
                "Nowy status zlecenia #{$order->id}",
                "Twój status w zleceniu #{$order->id} zmienił się na: <strong>{$status->name}</strong>",
                $order->warehouse ? $order->dashboardShowUrl() : null
            );
        }

        DB::transaction(function () use ($request, $order): void {
            $shippingType = $request->get('shipment', [])['form']['shipping_type'] ?? null;
            if ($shippingType !== null) {
                $form = $order->shipment->form;
                $form['shipping_type'] = $shippingType;
                $order->shipment->form = $form;
                $order->shipment->save();
            }

            $order->update($request->all());
        });

        return redirect()->back()->with('success', __('admin/common.alerts.success_save'));
    }

    public function invoice(int $id): Factory|View
    {
        $order = Order::findOrFail($id);
        $invoice = $order->invoice;
        $countries = Country::query()->get()->pluck('name', 'id');

        return view(
            'admin.orders.invoice',
            compact(
                'invoice',
                'order',
                'countries'
            )
        );
    }

    public function destroy(VehicleReportService $vehicleReportsService, int $id): ResponseFactory|Response
    {
        if (Gate::denies('orders.can-delete')) {
            abort('401');
        }

        $order = Order::findOrFail($id);

        // Unassign parcels/vehicles from the order
        if ($order->type->slug == 'parcels') {
            $parcels = Parcel::where('order_id', $id)->get();
            foreach ($parcels as $parcel) {
                $parcel->order_id = null;
                $parcel->save();
            }
        }

        if ($order->type->slug === Type::VEHICLES_SLUG) {
            $order->vehicle->delete();
        }

        if ($order->type->slug === Type::VEHICLE_REPORTS) {
            $vehicleReportsService->destroy($order->vehicleReport);
        }

        $order->delete();

        return response(['status' => 'ok']);
    }

    public function setImportantStatus(Request $request, $id): JsonResponse
    {
        $order = Order::findOrFail($id);

        $order->important = $request->get('important') === 'true';
        $order->save();

        return response()->json(['important' => $order->important]);
    }

    /**
     * @return mixed
     */
    public function datatable(Request $request)
    {
        $orders = Order::join('users', 'orders.user_id', '=', 'users.id')
            ->leftJoin('warehouses', 'orders.warehouse_id', '=', 'warehouses.id')
            ->join('types', 'orders.type_id', '=', 'types.id')
            ->leftJoin('services', 'orders.service_id', '=', 'services.id')
            ->join('order_statuses', 'orders.status_id', '=', 'order_statuses.id')
            ->leftJoin('companies', 'orders.company_id', '=', 'companies.id')
            ->with('service', 'status')
            ->select([
                'orders.id',
                'warehouses.name as warehouse_name',
                'types.name as type_name',
                'services.name as service_name',
                'services.id as service_id',
                'order_statuses.name as status_name',
                'order_statuses.id as status_id',
                'orders.price',
                'orders.order_number',
                'orders.tracking_number',
                'orders.created_at',
                'orders.important',
                'types.slug',
                'companies.name as company_name',
            ])->groupBy('orders.id');

        if ($userId = $request->get('user_id')) {
            $orders->where('users.id', $userId);
        }

        if ($companyId = $request->get('company_id')) {
            $orders->where('orders.company_id', $companyId);
        }

        $orders->where('types.slug', '!=', Type::VEHICLES_SLUG);

        if (auth()->user()->isWarehouseManager()) {
            $managed = $this->warehouseRepository->getManagedWarehouses(auth()->user());
            $orders->whereIn('orders.warehouse_id', $managed->pluck('id'));
            $orders->where('services.slug', Service::SHIPMENT_SLUG);
        }

        if ($type = $request->get('type')) {
            $orders->where('types.id', "{$type}");
        }

        if ($warehouse = $request->get('warehouse')) {
            $orders->where('warehouses.id', "{$warehouse}");
        }

        if ($service = $request->get('service')) {
            $orders->where('services.id', "{$service}");
        }

        if ($status = $request->get('status')) {
            $orders->where('order_statuses.id', "{$status}");
        }

        if ($status = $request->get('status')) {
            $orders->where('order_statuses.id', "{$status}");
        }

        if ($request->filled('important')) {
            $important = (bool) $request->get('important');
            $orders->where('orders.important', "{$important}");
        }

        $datatables = Datatables::of($orders)
            ->escapeColumns([])
            ->addColumn('action', function (Order $order): string {
                $actions = '<a href="'.route(
                    'admin.orders.edit',
                    $order->id
                ).'" class="btn btn-primary"><i class="glyphicon glyphicon-edit"></i> Zarządzaj</a>';

                if (Gate::allows('orders.can-delete')) {
                    $actions .= '<a href="'.route(
                        'admin.orders.destroy',
                        $order->id
                    ).'" class="btn btn-danger delete-item" data-text="Czy na pewno chcesz usunąć to zlecenie?"><i class="fa fa-trash fa-fw"></i> Usuń</a>';
                }

                return $actions;
            })
            ->editColumn('service_name', fn (Order $order): ?string => $order->service?->name)
            ->editColumn('status_name', fn (Order $order): ?string => $order->status?->name)
            ->editColumn('id', fn (Order $order): string => $order->id.' <a href="'.route(
                'admin.orders.set-important-status',
                $order->id
            ).'" data-important="'.$order->important.'" class="toggle-important">
                <i class="fa '.($order->important ? 'fa-star' : 'fa-star-o').' fa-fw text-yellow"></i>
                </a>')
            ->editColumn('created_at', fn (Order $order) => $order->created_at->format('Y-m-d'))
            ->editColumn('price', '@if($price) ${{ formatMoney($price) }} @endif')
            ->orderColumn('services.name', fn (Builder $query, string $order) => $query->orderBy('services.name->'.app()->getLocale(), $order))
            ->orderColumn('order_statuses.name', fn (Builder $query, string $order) => $query->orderBy('order_statuses.name->'.app()->getLocale(), $order))
            ->setRowClass(fn (Order $order): string => $order->important ? 'info' : '');

        return $datatables->make(true);
    }

    public function export(Export $export, Request $request): BinaryFileResponse
    {
        $service = Service::where('slug', 'shopping')->firstOrFail();
        $orders = Order::where('service_id', $service->id)
            ->where('id', '>=', $request->get('from_id'))
            ->where('id', '<=', $request->get('to_id'))
            ->with(['warehouse', 'type', 'service'])
            ->orderBy('id', 'ASC')
            ->get();
        $sheets = [
            'zlecenia' => array_merge(
                $orders->map(fn (Order $order): array => [
                    'Numer zlecenia' => $order->id,
                    'Magazyn' => $order->warehouse->name,
                    'Typ' => $order->type->name,
                    'Usługa' => $order->service->name,
                    'Numer zamówienia' => $order->order_number,
                    'Koszt zakupu' => '$'.price_to_string($order->price_hidden),
                ])->all(),
                [
                    ['Koszt zakupu ukryte: $'.price_to_string($orders->sum('price_hidden'))],
                ]
            ),
        ];

        return ($export)('zlecenia', $sheets);
    }

    public function postValuation(Order $order, Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = auth()->user();

        if ($user->isWarehouseManager() && ! empty($order->price)) {
            return redirect()
                ->back()
                ->withErrors('Tylko administrator może wykonać korektę ceny');
        }

        $this->orderRepository->valuation($order, $request);

        return redirect()
            ->back()
            ->with(
                'success',
                'Wycena została zapisana w systemie oraz powiadomienie zostało wysłane do użytkownika.'
            );
    }
}
