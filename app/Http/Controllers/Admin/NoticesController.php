<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\NoticeStoreRequest;
use App\Models\Notice;
use App\Models\User;
use App\Project\Notification\NotificationsSender;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Yajra\Datatables\Datatables;

class NoticesController extends Controller
{
    public function __construct(private readonly NotificationsSender $notificationsSender)
    {
    }

    public function index(): Factory|View
    {
        return view('admin.notices.index');
    }

    public function create(): View
    {
        $users = User::whereIn('id', old('users_id', []))
            ->get()
            ->keyBy('id')
            ->map(fn (User $user): string => $user->selectRepresentation())->toArray();

        return view('admin.notices.create')->with(compact('users'));
    }

    public function store(NoticeStoreRequest $request): RedirectResponse
    {
        $notice = new Notice();
        $notice = $this->updateNotice($request, $notice);

        return redirect()
            ->route('admin.notices.edit', $notice)
            ->with('success', __('notices.message_created'));
    }

    public function edit(Notice $notice): View
    {
        $users = $notice->getAttribute('show_to_all_users') ?
            [] : $notice->users->keyBy('id')
                ->map(fn (User $user): string => $user->selectRepresentation())->toArray();

        return view('admin.notices.edit')
            ->with(compact('notice', 'users'));
    }

    public function update(NoticeStoreRequest $request, Notice $notice): RedirectResponse
    {
        $notice = $this->updateNotice($request, $notice);

        return redirect()
            ->route('admin.notices.edit', $notice)
            ->with('success', __('notices.message_updated'));
    }

    public function destroy(Notice $notice): ResponseFactory|Response
    {
        DB::transaction(function () use ($notice): void {
            $notice->notifications()->delete();
            $notice->users()->detach();
            $notice->delete();
        });

        return response(['status' => 'ok']);
    }

    public function datatable(): JsonResponse
    {
        $notices = Notice::select([
            'notices.id',
            'notices.title',
            'notices.content',
            'notices.show_to_all_users',
            'notices.send_notifications',
            'notices.created_at',
        ]);

        return Datatables::of($notices)
            ->escapeColumns([])
            ->editColumn('id', fn (Notice $notice): string => '<a href="'.route('admin.notices.edit', $notice).'" class="btn btn-primary"><i class="glyphicon glyphicon-edit"></i>
                          '.$notice->getKey().'
                        </a>')
            ->editColumn('title', fn (Notice $notice) => Str::limit(strip_tags((string) $notice->title), 100))
            ->editColumn('content', fn (Notice $notice) => Str::limit(strip_tags((string) $notice->content), 100))
            ->editColumn('show_to_all_users', fn (Notice $notice) => $notice->show_to_all_users ? __('notices.yes') : __('notices.no'))
            ->editColumn('send_notifications', fn (Notice $notice) => $notice->send_notifications ? __('notices.yes') : __('notices.no'))
            ->editColumn('created_at', fn (Notice $notice) => $notice->created_at->format('Y-m-d H:i'))
            ->addColumn('action', fn (Notice $notice): string => '<a href="'.route('admin.notices.destroy', $notice->getKey()).'" class="btn btn-danger delete-item" data-text="'.__('notices.confirm_message_deletion').'"><i class="fa fa-trash fa-fw"></i>
                         '.__('notices.delete').'
                        </a>')
            ->orderColumn('notices.title', fn (Builder $query, string $order) => $query->orderBy('title->'.app()->getLocale(), $order))
            ->orderColumn('notices.content', fn (Builder $query, string $order) => $query->orderBy('content->'.app()->getLocale(), $order))
            ->make(true);
    }

    private function updateNotice(Request $request, Notice $notice): Notice
    {
        DB::transaction(function () use ($request, $notice): void {
            $title = $request->get('title');
            $content = $request->get('content');
            $notice->setAttribute('show_to_all_users', $request->get('show_to_all_users'));
            $notice->setAttribute('title', $title);
            $notice->setAttribute('content', $content);
            $notice->setAttribute('send_notifications', $request->get('send_notifications'));
            $notice->save();

            if (! $notice->getAttribute('show_to_all_users')) {
                $notice->users()
                    ->sync($request->get('users_id'));
            } else {
                $notice->users()->detach();
            }

            if ($notice->getAttribute('send_notifications')) {
                $this->syncNotifications($request, $notice);
            }
        });

        return $notice;
    }

    private function syncNotifications(Request $request, Notice $notice): void
    {
        $alreadySendToUsers = $notice->notifications()
            ->get(['user_id'])
            ->pluck('user_id')
            ->toArray();
        $usersQuery = User::where('active', 1)
            ->where('is_suspended', 0);
        if (! $notice->getAttribute('show_to_all_users')) {
            $usersQuery->whereIn('id', $request->get('users_id'));
        }

        /** @var Collection $users */
        $users = $usersQuery->get();
        $notice->notifications()
            ->whereNotIn('user_id', $users->pluck('id')->toArray())
            ->delete();
        $usersToSend = $users->keyBy('id')
            ->except($alreadySendToUsers);

        foreach ($usersToSend as $user) {
            $this->notificationsSender->sendOnlyToUser(
                true,
                $user,
                '',
                'notices.new_message_appeared_on_service',
                route('profile.dashboard').'#notices',
                '',
                false,
                $notice
            );
        }
    }
}
