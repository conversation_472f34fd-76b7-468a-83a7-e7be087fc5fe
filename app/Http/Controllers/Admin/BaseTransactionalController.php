<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\BaseTransactionalRequest;
use App\Models\BaseTransactionalModel;
use App\Models\Company;
use App\Models\User;
use App\Project\Transactional\BaseTransactionalDto;
use App\Project\Transactional\BaseUserBalanceService;
use App\Repositories\BaseTransactionalRepository;
use App\Repositories\TransactionTemplateRepository;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;

abstract class BaseTransactionalController extends Controller
{
    /** @var string */
    protected $indexViewName = '';

    /** @var string */
    protected $createViewName = '';

    /** @var string */
    protected $editViewName = '';

    /** @var string */
    protected $editRouteName = '';

    /** @var string */
    protected $templateType = '';

    abstract protected function createTransactionalDto(array $data, ?BaseTransactionalModel $transactionalModel = null): BaseTransactionalDto;

    abstract protected function userBalanceServiceClass(): string;

    abstract protected function canEdit(BaseTransactionalModel $transactionalModel): bool;

    abstract protected function canDelete(BaseTransactionalModel $transactionalModel): bool;

    public function __construct(protected TransactionTemplateRepository $templateRepository, protected BaseTransactionalRepository $transactionalRepository)
    {
    }

    public function index(): Factory|View
    {
        $companies = Company::all();

        return view($this->indexViewName)->with(compact('companies'));
    }

    public function create(Request $request): View
    {
        $user = null;
        if ($userId = $request->old('user_id')) {
            $user = User::findOrFail($userId);
        }

        $templates = $this->templatesForUserChoice();
        $companies = Company::all();

        return view($this->createViewName)->with(compact('templates', 'user', 'companies'));
    }

    public function edit(int $id): View
    {
        $transaction = $this->findTransaction($id);
        $templates = $this->templatesForUserChoice();
        $canEdit = $this->canEdit($transaction);
        $companies = Company::all();

        return view($this->editViewName)->with(compact('transaction', 'templates', 'canEdit', 'companies'));
    }

    public function destroy(int $id): ResponseFactory|Response
    {
        $transaction = $this->findTransaction($id);

        if (! $this->canDelete($transaction)) {
            return response(['msg' => 'Nie można usunąć tej transakcji ze względów bezpieczeństwa']);
        }

        $this->userBalanceServiceInstance()
            ->remove($transaction);

        return response(['status' => 'ok']);
    }

    protected function makeAction(BaseTransactionalRequest $request, ?int $id = null): RedirectResponse
    {
        $transaction = null;
        if ($id) {
            $transaction = $this->findTransaction($id);

            if (! $this->canEdit($transaction)) {
                return redirect()->route($this->editRouteName, $transaction->id)
                    ->with('danger', 'Transakcję przypisane do zlecenia można edytować wyłącznie poprzez edycję samego zlecenia');
            }
        }

        $transaction = $this->saveTransactional($request->all(), $transaction);

        return redirect()->route($this->editRouteName, $transaction->id)
            ->with('success', __('admin/common.alerts.success_save'));
    }

    protected function saveTransactional(array $data, ?BaseTransactionalModel $transactionalModel = null): BaseTransactionalModel
    {
        $depositDto = $this->createTransactionalDto($data, $transactionalModel);
        $transaction = $this->userBalanceServiceInstance()->add($depositDto);
        $transaction->save();

        return $transaction;
    }

    protected function findTransaction(int $id): BaseTransactionalModel
    {
        return $this->transactionalRepository->modelInstance()
            ->newQuery()
            ->findOrFail($id);
    }

    protected function templatesForUserChoice(): Collection
    {
        return $this->templateRepository->allForChoose($this->templateType);
    }

    protected function userBalanceServiceInstance(): BaseUserBalanceService
    {
        return app()->make($this->userBalanceServiceClass());
    }
}
