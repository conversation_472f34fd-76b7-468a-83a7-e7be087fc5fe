<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\Driver;
use App\Models\Presenters\CityPresenter;
use App\Models\Presenters\VehiclePresenter;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleTerminal;
use App\Project\Storage\CloudStorage;
use App\Project\Vehicle\Enums\VehicleStatuses;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AjaxController extends Controller
{
    /**
     * @return array<int, array{id: mixed, text: mixed}>
     */
    public function users(Request $request): array
    {
        $users = User::query();

        if ($request->filled('q')) {
            $query = $request->get('q');
            $likeString = '%'.$query.'%';
            $users = $users->where('email', 'like', $likeString)
                ->orWhere('id', 'like', $likeString)
                ->orWhereHas('orders', function (Builder $builder) use ($likeString): void {
                    $builder->where('id', 'like', $likeString);
                });
        }

        $users = $users->with('country')->get();

        $results = [];

        $isManager = auth()->user()->isWarehouseManager();

        foreach ($users as $item) {
            $tmp_arr['id'] = $item->id;
            $tmp_arr['text'] = $isManager ? $item->getUserCode() : $item->email.' ('.$item->getUserCode().')';
            $results[] = $tmp_arr;
        }

        return $results;
    }

    public function cities(Request $request): JsonResponse
    {
        $query = $request->get('q');
        $likeString = '%'.$query.'%';

        $cities = City::with('state')
            ->orderBy('name', 'asc')
            ->where('name', 'like', $likeString)
            ->with('state')
            ->get()
            ->map(function (City $city): array {
                $presenter = (new CityPresenter($city));

                return [
                    'id' => $presenter->getKey(),
                    'text' => $presenter->displayFormat(),
                ];
            });

        return response()->json($cities->all());
    }

    public function userBillingData(Request $request): JsonResponse
    {
        $user = User::findOrFail($request->get('userId'));

        if ($user->billing === null) {
            return response()->json(null);
        }

        return response()->json($user->billing->toArray());
    }

    public function drivers(Request $request): JsonResponse
    {
        $query = $request->get('q');
        $likeString = '%'.$query.'%';

        $drivers = Driver::where('company_name', 'like', $likeString)
            ->orWhere('address', 'like', $likeString)
            ->orWhere('email', 'like', $likeString)
            ->limit(10)
            ->get()
            ->map(fn (Driver $driver): array => [
                'id' => $driver->getKey(),
                'text' => $driver->company_name,
                'company_name' => $driver->company_name,
                'link' => route('admin.drivers.edit', $driver),
                'email' => $driver->email,
                'phone_number' => $driver->phone_number,
                'payments' => $driver->payments,
                'w9' => empty($driver->w9) ? null : $driver->w9,
                'w9_link' => empty($driver->w9) ? null : CloudStorage::viewFileRoute($driver->w9, $driver, 'w9'),
            ]);

        return response()->json($drivers->all());
    }

    public function vehicles(Request $request, User $user): JsonResponse
    {
        $query = $request->get('q');
        $likeString = '%'.$query.'%';

        $vehicles = Vehicle::where(function (Builder $query) use ($likeString): void {
            $query->where('vehicle_description', 'like', $likeString)
                ->orWhere('vin_number', 'like', $likeString);
        })
            ->where('status', VehicleStatuses::IN_TERMINAL)
            ->whereHas('terminal', function (Builder $query): void {
                $query->where('slug', VehicleTerminal::HMOTORS_NEWARK_NJ);
            })
            ->where('user_id', $user->getKey())
            ->limit(10)
            ->get()
            ->map(fn (Vehicle $vehicle): array => [
                'id' => $vehicle->getKey(),
                'text' => (new VehiclePresenter($vehicle))->displayFormat(),
            ]);

        return response()->json($vehicles->all());
    }

    public function driverExists(Request $request): JsonResponse
    {
        $companyName = $request->get('company_name');
        $dotNumber = $request->get('dot_number');
        $mcNumber = $request->get('mc_number');
        $driverId = $request->get('driver_id');

        $driver = Driver::query()
            ->when(! empty($driverId), fn (Builder $query): Builder => $query->where('id', '!=', $driverId))
            ->where(function (Builder $query) use ($companyName, $dotNumber, $mcNumber): void {
                $query->when(! empty($companyName), fn (Builder $query): Builder => $query->orWhere('company_name', $companyName))
                    ->when(! empty($dotNumber), fn (Builder $query): Builder => $query->orWhereIn('dot_number', [$mcNumber, $dotNumber]))
                    ->when(! empty($mcNumber), fn (Builder $query): Builder => $query->orWhereIn('mc_number', [$mcNumber, $dotNumber]));
            })
            ->first();

        return response()->json([
            'url' => $driver ? route('admin.drivers.edit', $driver) : null,
        ]);
    }
}
