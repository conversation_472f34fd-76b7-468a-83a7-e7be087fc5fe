<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\Vehicle;
use App\Models\Warehouse;
use Illuminate\Contracts\View\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class VehicleParcelsController extends Controller
{
    public function index(Vehicle $vehicle): View|Factory
    {
        $warehouse = Warehouse::where('slug', Warehouse::NEWARK_SLUG)
            ->firstOrFail();
        $parcels = Parcel::where('warehouse_id', $warehouse->getKey())
            ->where('user_id', $vehicle->user_id)
            ->where(function (Builder $query) use ($vehicle): void {
                $query->where('vehicle_id', $vehicle->getKey())
                    ->orWhereNull('vehicle_id');
            })
            ->get();

        return view('admin.vehicles.parcels', compact('vehicle', 'parcels'));
    }

    public function store(Request $request, Vehicle $vehicle): RedirectResponse
    {
        $parcels = $request->get('parcels', []);
        Parcel::where('user_id', $vehicle->user_id)
            ->whereIn('id', $parcels)
            ->update(['vehicle_id' => $vehicle->getKey()]);

        Parcel::where('user_id', $vehicle->user_id)
            ->where('vehicle_id', $vehicle->getKey())
            ->whereNotIn('id', $parcels)
            ->update(['vehicle_id' => null]);

        return redirect()
            ->route('admin.vehicles.parcels.index', $vehicle)
            ->with('success', __('admin/common.alerts.success_save'));
    }
}
