<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\OrderInvoiceEditRequest;
use App\Models\Order;
use App\Project\Invoice\InvoiceService;
use App\Repositories\OrderInvoiceRepository;
use DB;
use Illuminate\Http\RedirectResponse;

class InvoicesController extends Controller
{
    public function __construct(private readonly OrderInvoiceRepository $orderInvoiceRepository, private readonly InvoiceService $invoiceService)
    {
    }

    public function update(OrderInvoiceEditRequest $request, int $id): RedirectResponse
    {
        /** @var Order $order */
        $order = Order::findOrFail($id);

        $invoice = $this->orderInvoiceRepository->make($request->all(), $order->invoice);
        DB::transaction(function () use ($order, $invoice): void {
            $order->invoice()->save($invoice);
            $this->invoiceService->manageOrder($order);
        });

        return redirect()->back()->with('success', __('admin/common.alerts.success_save'));
    }
}
