<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\VehicleCalculationPrice;
use App\Models\VehicleDeliveryLocation;
use App\Project\VehicleTransportCalculator\Enums\VehicleCalculationPriceTypes;
use App\Repositories\CityRepository;
use App\Repositories\VehicleCalculationPriceRepository;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Yajra\Datatables\Datatables;

class VehicleCalculatorsController extends Controller
{
    public function __construct(private readonly VehicleCalculationPriceRepository $vehicleCalculationPriceRepository, private readonly CityRepository $cityRepository)
    {
    }

    public function index(string $type = VehicleCalculationPriceTypes::VEHICLE): View
    {
        $defaultCity = City::first();
        $transitCities = $this->cityRepository->transitCities(types: [$type]);

        return view('admin.vehicle-calculators.index')->with(compact('type', 'defaultCity', 'transitCities'));
    }

    public function edit(string $type, City $city): View
    {
        $transitCities = $this->cityRepository->transitCities(types: [$type]);
        $city->load([
            'vehicleCalculationPrices' => function (MorphMany $query) use ($type, $transitCities): void {
                $query->where('type', $type)
                    ->whereIn('city_id', $transitCities->pluck('id'));
            },
            'state',
        ]);

        return view('admin.vehicle-calculators.edit')->with(compact('transitCities', 'city', 'type'));
    }

    public function update(Request $request, string $type, City $city): RedirectResponse
    {
        $transitCities = $this->cityRepository->transitCities(types: [$type]);
        $city->load([
            'vehicleCalculationPrices' => function (MorphMany $query) use ($type, $transitCities): void {
                $query->where('type', $type)
                    ->whereIn('city_id', $transitCities->pluck('id'));
            },
        ]);
        $cities = $request->get('cities', []);

        DB::transaction(function () use ($cities, $city, $type): void {
            foreach ($cities as $transitCityId => $transitCityData) {
                $vehicleCalculationPrice = $city->vehicleCalculationPrices->where('city_id', $transitCityId)
                    ->first();

                if (empty($transitCityData['price'])) {
                    if ($vehicleCalculationPrice) {
                        $vehicleCalculationPrice->delete();
                    }

                    continue;
                }

                $transitCityData['city_id'] = $transitCityId;
                $calculation = $this->vehicleCalculationPriceRepository->make(
                    $type,
                    $transitCityData,
                    $city,
                    $vehicleCalculationPrice
                );
                $calculation->save();
            }
        });

        return redirect()->back()->with('success', __('admin/common.alerts.success_save'));
    }

    public function destroy(string $type, City $city): ResponseFactory|Response
    {
        $city->load([
            'vehicleCalculationPrices' => function (MorphMany $query) use ($type): void {
                $query->where('type', $type);
            },
        ]);

        VehicleCalculationPrice::whereIn('id', $city->vehicleCalculationPrices->pluck('id'))
            ->delete();

        return response(['status' => 'ok']);
    }

    public function deliveryPrices(string $type = VehicleCalculationPriceTypes::VEHICLE): View
    {
        $destinations = VehicleDeliveryLocation::with([
            'vehicleCalculationPrice' => function (MorphMany $query) use ($type): void {
                $query->where('type', $type);
            },
            'vehicleCalculationPrice.city',
            'vehicleCalculationPrice.city.state',
        ])->get();

        return view('admin.vehicle-calculators.delivery.edit')->with(compact('destinations', 'type'));
    }

    public function updateDeliveryPrices(Request $request, string $type): RedirectResponse
    {
        $deliveryLocations = $request->get('delivery_locations', []);

        DB::transaction(function () use ($deliveryLocations, $type): void {
            foreach ($deliveryLocations as $deliveryLocationId => $deliveryLocationData) {
                $deliveryLocation = VehicleDeliveryLocation::with([
                    'vehicleCalculationPrice' => function (MorphMany $query) use ($type): void {
                        $query->where('type', $type);
                    },
                ])->findOrFail($deliveryLocationId);
                if ($deliveryLocationData !== null) {
                    foreach ($deliveryLocationData as $calculation) {
                        $vehicleCalculationPrice = null;
                        if (isset($calculation['id'])) {
                            $vehicleCalculationPrice = $deliveryLocation->vehicleCalculationPrice
                                ->where('id', (int) $calculation['id'])
                                ->first();
                        }

                        $calculation = $this->vehicleCalculationPriceRepository->make(
                            $type,
                            $calculation,
                            $deliveryLocation,
                            $vehicleCalculationPrice
                        );

                        $calculation->save();
                    }
                }

                $toDelete = collect($deliveryLocation->vehicleCalculationPrice->pluck('id')->toArray())
                    ->diff(collect($deliveryLocationData)->pluck('id'));
                if (! empty($toDelete)) {
                    VehicleCalculationPrice::whereIn('id', $toDelete)->delete();
                }
            }
        });

        return redirect()->back()->with('success', __('admin/common.alerts.success_save'));
    }

    public function datatable(string $type): JsonResponse
    {
        $transitCities = $this->cityRepository->transitCities(types: [$type]);
        $transitCitiesSelects = [];
        foreach ($transitCities as $transitCity) {
            $transitCitiesSelects[] = DB::raw(
                "MAX(CASE WHEN vehicle_calculation_prices.city_id = {$transitCity->getKey()} THEN vehicle_calculation_prices.price ELSE NULL END) AS 'city_{$transitCity->getKey()}'"
            );
        }

        $cities = City::join('vehicle_calculation_prices', function (JoinClause $join) use ($type, $transitCities): void {
            $join->on('vehicle_calculation_prices.calculable_id', '=', 'cities.id')
                ->where('vehicle_calculation_prices.calculable_type', '=', City::class)
                ->where('vehicle_calculation_prices.type', '=', $type);
            if (! $transitCities->isEmpty()) {
                $join->whereIn('vehicle_calculation_prices.city_id', $transitCities->pluck('id')->toArray());
            }
        })->join('states', 'cities.state_id', '=', 'states.id')
            ->select(
                ['cities.*', 'states.name as state', ...$transitCitiesSelects]
            )->groupBy('cities.id');

        $dataTables = Datatables::of($cities)
            ->escapeColumns([]);

        foreach ($transitCities as $transitCity) {
            $dataTables->editColumn("city_{$transitCity->getKey()}", function (City $city) use ($transitCity) {
                $price = $city->{"city_{$transitCity->getKey()}"};

                return $price ? '$ '.price_to_string($price) : null;
            });
        }

        $dataTables->editColumn('name', fn (City $city): string => '<a href="'.route('admin.vehicle-calculators.edit', [$type, $city]).'" class="btn btn-primary"><i class="glyphicon glyphicon-edit"></i>
                          '.$city->name.'
                    </a>')->addColumn('action', fn (City $city): string => '<a href="'.route('admin.vehicle-calculators.destroy', [$type, $city]).'" class="btn btn-danger delete-item" data-text="Czy na pewno chcesz usunąć tą kalkulację?"><i class="fa fa-trash fa-fw"></i>
                     Usuń
                    </a>');

        return $dataTables->make(true);
    }
}
