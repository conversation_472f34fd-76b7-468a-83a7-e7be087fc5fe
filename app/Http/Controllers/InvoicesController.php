<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Project\Invoice\InvoiceService;
use Gate;

class InvoicesController extends Controller
{
    public function __construct(private readonly InvoiceService $invoiceService)
    {
    }

    public function show(int $orderId): void
    {
        $order = Order::findOrFail($orderId);

        if (Gate::denies('invoices.can-show', $order)) {
            abort('404');
        }

        $invoice = $this->invoiceService->invoiceForOrder($order);

        $invoice->show($invoice->name);
    }
}
