<?php

namespace App\Http\Controllers;

use App\Project\Point\PointsProgressBarDataProvider;
use App\Repositories\PointsRepository;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use Throwable;

class PointsController extends Controller
{
    public function __construct(private readonly PointsRepository $pointsRepository)
    {
    }

    /**
     * @throws Throwable
     */
    public function index(Request $request): array|View|string
    {
        $filters = $request->all();
        $points = $this->pointsRepository->forUser(auth()->user(), $filters);
        $points = $this->setPaginatorSettings($points, $request);

        if ($request->ajax()) {
            return view('points.partials.table-content', compact('points'))->render();
        }

        $progressBarData = PointsProgressBarDataProvider::forUser(auth()->user());

        return view('points.index')->with(compact('points', 'progressBarData'));
    }

    private function setPaginatorSettings(
        LengthAwarePaginator $transactions,
        Request $request
    ): LengthAwarePaginator {
        $appends = $request->all();
        unset($appends['page']);
        $transactions->setPath(route('points.index'))
            ->appends($appends);

        return $transactions;
    }
}
