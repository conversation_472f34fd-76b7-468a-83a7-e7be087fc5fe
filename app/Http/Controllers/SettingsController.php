<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\UserBillingRequest;
use App\Http\Requests\UserSettingsRequest;
use App\Models\Country;
use App\Models\User;
use App\Models\VehicleDeliveryLocation;
use App\Models\VehicleTitleType;
use App\Project\Option\Enums\GlobalOptions;
use App\Project\Option\Enums\UserOptions;
use App\Project\Option\Providers\GlobalOptionsProvider;
use App\Project\Option\Providers\UserOptionsProvider;
use App\Project\User\UserAccountTypeProvider;
use App\Project\VehicleTransportCalculator\VehiclePriceListProvider;
use App\Repositories\UserBillingRepository;
use App\Repositories\UserRepository;
use App\ViewModels\Dashboard\Settings\SettingsCommissionsViewModel;
use App\ViewModels\Dashboard\Settings\SettingsPriceListViewModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;
use Meta;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class SettingsController extends Controller
{
    private const PREMIUM_DELIVERY_LOCATION_SLUGS = [
        'bremerhaven',
        'rotterdam',
    ];

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly UserBillingRepository $billingRepository,
        private readonly UserAccountTypeProvider $userAccountTypeProvider,
        private readonly VehiclePriceListProvider $vehiclePriceListProvider,
    ) {
    }

    public function settings(Request $request): View
    {
        $tab = $request->get('tab', 'contact');
        $countries = Country::query()->get()->pluck('name', 'id');

        return view("profile.settings.{$tab}")->with(compact('tab', 'countries'));
    }

    public function invoice(): View
    {
        $billing = auth()->user()->billing;
        $countries = Country::query()->get()->pluck('name', 'id');

        return view('profile.settings.invoice')->with(compact('billing', 'countries'));
    }

    public function commissions(
        UserOptionsProvider $userOptionsProvider,
        GlobalOptionsProvider $globalOptionsProvider
    ): View {
        $defaultFee = (int) $globalOptionsProvider->option(GlobalOptions::VEHICLES_PRICES_DEFAULT_BROKER_FEE, 0);
        $iaaFee = (int) $userOptionsProvider->option(UserOptions::BROKER_FEE_IAA, $defaultFee, true);
        $copartFee = (int) $userOptionsProvider->option(UserOptions::BROKER_FEE_COPART, $defaultFee, true);
        $otherFee = (int) $userOptionsProvider->option(UserOptions::BROKER_FEE_OTHER, $defaultFee, true);
        $cwPaysFee = (int) $userOptionsProvider->option(UserOptions::BROKER_FEE_CW_PAYS, 0, true);

        return view('profile.settings.commissions')->with([
            'viewModel' => new SettingsCommissionsViewModel($iaaFee, $copartFee, $otherFee, $cwPaysFee),
        ]);
    }

    public function priceList(
        Request $request,
        GlobalOptionsProvider $optionsProvider,
    ): View|string {
        $isPremium = $request->boolean('isPremium');
        $deliveryLocations = VehicleDeliveryLocation::has('vehicleCalculationPrice')
            ->when($isPremium, fn (Builder $query) => $query->whereIn('slug', self::PREMIUM_DELIVERY_LOCATION_SLUGS))
            ->get();
        /** @var VehicleDeliveryLocation|null $vehicleDeliveryLocation */
        $vehicleDeliveryLocation = $deliveryLocations->firstWhere(
            'id',
            $request->get('delivery_location')
        );
        $accountType = ($this->userAccountTypeProvider)();

        if ($vehicleDeliveryLocation !== null) {
            $vehiclePrices = ($this->vehiclePriceListProvider)(
                vehicleDeliveryLocation: $vehicleDeliveryLocation,
                accountType: $accountType,
                search: $request->get('search'),
                hideCities: true,
                isPremium: $isPremium
            );
        }

        $note = $optionsProvider->option(GlobalOptions::VEHICLES_CALCULATOR_NOTE);

        $viewModel = new SettingsPriceListViewModel(
            $deliveryLocations,
            $vehicleDeliveryLocation ?? null,
            $vehiclePrices ?? null,
            $note,
            $isPremium
        );

        if ($request->ajax()) {
            return view('profile.settings._price-list-table-content', compact('viewModel'))->render();
        }

        return view('profile.settings.vehicles-price-list')->with(compact('viewModel'));
    }

    public function exportPriceList(Request $request): BinaryFileResponse
    {
        $accountType = ($this->userAccountTypeProvider)();

        return $this->vehiclePriceListProvider->export(
            accountType: $accountType,
            hideCities: true,
            isPremium: $request->boolean('isPremium')
        );
    }

    public function titleTypes(Request $request, ?string $status = null): array|View|string
    {
        Meta::title('Sprawdź jakie title można eksportować z USA');
        $search = $request->get('search');
        $status = $status ?: VehicleTitleType::OK_STATUS;
        $vehicleTitleTypes = VehicleTitleType::when(
            empty($search),
            fn (Builder $query) => $query->where('status', $status),
            fn (Builder $query) => $query->where('name', 'like', "%{$search}%")
        )->orderBy('name', 'ASC')
            ->paginate(40);
        $statuses = VehicleTitleType::STATUSES;
        // Paginator settings
        $appends = $request->all();
        unset($appends['page']);
        $vehicleTitleTypes->setPath(route('settings.title-types', $status))
            ->appends($appends);

        if ($request->ajax()) {
            return view(
                'profile.settings.title-types.partials.table-content',
                compact('vehicleTitleTypes', 'statuses', 'status')
            )->render();
        }

        return view('profile.settings.title-types.index')
            ->with(compact('vehicleTitleTypes', 'statuses', 'status'));
    }

    public function postSettings(UserSettingsRequest $request): RedirectResponse
    {
        /** @var User $user */
        $user = auth()->user();

        if ($request->filled('new_password')) {
            if (! Hash::check($request->get('password'), $user->password)) {
                return redirect()->back()->with(
                    'danger',
                    __('settings.changes_rejected')
                );
            }

            $user->password = Hash::make($request->get('new_password'));
            $user->save();
        }

        $this->userRepository->update($user, $request);

        return redirect()->back()->with('success', __('settings.success'));
    }

    public function postInvoiceSettings(UserBillingRequest $request): RedirectResponse
    {
        $user = auth()->user();

        $billing = $this->billingRepository->make($request->all(), $user->billing);
        $billing->user()->associate($user);
        $billing->save();

        return redirect()->back()->with('success', __('settings.success'));
    }
}
