<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\StoreBusinessCardRequest;
use App\Models\UserBusinessCard;
use App\Project\Storage\CloudStorage;
use Illuminate\Http\Response;
use <PERSON><PERSON>zart\Assert\Assert;

class BusinessCardController extends Controller
{
    public function __construct(
        private readonly CloudStorage $cloudStorage,
    ) {
    }

    public function store(StoreBusinessCardRequest $request): Response
    {
        $user = $request->user();
        $businessCard = $user->businessCard ?? new UserBusinessCard();
        $businessCard->user()->associate($request->user());
        if (! empty($businessCard->desktop_image)) {
            $this->cloudStorage->removeFile($businessCard->desktop_image, $businessCard, 'desktop_image');
        }
        if (! empty($businessCard->mobile_image)) {
            $this->cloudStorage->removeFile($businessCard->mobile_image, $businessCard, 'mobile_image');
        }
        $businessCard->desktop_image = null;
        $businessCard->mobile_image = null;

        if ($request->hasFile('desktop_image')) {
            $businessCard->desktop_image = $this->cloudStorage->storeImage(
                image: $request->file('desktop_image'),
                model: $businessCard,
                attributeName: 'desktop_image',
                width: 1320,
                quality: 90,
            );
        }
        if ($request->hasFile('mobile_image')) {
            $businessCard->mobile_image = $this->cloudStorage->storeImage(
                image: $request->file('mobile_image'),
                model: $businessCard,
                attributeName: 'mobile_image',
                width: 540,
                quality: 90,
            );
        }
        $businessCard->save();

        return response()->noContent();
    }

    public function deleteImage(string $type): Response
    {
        Assert::oneOf($type, ['desktop_image', 'mobile_image']);
        /** @var UserBusinessCard $businessCard */
        $businessCard = auth()->user()->businessCard;
        $this->cloudStorage->removeFile(
            fileName: $businessCard->getAttribute($type),
            model: $businessCard,
            attributeName: $type
        );
        $businessCard->setAttribute($type, null);
        $businessCard->save();

        return response()->noContent();
    }
}
