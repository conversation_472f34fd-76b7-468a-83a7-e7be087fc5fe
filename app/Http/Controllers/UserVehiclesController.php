<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserVehicleRequest;
use App\Models\Port;
use App\Models\Presenters\PortPresenter;
use App\Models\Presenters\UserVehiclePresenter;
use App\Models\UserVehicle;
use App\Models\VehicleShippingLine;
use App\Project\Vehicle\Enums\VehicleKeys;
use App\Project\Vehicle\Enums\VehicleTabs;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Meta;

class UserVehiclesController extends Controller
{
    public function show(UserVehicle $userVehicle): View
    {
        $this->authorize('user-vehicles.can-show', $userVehicle);
        $userVehicle->load('shippingLine');

        $userVehicle = new UserVehiclePresenter($userVehicle);
        Meta::title(__('user-vehicles.meta.show.title', ['description' => $userVehicle->vehicleDescription()]));

        return view('dealers.user-vehicles.show')
            ->with(compact('userVehicle'));
    }

    public function create(): View
    {
        Meta::title(__('user-vehicles.meta.create.title'));

        return view('dealers.user-vehicles.create')
            ->with($this->formData());
    }

    public function store(StoreUserVehicleRequest $request): RedirectResponse
    {
        $userVehicle = $request->user()
            ->userVehicles()
            ->create($this->saveData($request));

        return redirect()
            ->route('dealers.user-vehicles.show', $userVehicle)
            ->with('success', __('user-vehicles.store.flash.created'));
    }

    public function edit(UserVehicle $userVehicle): View
    {
        $this->authorize('user-vehicles.cen-edit', $userVehicle);

        $userVehicle = new UserVehiclePresenter($userVehicle);
        Meta::title(__('user-vehicles.meta.edit.title', ['description' => $userVehicle->vehicleDescription()]));

        return view('dealers.user-vehicles.edit')
            ->with($this->formData() + compact('userVehicle'));
    }

    public function update(StoreUserVehicleRequest $request, UserVehicle $userVehicle): RedirectResponse
    {
        $this->authorize('user-vehicles.cen-edit', $userVehicle);

        $userVehicle->update($this->saveData($request));

        return redirect()
            ->route('dealers.user-vehicles.show', $userVehicle)
            ->with('success', __('user-vehicles.update.flash.updated'));
    }

    private function formData(): array
    {
        $statuses = VehicleTabs::selectForDealer(VehicleTabs::forDealer());
        $keys = VehicleKeys::forSelect();
        $ports = Port::query()
            ->with('country')
            ->orderBy('name')
            ->get()
            ->mapWithKeys(
                fn (Port $port): array => [$port->id => (new PortPresenter($port))->displayFormat()]
            );
        $vehicleShippingLines = VehicleShippingLine::query()
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id');

        return compact('statuses', 'keys', 'ports', 'vehicleShippingLines');
    }

    private function saveData(Request $request): array
    {
        return array_merge(
            $request->validated(),
            [
                'eta' => $request->get('eta') ? Carbon::createFromFormat(
                    'd.m.Y',
                    $request->get('eta')
                )->startOfDay() : null,
            ]
        );
    }
}
