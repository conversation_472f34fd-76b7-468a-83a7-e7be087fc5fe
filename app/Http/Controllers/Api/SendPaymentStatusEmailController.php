<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SendPaymentStatusEmailRequest;
use App\Models\Driver;
use App\Project\Driver\DriverMailer;
use Illuminate\Http\Response;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\App;

class SendPaymentStatusEmailController extends Controller
{
    public function __invoke(DriverMailer $driverMailer, SendPaymentStatusEmailRequest $request): Response
    {
        Driver::where('email', $request->input('email'))->firstOrFail();

        $locale = App::getLocale();
        App::setLocale('en');
        $driverMailer(
            view: 'emails.driver.payment-status-link',
            data: [
                'title' => __('email.driver.payment_status_link.title'),
                'uri' => $request->input('uri'),
            ],
            callback: function (Message $message) use ($request): void {
                $message->to($request->input('email'))
                    ->subject(__('email.driver.payment_status_link.subject'));
            }
        );
        App::setLocale($locale);

        return response()->noContent();
    }
}
