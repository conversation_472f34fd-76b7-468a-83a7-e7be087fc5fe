<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Project\Vehicle\Enums\VehicleTabs;
use Illuminate\Http\JsonResponse;

class VehicleStatusesController extends Controller
{
    public function index(): JsonResponse
    {
        $tabs = VehicleTabs::selectForDealer(VehicleTabs::forDealer());

        return response()->json([
            'data' => $tabs,
        ]);
    }
}
