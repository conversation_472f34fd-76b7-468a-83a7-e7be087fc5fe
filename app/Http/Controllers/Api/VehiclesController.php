<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\VehicleResource;
use App\Models\DealerClient;
use App\Models\UserVehicle;
use App\Models\Vehicle;
use App\Project\Dealer\DealerVehiclesProvider;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class VehiclesController extends Controller
{
    public function index(Request $request, DealerVehiclesProvider $dealerVehiclesProvider): AnonymousResourceCollection
    {
        $dealerClient = DealerClient::where('email', $request->input('email'))->firstOrFail();

        $vehicles = $dealerVehiclesProvider->forDealerClient($dealerClient);

        return VehicleResource::collection($vehicles);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function show(string $shareUuid): VehicleResource
    {
        $vehicle = Vehicle::where('share_uuid', $shareUuid)
            ->with('user.businessCard', 'shippingLine', 'order.invoice', 'deliveryLocation')
            ->where('share_link_disabled', false)
            ->first();

        if ($vehicle === null) {
            $vehicle = UserVehicle::where('share_uuid', $shareUuid)
                ->with('user.businessCard', 'shippingLine')
                ->where('share_link_disabled', false)
                ->first();
        }

        if ($vehicle === null) {
            throw (new ModelNotFoundException())
                ->setModel(Vehicle::class, $shareUuid);
        }

        return new VehicleResource($vehicle);
    }
}
