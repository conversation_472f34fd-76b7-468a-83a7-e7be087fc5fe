<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\DriverVehicle;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method DriverVehicle make($attributes = [], ?Model $parent = null)
 * @method DriverVehicle create($attributes = [], ?Model $parent = null)
 */
class DriverVehicleFactory extends Factory
{
    protected $model = DriverVehicle::class;

    public function definition(): array
    {
        return [
            'paid_at' => $this->faker->dateTimeThisYear,
            'transport_price' => $this->faker->numberBetween(),
        ];
    }
}
