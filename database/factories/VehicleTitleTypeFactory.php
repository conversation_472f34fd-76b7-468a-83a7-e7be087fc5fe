<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\VehicleTitleType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method VehicleTitleType make($attributes = [], ?Model $parent = null)
 * @method VehicleTitleType create($attributes = [], ?Model $parent = null)
 */
class VehicleTitleTypeFactory extends Factory
{
    protected $model = VehicleTitleType::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->text,
            'status' => $this->faker->randomElement(VehicleTitleType::STATUSES),
        ];
    }
}
