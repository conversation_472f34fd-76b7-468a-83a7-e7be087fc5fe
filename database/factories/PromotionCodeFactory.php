<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PromotionCode;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method PromotionCode make($attributes = [], ?Model $parent = null)
 * @method PromotionCode create($attributes = [], ?Model $parent = null)
 */
class PromotionCodeFactory extends Factory
{
    protected $model = PromotionCode::class;

    public function definition(): array
    {
        return [
            'code' => $this->faker->text,
            'value' => $this->faker->numberBetween(1, 10),
            'addition_type' => PromotionCode::MULTIPLE_ADDITION_TYPE,
            'active' => $this->faker->boolean,
            'valid_from' => $this->faker->dateTime,
            'valid_to' => $this->faker->dateTime,
        ];
    }
}
