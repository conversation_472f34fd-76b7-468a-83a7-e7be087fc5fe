<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Notification;
use App\Models\User;
use App\Project\Notification\Notification as NotificationValueObject;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationFactory extends Factory
{
    protected $model = Notification::class;

    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement([
                NotificationValueObject::VALUATION_TYPE,
                NotificationValueObject::VEHICLE_TYPE,
                NotificationValueObject::PARCEL_TYPE,
                NotificationValueObject::MESSAGE_TYPE,
                NotificationValueObject::INFO_TYPE,
            ]),
            'content' => [
                'pl' => $this->faker->text,
                'en' => $this->faker->text,
            ],
            'link' => $this->faker->url,
            'displayed_at' => $this->faker->boolean ? $this->faker->dateTimeThisYear : null,
            'user_id' => fn () => User::factory()->create()->getKey(),
        ];
    }
}
