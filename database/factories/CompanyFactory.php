<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'slug' => $this->faker->slug,
            'address' => $this->faker->address,
            'post_code' => $this->faker->postcode,
            'city' => $this->faker->city,
            'bank_account_number' => $this->faker->numerify('##############'),
            'bank_swift_code' => $this->faker->swiftBicNumber,
            'bank_branch_number' => $this->faker->numerify('###'),
            'bank_address' => $this->faker->address,
            'wise_account_name' => $this->faker->safeEmail,
            'revoult_account_name' => $this->faker->safeEmail,
            'zelle_account_name' => $this->faker->safeEmail,
        ];
    }
}
