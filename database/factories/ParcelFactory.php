<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Parcel;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method Parcel make($attributes = [], ?Model $parent = null)
 * @method Parcel create($attributes = [], ?Model $parent = null)
 */
class ParcelFactory extends Factory
{
    protected $model = Parcel::class;

    public function definition(): array
    {
        return [
            'user_id' => fn () => User::factory()->create()->getKey(),
            'warehouse_id' => fn () => Warehouse::factory()->create()->getKey(),
            'order_number' => $this->faker->randomNumber(8),
            'tracking_number' => $this->faker->randomNumber(8),
            'sender' => $this->faker->company,
            'description' => $this->faker->text,
        ];
    }
}
