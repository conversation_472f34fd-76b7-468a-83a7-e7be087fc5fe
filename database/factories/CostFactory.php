<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Cost;
use App\Project\Cost\Enums\AccountTypes;
use App\Project\Cost\Enums\CostCategories;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method Cost make($attributes = [], ?Model $parent = null)
 * @method Cost create($attributes = [], ?Model $parent = null)
 */
class CostFactory extends Factory
{
    protected $model = Cost::class;

    public function definition(): array
    {
        return [
            'account_type' => $this->faker->randomElement(AccountTypes::ALL_TYPES),
            'title' => $this->faker->text,
            'amount' => $this->faker->numberBetween(-100000, 100000),
            'category' => $this->faker->randomElement(CostCategories::ALL_TYPES),
            'created_at' => $this->faker->dateTimeThisYear,
            'updated_at' => $this->faker->dateTimeThisYear,
        ];
    }
}
