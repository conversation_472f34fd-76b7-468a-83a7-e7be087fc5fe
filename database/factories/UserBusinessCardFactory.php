<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\UserBusinessCard;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method UserBusinessCard make($attributes = [], ?Model $parent = null)
 * @method UserBusinessCard create($attributes = [], ?Model $parent = null)
 */
class UserBusinessCardFactory extends Factory
{
    protected $model = UserBusinessCard::class;

    public function definition(): array
    {
        return [];
    }
}
