<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Notice;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method Notice make($attributes = [], ?Model $parent = null)
 * @method Notice create($attributes = [], ?Model $parent = null)
 */
class NoticeFactory extends Factory
{
    protected $model = Notice::class;

    public function definition(): array
    {
        return [
            'title' => [
                'en' => $this->faker->text,
                'pl' => $this->faker->text,
            ],
            'content' => [
                'en' => $this->faker->text,
                'pl' => $this->faker->text,
            ],
            'show_to_all_users' => $this->faker->boolean,
            'send_notifications' => $this->faker->boolean,
        ];
    }
}
