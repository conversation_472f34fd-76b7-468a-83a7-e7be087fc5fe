<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\VehicleCalculationPrice;
use App\Project\VehicleTransportCalculator\Enums\VehicleCalculationPriceTypes;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleCalculationPriceFactory extends Factory
{
    protected $model = VehicleCalculationPrice::class;

    public function definition(): array
    {
        return [
            'price' => $this->faker->numberBetween(1000, 100000),
            'type' => $this->faker->randomElement([
                VehicleCalculationPriceTypes::VEHICLE,
                VehicleCalculationPriceTypes::MOTORCYCLE,
                VehicleCalculationPriceTypes::ATV,
            ]),
        ];
    }
}
