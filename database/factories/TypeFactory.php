<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Type;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method Type make($attributes = [], ?Model $parent = null)
 * @method Type create($attributes = [], ?Model $parent = null)
 */
class TypeFactory extends Factory
{
    protected $model = Type::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['Parcels', 'Vehicles', 'Vehicle reports', 'Other', 'Security deposit']),
            'slug' => $this->faker->randomElement(['parcels', 'vehicles', 'vehicle-reports', 'other', 'deposit']),
        ];
    }
}
