<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Vehicle;
use App\Project\Vehicle\Enums\VehicleStatuses;
use App\Project\Vehicle\Enums\VehicleTypes;
use App\Project\Vehicle\Enums\VehicleTypesOfServices;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Vehicle>
 */
class VehicleFactory extends Factory
{
    protected $model = Vehicle::class;

    public function definition(): array
    {
        return [
            'status' => $this->faker->randomElement(VehicleStatuses::ALL_TYPES),
            'type_of_service' => $this->faker->randomElement(VehicleTypesOfServices::ALL_TYPES),
            'vehicle_description' => $this->faker->text(),
            'vin_number' => $this->faker->randomElement(['JF1GD77687L502470', '1B3CB5HA7BD211369', '1GBCS1046R2917790']),
            'vehicle_type' => $this->faker->randomElement(VehicleTypes::ALL_TYPES),
            'seller_type' => 'private',
            'share_uuid' => $this->faker->uuid,
        ];
    }
}
