<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\DealerClient;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method DealerClient make($attributes = [], ?Model $parent = null)
 * @method DealerClient create($attributes = [], ?Model $parent = null)
 */
class DealerClientFactory extends Factory
{
    protected $model = DealerClient::class;

    public function definition(): array
    {
        return [
            'email' => $this->faker->email,
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'phone_number' => $this->faker->phoneNumber,
        ];
    }
}
