<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\OrderStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method OrderStatus make($attributes = [], ?Model $parent = null)
 * @method OrderStatus create($attributes = [], ?Model $parent = null)
 */
class OrderStatusFactory extends Factory
{
    protected $model = OrderStatus::class;

    public function definition(): array
    {
        return [
            'name' => [
                'pl' => $this->faker->randomElement([
                    'Oczekuje na wycenę',
                    'Wycenione',
                    'Opłacone',
                    'W trakcie realizacji',
                    'Zakończone',
                ]),
                'en' => $this->faker->randomElement([
                    'Awaiting price',
                    'Priced',
                    'Paid',
                    'In progress',
                    'Finished',
                ]),
            ],
            'slug' => $this->faker->randomElement([
                OrderStatus::WAITING_SLUG,
                OrderStatus::PRICED_SLUG,
                OrderStatus::PAID_SLUG,
                OrderStatus::IN_PROGRESS_SLUG,
                OrderStatus::FINISHED_SLUG,
            ]),
            'order' => $this->faker->numberBetween(0, 10),
        ];
    }
}
