<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Project\Permission\Enum\PermissionsNames;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionsSeeder extends Seeder
{
    public function run(): void
    {
        foreach (PermissionsNames::ALL as $permission) {
            $permissionModel = Permission::where(
                ['name' => $permission]
            )->first() ?: new Permission();
            $permissionModel->name = $permission;
            $permissionModel->save();
        }
    }
}
