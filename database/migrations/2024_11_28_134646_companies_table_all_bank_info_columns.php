<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->string('bank_account_number')->nullable()->after('city');
            $table->string('bank_swift_code')->nullable()->after('bank_account_number');
            $table->string('bank_branch_number')->nullable()->after('bank_swift_code');
            $table->text('bank_address')->nullable()->after('bank_swift_code');
            $table->string('wise_account_name')->nullable()->after('bank_address');
            $table->string('revoult_account_name')->nullable()->after('wise_account_name');
            $table->string('zelle_account_name')->nullable()->after('revoult_account_name');
        });
    }

    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn([
                'bank_account_number',
                'bank_swift_code',
                'bank_branch_number',
                'bank_address',
                'wise_account_name',
                'revoult_account_name',
                'zelle_account_name',
            ]);
        });
    }
};
