<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dateTime('paid_at')->nullable(false)->change();
        });

        \DB::statement('ALTER TABLE transactions MODIFY paid_at DATETIME NOT NULL AFTER company_id');
    }

    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dateTime('paid_at')->nullable()->change();
        });
    }
};
