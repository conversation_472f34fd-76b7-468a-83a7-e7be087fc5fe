<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class VehiclesTableAddVehicleLengthColumn extends Migration
{
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->unsignedInteger('vehicle_length')->after('vehicle_auction_location_email')->nullable();
        });
    }

    public function down(): void
    {
        //
    }
}
