<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class TransactionTemplatesTableChangeTranslationsColumnToTitle extends Migration
{
    public function up(): void
    {
        Schema::table('transaction_templates', function (Blueprint $table) {
            $table->json('translations')->change();
            $table->dropColumn('title_template');
            $table->renameColumn('translations', 'title');
        });
    }

    public function down(): void
    {
        Schema::table('transaction_templates', function (Blueprint $table) {
            $table->renameColumn('title', 'translations');
        });
    }
}
