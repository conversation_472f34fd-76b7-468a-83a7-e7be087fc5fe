<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UserVehiclesTableAddMissingColumns extends Migration
{
    public function up(): void
    {
        Schema::table('user_vehicles', function (Blueprint $table): void {
            $table->renameColumn('images_user', 'images_collection');
        });

        Schema::table('user_vehicles', function (Blueprint $table): void {
            $table->json('images_terminal')->after('images_collection')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('user_vehicles', function (Blueprint $table): void {
            $table->renameColumn('images_collection', 'images_user');
            $table->dropColumn('images_terminal');
        });
    }
}
