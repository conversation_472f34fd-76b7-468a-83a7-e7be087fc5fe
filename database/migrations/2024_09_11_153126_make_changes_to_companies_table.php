<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('country');
            $table->dropColumn('active');
            $table->dropColumn('phone');
            $table->unsignedInteger('country_id')->nullable()->after('city');

            $table->foreign('country_id')->references('id')->on('countries');
        });
    }

    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->string('type')->nullable();
            $table->string('country')->nullable();
            $table->boolean('active')->default(1);
            $table->string('phone')->nullable();
        });
    }
};
