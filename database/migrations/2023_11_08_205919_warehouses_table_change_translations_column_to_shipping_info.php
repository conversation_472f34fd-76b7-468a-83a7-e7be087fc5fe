<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class WarehousesTableChangeTranslationsColumnToShippingInfo extends Migration
{
    public function up(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->dropColumn('shipping_info');
            $table->renameColumn('translations', 'shipping_info');
        });
    }

    public function down(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->renameColumn('shipping_info', 'translations');
        });
    }
}
