<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeAccountTypeTableCategoryColumnsToNotNullable extends Migration
{
    public function up(): void
    {
        Schema::table('costs', function (Blueprint $table) {
            $table->string('account_type')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('costs', function (Blueprint $table) {
            $table->string('account_type')->nullable()->change();
        });
    }
}
