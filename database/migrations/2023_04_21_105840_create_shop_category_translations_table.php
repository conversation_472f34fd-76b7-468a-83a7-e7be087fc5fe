<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopCategoryTranslationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_category_translations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('shop_category_id');
            $table->string('slug');
            $table->string('name');
            $table->string('locale')->index();
            $table->unique(['shop_category_id', 'locale']);

            $table->foreign('shop_category_id')->references('id')->on('shop_categories')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_category_translations');
    }
}
