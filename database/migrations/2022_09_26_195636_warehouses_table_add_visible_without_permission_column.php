<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class WarehousesTableAddVisibleWithoutPermissionColumn extends Migration
{
    public function up(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->boolean('visible_without_permission')->default(1)->after('needs_permission');
        });
    }

    public function down(): void
    {
        //
    }
}
