<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserVehiclesTable extends Migration
{
    public function up(): void
    {
        Schema::create('user_vehicles', function (Blueprint $table) {
            $table->increments('id');
            $table->string('brand');
            $table->string('model');
            $table->year('year');
            $table->string('vin_number');
            $table->string('status');
            $table->string('keys')->nullable();
            $table->string('container_number')->nullable();
            $table->dateTime('eta')->nullable();
            $table->unsignedInteger('port_id')->nullable();
            $table->unsignedInteger('vehicle_shipping_line_id')->nullable();
            $table->unsignedInteger('user_id');

            $table->timestamps();

            $table->foreign('vehicle_shipping_line_id')
                ->references('id')
                ->on('vehicle_shipping_lines');

            $table->foreign('user_id')
                ->references('id')
                ->on('users');

            $table->foreign('port_id')
                ->references('id')
                ->on('ports');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_vehicles');
    }
}
