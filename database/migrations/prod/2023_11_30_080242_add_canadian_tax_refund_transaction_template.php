<?php

use App\Models\TransactionTemplate;
use Illuminate\Database\Migrations\Migration;

class AddCanadianTaxRefundTransactionTemplate extends Migration
{
    public function up(): void
    {
        $template = new TransactionTemplate();
        $template->title = ['pl' => 'Zwrot podatku z Kanady', 'en' => 'Canadian tax refund'];
        $template->slug = 'canadian_tax_refund';
        $template->type = TransactionTemplate::TRANSACTION_TYPE;
        $template->available_for_choose = true;
        $template->save();
    }
}
