<?php

use App\Models\Warehouse;
use Illuminate\Database\Migrations\Migration;

class MakeNewarkWarehouseInvisibleForUsersWithoutPermission extends Migration
{
    public function up(): void
    {
        $newark = Warehouse::whereSlug(Warehouse::NEWARK_SLUG)
            ->firstOrFail();
        $newark->visible_without_permission = 0;
        $newark->save();
    }

    public function down(): void
    {
        //
    }
}
