<?php

use App\Models\TransactionTemplate;
use Illuminate\Database\Migrations\Migration;

class AddRefundFromCarrierTransactionTemplate extends Migration
{
    public function up(): void
    {
        $template = new TransactionTemplate();
        $template->title = ['pl' => 'Zwrot od kierowcy', 'en' => 'Refund from carrier'];
        $template->slug = 'refund_from_carrier';
        $template->type = TransactionTemplate::TRANSACTION_TYPE;
        $template->available_for_choose = true;
        $template->save();
    }
}
