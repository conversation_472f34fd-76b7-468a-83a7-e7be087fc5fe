<?php

declare(strict_types=1);

use App\Models\User;
use App\Project\Permission\Enum\PermissionsNames;
use Illuminate\Database\Migrations\Migration;

class AssignVehiclesPriceListShowPermission extends Migration
{
    public function up(): void
    {
        User::whereHas('vehicles')
            ->get()
            ->each(function (User $user): void {
                $user->givePermissionTo(PermissionsNames::VEHICLES_PRICE_LIST_SHOW);
            });
    }

    public function down(): void
    {
        //
    }
}
