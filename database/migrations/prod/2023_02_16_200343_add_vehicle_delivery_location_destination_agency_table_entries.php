<?php

use App\Models\VehicleDeliveryLocation;
use App\Models\VehicleDestinationAgency;
use Illuminate\Database\Migrations\Migration;

class AddVehicleDeliveryLocationDestinationAgencyTableEntries extends Migration
{
    public function up(): void
    {
        $rotterdam = VehicleDeliveryLocation::where('name', 'Rotterdam')->first();
        $agencies = VehicleDestinationAgency::whereIn('slug', ['nl-shipping-bv', 'marlog-car-handling']
        )->get();
        $rotterdam->vehicleDestinationAgencies()->sync($agencies->pluck('id')->toArray());
    }

    public function down(): void
    {
        //
    }
}
