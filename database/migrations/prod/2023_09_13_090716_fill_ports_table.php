<?php

use App\Models\Country;
use App\Models\Port;
use Illuminate\Database\Migrations\Migration;

class FillPortsTable extends Migration
{
    private const MAP_COUNTRIES = [
        'United States of America' => 'United States',
        'Cote D\'ivoire' => 'Côte d’Ivoire',
        'Libyan Arab Jamahiriya' => 'Libya',
        'People\'s Republic of China' => 'China',
        'Republic of Korea' => 'South Korea',
        'Viet Nam' => 'Vietnam',
        'Netherlands Antilles' => 'Netherlands',
        'Korea, Democratic People\'s Republic of' => 'North Korea',
        'United States Minor Outlying Islands' => 'United States',
        'Russian Federation' => 'Russia',
        'Micronesia, Federated States of' => 'Micronesia',
        'Trinidad and Tobago' => 'Trinidad & Tobago',
        'Virgin Islands, U.S.' => 'United States',
        'Syrian Arab Republic' => 'Syria',
    ];

    public function up(): void
    {
        $csv = array_map(function ($line) {
            return str_getcsv($line, ';'); // using ; as delimiter
        }, file(database_path('seeders/csv/ports.csv')));
        $headers = array_shift($csv);

        $csv = array_map(function ($row) use ($headers) {
            return array_combine($headers, $row);
        }, $csv);

        foreach ($csv as $row) {
            $port = new Port();
            $port->name = $row['mx_contacts_contact_name'];
            $port->code = $row['mx_contacts_mx_code'];
            if (Port::whereCode($port->code)->first()) {
                continue;
            }
            $countryName = self::MAP_COUNTRIES[$row['mx_contacts_address_country']] ?? $row['mx_contacts_address_country'];
            $country = Country::whereNameEn($countryName)->first();
            if (! $country) {
                throw new Exception("Country not found {$row['mx_contacts_address_country']}");
            }
            $port->country()->associate($country);
            $port->save();
        }
    }

    public function down(): void
    {
        //
    }
}
