<?php

use App\Models\City;
use App\Models\VehicleTerminal;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Migrations\Migration;

class FillVehicleTerminalsCities extends Migration
{
    public function up(): void
    {
        $terminals = [
            2 => [
                0 => 'BS BENSENVILLE, IL',
                1 => 'Chicago, IL',
            ],
            3 => [
                0 => 'UIC HOUSTON, TX',
                1 => 'Houston, TX',
            ],
            4 => [
                0 => 'UIC INDIANAPOLIS, IN',
                1 => 'Indianapolis, IN',
            ],
            5 => [
                0 => 'UIC NEWARK, NJ',
                1 => 'Newark, NJ',
            ],
            6 => [
                0 => 'UIC SAVANNAH, GA',
                1 => 'Savannah, GA',
            ],
            7 => [
                0 => 'UIC LOS ANGELES, CA',
                1 => 'Los Angeles, CA',
            ],
            8 => [
                0 => 'AEC ALASKA, AK',
                1 => null,
            ],
            9 => [
                0 => 'AEC CHICAGO, IL',
                1 => 'Chicago, IL',
            ],
            10 => [
                0 => 'AEC HOUSTON, TX',
                1 => 'Houston, TX',
            ],
            11 => [
                0 => 'AEC LOS ANGELES, CA',
                1 => 'Los Angeles, CA',
            ],
            12 => [
                0 => 'AEC MIAMI, FL',
                1 => 'MIAMI, FL',
            ],
            13 => [
                0 => 'AEC NORFOLK, VA',
                1 => 'NORFOLK, VA',
            ],
            14 => [
                0 => 'AEC SAVANNAH, GA',
                1 => 'Savannah, GA',
            ],
            15 => [
                0 => 'AEC SEATTLE, WA',
                1 => 'Seattle, WA',
            ],
            16 => [
                0 => 'ALCO MONTREAL, CAN',
                1 => null,
            ],
            17 => [
                0 => 'AARON NEW JERSEY, NJ',
                1 => null,
            ],
            18 => [
                0 => 'UIC SEATTLE, WA',
                1 => 'Seattle, WA',
            ],
            19 => [
                0 => 'NVS LINDEN, NJ',
                1 => 'LINDEN, NJ',
            ],
            20 => [
                0 => 'AEC HONOLULU, HI',
                1 => 'Honolulu, HI',
            ],
            21 => [
                0 => 'TRT PUYALLUP, WA',
                1 => 'PUYALLUP, WA',
            ],
            22 => [
                0 => 'PRESTIGE PUYALLUP, WA',
                1 => 'PUYALLUP, WA',
            ],
            23 => [
                0 => 'AEC TORONTO, CAN',
                1 => null,
            ],
            24 => [
                0 => 'W8 SEATTLE, WA',
                1 => 'Seattle, WA',
            ],
            25 => [
                0 => 'MTI LOS ANGELES, CA',
                1 => 'Los Angeles, CA',
            ],
            26 => [
                0 => 'TRT NEWARK, NJ',
                1 => 'Newark, NJ',
            ],
            27 => [
                0 => 'HMOTORS NEWARK, NJ',
                1 => 'Newark, NJ',
            ],
            28 => [
                0 => 'W8 HOUSTON, TX',
                1 => 'Houston, TX',
            ],
            29 => [
                0 => 'TRT SAVANNAH, GA',
                1 => 'SAVANNAH, GA',
            ],
            30 => [
                0 => 'ALL CARGO SOLUTIONS SAVANNAH, GA',
                1 => 'SAVANNAH, GA',
            ],
            31 => [
                0 => 'W8 LINDEN, NJ',
                1 => 'LINDEN, NJ',
            ],
            32 => [
                0 => 'W8 Jersey City, NJ',
                1 => 'Jersey City, NJ',
            ],
            33 => [
                0 => 'AIS HOUSTON, TX',
                1 => 'Houston, TX',
            ],
            34 => [
                0 => 'HMOTORS SAVANNAH, GA',
                1 => 'Savannah, GA',
            ],
            35 => [
                0 => 'WCS SAN FRANCISCO, CA',
                1 => 'San Francisco, CA',
            ],
            36 => [
                0 => 'DNIPRO ROSELLE, NJ',
                1 => 'ROSELLE, NJ',
            ],
            37 => [
                0 => 'LINEAR SHIPPING HOUSTON, TX',
                1 => 'Houston, TX',
            ],
            38 => [
                0 => 'HMOTORS JACKSONVILLE, FL',
                1 => 'Jacksonville, FL',
            ],
            39 => [
                0 => 'WCS LINDEN, NJ',
                1 => 'Linden, NJ',
            ],
            40 => [
                0 => 'WCS FORT LAUDERDALE, FL',
                1 => 'FORT LAUDERDALE, FL',
            ],
        ];
        foreach ($terminals as $terminal) {
            $vehicleTerminal = VehicleTerminal::whereName($terminal[0])
                ->firstOrFail();
            if ($terminal[1] === null) {
                $vehicleTerminal->city_id = null;
                $vehicleTerminal->save();

                continue;
            }

            $cityExplode = explode(',', $terminal[1]);
            $city = City::whereName($cityExplode[0])
                ->whereHas('state', function (Builder $builder) use ($cityExplode): void {
                    $builder->where('code', trim($cityExplode[1]));
                })
                ->first();
            if ($city === null) {
                dd($cityExplode);
            }

            $vehicleTerminal->city()->associate($city);
            $vehicleTerminal->save();
        }
    }

    public function down(): void
    {
        //
    }
}
