<?php

use Illuminate\Database\Migrations\Migration;

class UpdateTitleAndContentColumnToSupportTranslationsInNoticesTable extends Migration
{
    public function up(): void
    {
        $notices = DB::table('notices')->get();

        foreach ($notices as $notice) {
            $titleTranslations = json_encode([
                'en' => $notice->title,
                'pl' => $notice->title,
            ]);
            $contentTranslations = json_encode([
                'en' => $notice->content,
                'pl' => $notice->content,
            ]);

            DB::table('notices')->where('id', $notice->id)->update([
                'title_json' => $titleTranslations,
                'content_json' => $contentTranslations,
            ]);
        }
    }

    public function down(): void
    {
        //
    }
}
