<?php

use App\Models\User;
use App\Project\User\Enums\Roles;
use Illuminate\Database\Migrations\Migration;

class ChangeWarehouseManagersToTerminals extends Migration
{
    public function up(): void
    {
        User::where('role', Roles::WAREHOUSE_MANAGER)
            ->has('managedVehicleTerminals')
            ->each(function (App\Models\User $user): void {
                $user->role = Roles::TERMINAL_MANAGER;
                $user->save();
            });
    }

    public function down(): void
    {
        //
    }
}
