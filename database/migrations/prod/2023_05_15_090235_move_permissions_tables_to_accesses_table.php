<?php

use App\Models\Access;
use App\Models\VehicleDestinationAgency;
use App\Models\VehicleTerminal;
use App\Models\Warehouse;
use Illuminate\Database\Migrations\Migration;

class MovePermissionsTablesToAccessesTable extends Migration
{
    public function up(): void
    {
        DB::table('warehouse_permissions')
            ->get()
            ->each(function (\stdClass $permission) {
                $access = new Access();
                $access->user_id = $permission->user_id;
                $access->manager_access = $permission->manager_access;
                $access->accessable()
                    ->associate(Warehouse::find($permission->warehouse_id));
                $access->save();
            });

        DB::table('vehicle_terminal_permissions')
            ->get()
            ->each(function (\stdClass $permission) {
                $access = new Access();
                $access->user_id = $permission->user_id;
                $access->manager_access = $permission->manager_access;
                $access->accessable()
                    ->associate(VehicleTerminal::find($permission->vehicle_terminal_id));
                $access->save();
            });

        DB::table('vehicle_destination_agency_permissions')
            ->get()
            ->each(function (\stdClass $permission) {
                $access = new Access();
                $access->user_id = $permission->user_id;
                $access->manager_access = $permission->manager_access;
                $access->accessable()
                    ->associate(VehicleDestinationAgency::find($permission->vehicle_destination_agency_id));
                $access->save();
            });
    }
}
