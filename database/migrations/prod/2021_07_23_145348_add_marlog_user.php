<?php

use App\Models\Country;
use App\Models\User;
use App\Models\VehicleDestinationAgency;
use App\Project\User\Enums\Roles;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Hash;

class AddMarlogUser extends Migration
{
    public function up(): void
    {
        $user = new User();
        $user->email = '<EMAIL>';
        $user->password = Hash::make('Marlog*!#123');
        $user->active = 1;
        $user->role = Roles::CUSTOMS_AGENCY;
        $user->language_code = 'en';
        $user->country_id = Country::where('iso', 'US')->first()->id;
        $user->save();

        $destinationAgency = VehicleDestinationAgency::whereSlug('marlog-car-handling')->first();

        $user->managedDestinationAgencies()
            ->attach($destinationAgency->id, ['manager_access' => 1]);
    }

    public function down(): void
    {
        //
    }
}
