<?php

use App\Models\Option;
use App\Project\Option\Enums\UserOptions;
use Illuminate\Database\Migrations\Migration;

class ChangeUserAccountTypeOptionKeyName extends Migration
{
    public function up(): void
    {
        Option::where('key', 'vehicles_price_list_access')
            ->get()
            ->each(function (Option $option): void {
                $option->key = UserOptions::ACCOUNT_TYPE;
                $option->save();
            });
    }

    public function down(): void
    {
        //
    }
}
