<?php

use App\Models\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class UpdateTranslationsInNotificationsTable extends Migration
{
    public function up(): void
    {
        Notification::query()
            ->chunkById(1000, function (Collection $notifications) {
                $notifications->each(function (Notification $notification) {
                    $notification->translations = [
                        'pl' => $notification->content,
                    ];
                    $notification->save();
                });
            });

        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn('content');
        });
    }
}
