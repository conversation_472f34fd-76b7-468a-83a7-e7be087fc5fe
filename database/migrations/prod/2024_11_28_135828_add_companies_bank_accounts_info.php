<?php

use App\Models\Company;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        /** @var Company $oceanDrive */
        $oceanDrive = Company::whereSlug('ocean-drive-wholesale-llc')->firstOrFail();
        $oceanDrive->bank_account_number = '************';
        $oceanDrive->bank_swift_code = 'BOFAUS3N';
        $oceanDrive->bank_branch_number = '*********';
        $oceanDrive->bank_address = 'Bank of America, NA
222 Broadway
New York, New York 10038
';
        $oceanDrive->wise_account_name = '<EMAIL>';
        $oceanDrive->revoult_account_name = '<EMAIL>';
        $oceanDrive->zelle_account_name = null;
        $oceanDrive->save();

        /** @var Company $carWiseAutos */
        $carWiseAutos = Company::whereSlug('car-wise-autos')->firstOrFail();
        $carWiseAutos->name = 'Carwise Autos LLC';
        $carWiseAutos->bank_account_number = '************';
        $carWiseAutos->bank_swift_code = 'BOFAUS3N';
        $carWiseAutos->bank_branch_number = '*********';
        $carWiseAutos->bank_address = 'Bank of America, NA
222 Broadway
New York, New York 10038
';
        $carWiseAutos->wise_account_name = null;
        $carWiseAutos->revoult_account_name = null;
        $carWiseAutos->zelle_account_name = null;

        $carWiseAutos->address = '1815 Jim Walter Dr. ';
        $carWiseAutos->post_code = '71854';
        $carWiseAutos->city = 'Texarkana, AR';
        $carWiseAutos->save();
    }
};
