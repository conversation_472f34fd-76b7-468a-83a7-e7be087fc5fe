<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CostsTableAllowAmountToBeNegative extends Migration
{
    public function up(): void
    {
        Schema::table('costs', function (Blueprint $table): void {
            $table->integer('amount')->change();
        });
    }

    public function down(): void
    {
        Schema::table('be_negative', function (Blueprint $table): void {
            $table->integer('amount')->unsigned()->change();
        });
    }
}
