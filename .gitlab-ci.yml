stages:
  - build auction
  - tests
  - deploy

build auction:
  image: docker:stable
  stage: build auction
  script:
    - cd $CI_PROJECT_DIR
    - DOCKER_IMAGE=$CI_REGISTRY_IMAGE/auction
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $DOCKER_IMAGE
    - docker build -t ${DOCKER_IMAGE}:latest .
    - docker push ${DOCKER_IMAGE}:latest
  services:
    - docker:20.10-dind
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" #See https://gitlab.com/gitlab-org/gitlab/-/issues/29605#note_726636919
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
      changes:
          - Dockerfile
    - when: manual
      allow_failure: true

.deploy-job:
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/
  before_script:
    - rm /home/<USER>/.ssh/id_rsa
    - echo "$DEPLOY_KEY" | tr -d '\r' > /home/<USER>/.ssh/id_rsa
    - chmod 400 /home/<USER>/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H 's6.mydevil.net' >> ~/.ssh/known_hosts

tests:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: tests
  variables:
    MYSQL_DATABASE: "auction_test"
    MYSQL_USER: "dev"
    MYSQL_PASSWORD: "c@rrierWiSe123!"
    MYSQL_ROOT_PASSWORD: "c@rrierWiSe123!root"
  cache:
    - key:
        files:
          - composer.lock
      paths:
        - vendor/
  before_script:
    - mysql -h"mysql" --skip_ssl -uroot -p"$MYSQL_ROOT_PASSWORD" -e "GRANT ALL PRIVILEGES ON \`auction_test_%\`.* TO '$MYSQL_USER'@'%'; FLUSH PRIVILEGES;"
  script:
    - cd $CI_PROJECT_DIR
    - composer install --ignore-platform-reqs --optimize-autoloader --no-ansi --no-interaction --no-progress --profile
    - php artisan test --parallel
  services:
    - name: mysql:8.3
      command: [
          "--default-authentication-plugin=mysql_native_password",
          "--explicit_defaults_for_timestamp",
          "--max_allowed_packet=100M",
      ]

analyze:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: tests
  cache:
    - key:
        files:
          - composer.lock
      paths:
        - vendor/
  script:
    - cd $CI_PROJECT_DIR
    - composer install --ignore-platform-reqs --optimize-autoloader --no-ansi --no-interaction --no-progress --profile
    - ./vendor/bin/phpstan analyse --memory-limit=512M -c phpstan.neon

pint:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: tests
  cache:
    - key:
        files:
          - composer.lock
      paths:
        - vendor/
  script:
    - cd $CI_PROJECT_DIR
    - composer install --ignore-platform-reqs --optimize-autoloader --no-ansi --no-interaction --no-progress --profile
    - ./vendor/bin/pint --test

fe-lint:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: tests
  cache:
    - key:
        files:
          - package-lock.json
      paths:
        - node_modules/
  script:
    - cd $CI_PROJECT_DIR
    - npm ci --cache .npm --prefer-offline
    - npm run lint

deploy-staging:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: deploy
  needs:
    - tests
  extends: .deploy-job
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" #See https://gitlab.com/gitlab-org/gitlab/-/issues/29605#note_726636919
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: manual
  script:
    - cd $CI_PROJECT_DIR
    - npm ci --cache .npm --prefer-offline
    - dep deploy staging --branch=$CI_COMMIT_BRANCH

deploy-production:
  image: $CI_REGISTRY_IMAGE/auction:latest
  stage: deploy
  needs:
    - tests
  when: manual
  extends: .deploy-job
  script:
    - cd $CI_PROJECT_DIR
    - npm ci --cache .npm --prefer-offline
    - dep deploy production
  only:
    refs:
      - master
