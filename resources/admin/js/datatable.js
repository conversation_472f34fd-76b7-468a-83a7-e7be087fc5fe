import 'datatables.net-bs';
import 'datatables.net-responsive-bs';
import 'datatables.net-fixedheader-bs';
import 'datatables.net-select-bs';
import popovers from '../../js/popovers';

export default function (selector) {
  const dataTableTranslations = {
    "sProcessing": "Przetwarzanie...",
    "sLengthMenu": "Pokaż _MENU_ pozycji",
    "sZeroRecords": "Nie znaleziono pasujących pozycji",
    "sInfoThousands": " ",
    "sInfo": "Pozycje od _START_ do _END_ z _TOTAL_ łącznie",
    "sInfoEmpty": "Pozycji 0 z 0 dostępnych",
    "sInfoFiltered": "(filtrowanie spośród _MAX_ dostępnych pozycji)",
    "sInfoPostFix": "",
    "sSearch": "Szukaj:",
    "sUrl": "",
    "oPaginate": {
      "sFirst": "<PERSON><PERSON><PERSON>",
      "sPrevious": "Poprzednia",
      "sNext": "Następna",
      "sLast": "Ostatnia"
    },
    "sEmptyTable": "Brak danych",
    "sLoadingRecords": "Wczytywanie...",
    "oAria": {
      "sSortAscending": ": aktywuj, by posortować kolumnę rosnąco",
      "sSortDescending": ": aktywuj, by posortować kolumnę malejąco"
    }
  };
  window.dataTableTranslations = dataTableTranslations;
  const $dataTable = $(selector);
  if (!$dataTable.length) {
    return;
  }
  var $filters = $('#filters');
  const currentDatable = window.currentDatable = $dataTable.DataTable({
    language: document.documentElement.lang === 'pl' ? dataTableTranslations : null,
    lengthMenu: [ 50, 100, 200 ],
    pageLength: 100,
    processing: true,
    serverSide: true,
    ajax: {
      url: $dataTable.data('ajax-url'),
      type: 'POST',
      data: function (d) {
        if ($filters.length) {
          var values = $filters.find('form').serializeArray();
          values.forEach(function (item) {
            d[item.name] = item.value;
          });
        }
      },
    },
    select: $dataTable.data('selectable') ? {
      style:    'multi',
      selector: 'td:first-child'
    }: null,
    initComplete: function () {
      this.api().columns().every(function () {
        var column = this;
        if ($(column.header()).data('searchable') === false) {
          return;
        }
        var input = document.createElement("input");
        $(input).appendTo($(column.footer()).empty())
          .on('change', function () {
            column.search($(this).val(), false, false, true).draw();
          });
      });
    },
  });

  $dataTable.on('draw.dt', function () {
    popovers();
    $('[data-toggle="tooltip"]').tooltip();
  });

  if ($filters.length) {
    $filters.find('form').on('change', function (e) {
      currentDatable.draw();
    });
  }

  if ($('.select-all').length) {
    $('.select-all').click(function () {
      if ($(this).is(':checked')) {
        currentDatable.rows().select();
      } else {
        currentDatable.rows().deselect();
      }
    });
  }

  $(document).on('click', '.delete-item', function (e) {
    e.preventDefault();
    var $link = $(this);
    var text = $link.data('text') || 'Are you sure?';
    var r = confirm(text);
    if (!$link.next('.delete-icon').length && r) {
      $link.after('<i class="fa fa-cog fa-spin delete-icon"></i>');
      $.ajax({
        type: "DELETE",
        url: $link.attr('href'),
        dataType: "json",
        success: function (result) {
          if (result.status === 'ok') {
            if ($link.data('remove')) {
              $link.closest($link.data('remove')).remove();
            }
            else {
              $link.closest('tr').remove();
            }
            if (typeof currentDatable !== 'undefined') {
              currentDatable.ajax.reload(null, false);
            }
          }
          else {
            $link.next('.delete-icon').remove();
            alert(result.msg);
          }
        }
      });
    }
  });
}
