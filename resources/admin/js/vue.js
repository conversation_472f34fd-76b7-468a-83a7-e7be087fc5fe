import Vue from 'vue';
import ImageManager from '@/components/ImageManager/ImageManager.vue';
import Tasks from './components/Tasks/Tasks.vue';

export default function (selector) {
  import('vue-wysiwyg').then((module) => {
    // To work in build and dev mode. Don't ask... JS -_-
    const wysiwyg =  module.default.default || module.default;
    Vue.use(wysiwyg, {});
  });
  Vue.component('tasks', Tasks);
  Vue.component('imageManager', ImageManager);

  new Vue({
    el: selector,
  });
}
