<template>
  <div
    class="task"
    :class="{'task--edit': editMode, 'task--loading': loading, 'task--completed': completedAt !== null}"
  >
    <div class="task__handle">
      <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
      <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
    </div>
    <i
      @click.stop.prevent="$emit('mark-completed', !completedAt)"
      class="fa fa-check task__check" aria-hidden="true"
    ></i>
    <p
      v-if="!editMode"
      class="task__title"
    >
      {{title}}
    </p>
    <input
      maxlength="255"
      class="task__input"
      v-if="editMode"
      ref="input"
      :value="title"
      @input="$event => $emit('update:title', $event.target.value)"
      @change="$event => saveTask($event)"
    >
    <p
      class="task__date"
      :class="{'text-danger': dueDateIsInPast}"
    >
      {{dueDateString}}
    </p>
  </div>
</template>

<script>
import {DateTime} from 'luxon';

export default {
  props: {
    id: Number,
    title: String,
    editMode: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    completedAt: String,
    dueDate: String,
  },
  data() {
    return {};
  },
  watch: {
    editMode(val) {
      if (!val) {
        return;
      }
      this.$nextTick(() => {
        this.$refs.input.focus();
      });
    },
  },
  computed: {
    dueDateString() {
      if (!this.dueDate) {
        return;
      }
      return this.datetimeFromISO(this.dueDate).setLocale('pl').toFormat('dd LLL');
    },
    dueDateIsInPast() {
      if (!this.dueDate) {
        return false;
      }
      return this.datetimeFromISO(this.dueDate) < DateTime.local();
    }
  },
  methods: {
    saveTask() {
      this.$emit('save-task');
    },
    datetimeFromISO(string) {
      const datetime = DateTime.fromISO(string);

      return datetime.isValid ? datetime : null
    }
  }
}
</script>
