<template>
  <div class="task-panel">
    <button
      @click.stop.prevent="$emit('mark-completed', !completedAt)"
      class="btn btn-info task-panel__completed_btn"
    >
      {{ this.completedAt ? '<PERSON><PERSON>z jako nieukończone' : '<PERSON><PERSON><PERSON> jako ukończone' }}
    </button>
    <div class="task-panel__actions">
      <div>
        <a
          href=""
          class="task-panel__calendar_btn"
          @click.stop.prevent="showDateTimePicker"
        >
          <i class="fa fa-calendar" aria-hidden="true"></i>
          {{ editDueDate ? dueDateString : 'Termin' }}
          <i
            v-if="editDueDate"
            class="fa fa-times"
            aria-hidden="true"
            @click.stop.prevent="editDueDate = null"
          ></i>
        </a>
        <datetime
          type="datetime"
          v-model="editDueDate"
        />
      </div>
      <a
        href=""
        title="Usuń zadanie"
        class="text-danger task-panel__delete_btn"
        @click.stop.prevent="$emit('delete-task')"
      >
        <i class="fa fa-trash" aria-hidden="true"></i>
      </a>
    </div>
    <input
      maxlength="255"
      class="task-panel__title-input form-control"
      :value="title"
      @input="$event => $emit('update:title', $event.target.value)"
      @change="saveTask()"
    >
    <wysiwyg
      :options="{ image: { uploadURL: imageUploadUrl, dropzoneOptions: { headers: { 'X-CSRF-TOKEN': getCsrfToken() }} } }"
      v-model="editContent"
    />
    <button
      @click="saveTask"
      class="btn btn-success task-panel__save_btn"
    >
      Zapisz treść zadania
      <i v-if="loading" class="fa fa-spinner fa-spin fa-fw margin-bottom"></i>
    </button>
  </div>
</template>

<script>
import {Datetime} from 'vue-datetime';
import {DateTime} from 'luxon';

export default {
  components: {
    datetime: Datetime,
  },
  props: {
    id: Number,
    title: String,
    content: String,
    loading: {
      type: Boolean,
      default: false,
    },
    dueDate: String,
    completedAt: String,
  },
  data() {
    return {
      editContent: this.content,
      editDueDate: this.dueDate,
      preventDateSave: true,
    };
  },
  watch: {
    id() {
      this.editContent = this.content;
      this.editDueDate = this.dueDate;
      this.preventDateSave = true;
    },
    editContent(val) {
      this.$emit('update:content', val);
    },
    editDueDate(val) {
      this.$emit('update:due-date', DateTime.fromISO(val).toLocal().toISO());
      if (!this.preventDateSave) {
        this.saveTask();
      }
      this.preventDateSave = false;
    }
  },
  computed: {
    dueDateString() {
      if (!this.editDueDate) {
        return;
      }
      return this.datetimeFromISO(this.editDueDate).toLocaleString(DateTime.DATETIME_MED);
    },
    imageUploadUrl() {
      return window.tasksRoutes.uploadImage + '/' + this.id;
    }
  },
  methods: {
    saveTask() {
      this.$emit('save-task');
    },
    handleImageAdded(file, Editor, cursorLocation, resetUploader) {
      let formData = new FormData();
      formData.append('image', file);

      $.ajax({
        url: window.tasksRoutes.uploadImage + '/' + this.id,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
      }).done(response => {
        let url = response.url;
        Editor.insertEmbed(cursorLocation, 'image', url);
        resetUploader();
      }).error(response => {
        alert('Podczas wysyłania zdjęcia wystąpił błąd');
      });
    },
    showDateTimePicker() {
      // Trick because no native method
      this.$children[0].isOpen = true;
    },
    datetimeFromISO(string) {
      const datetime = DateTime.fromISO(string);

      return datetime.isValid ? datetime : null
    },
    getCsrfToken() {
      return document.head.querySelector("[name=csrf-token]").content;
    }
  }
}
</script>
