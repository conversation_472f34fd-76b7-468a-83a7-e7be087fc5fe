<template>
  <div class="tasks">
    <div class="row">
      <div
        :class="{'col-md-12': !selected, 'col-md-7': selected, 'small-padding-right': selected}"
      >
        <div class="tasks__panel">
          <h1 class="page-header">
            Zadania
          </h1>
          <div style="margin-bottom: 10px;">
            <button
              v-if="!showUncompleted"
              class="btn btn-success"
              @click="addTask"
            >
              <PERSON><PERSON><PERSON><PERSON>
            </button>
            <button
              class="btn btn-info"
              @click="toggleUncompleted"
            >
              {{showUncompleted ? 'Pokaż aktywne' : '<PERSON><PERSON>ż ukończone'}}
            </button>
          </div>
          <div v-if="!showUncompleted">
            <draggable
              v-model="tasks"
              :options="{handle: '.task__handle'}"
              @end="saveOrder"
              class="tasks__container"
            >
              <task
                v-for="task in tasks"
                :key="task.id"
                :id="task.id"
                :edit-mode="selected && selected.id === task.id"
                :title.sync="task.title"
                :loading="task.loading"
                :completed-at="task.completed_at"
                :due-date="task.due_date"
                @click.native="selectTask(task)"
                @save-task="saveTask(task)"
                @mark-completed="completed => markCompleted(task, completed)"
              />
            </draggable>
          </div>
          <div
            v-if="showUncompleted"
            class="tasks__container"
          >
            <task
              v-for="task in tasks"
              :key="task.id"
              :id="task.id"
              :edit-mode="selected && selected.id === task.id"
              :title.sync="task.title"
              :loading="task.loading"
              :completed-at="task.completed_at"
              :due-date="task.due_date"
              @click.native="selectTask(task)"
              @save-task="saveTask(task)"
              @mark-completed="completed => markCompleted(task, completed)"
            />
            <infinite-loading @infinite="infiniteHandler"></infinite-loading>
          </div>
        </div>
      </div>
      <div
        v-if="selected"
        class="col-md-5 small-padding-left"
      >
        <div class="tasks__panel">
          <task-panel
            :id="selected.id"
            :title="selected.title"
            :title.sync="selected.title"
            :content="selected.content"
            :content.sync="selected.content"
            :loading="selected.loading"
            :due-date="selected.due_date"
            :completed-at="selected.completed_at"
            @mark-completed="completed => markCompleted(selected, completed)"
            @update:due-date="selected.due_date = $event"
            @delete-task="deleteTask(selected)"
            @save-task="saveTask(selected)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Task from './Task.vue';
import TaskPanel from './TaskPanel.vue';
import draggable from 'vuedraggable';
import InfiniteLoading from 'vue-infinite-loading';

export default {
  components: {
    Task,
    draggable,
    TaskPanel,
    InfiniteLoading,
  },
  data() {
    return {
      tasks: [],
      page: 0,
      totalPages: 1,
      selected: null,
      showUncompleted: false,
      loadingTasks: false,
    };
  },
  created() {
    this.getTasks();
  },
  methods: {
    getTasks() {
      return new Promise((resolve, reject) => {
        if (this.loadingTasks) {
          resolve();
          return;
        }
        this.loadingTasks = true;
        $.ajax({
          url: window.tasksRoutes.index,
          data: {
            showUncompleted: this.showUncompleted,
            page: this.page,
          }
        }).done(response => {
          this.loadingTasks = false;
          this.tasks = response.current_page === 0 ? response.data : this.tasks.concat(response.data);
          this.totalPages = response.last_page;
          this.page = response.current_page;
          resolve();
        }).error(response => {
          this.loadingTasks = false;
          alert('Podczas pobierania zadań wystąpił błąd');
          reject();
        });
      });
    },
    addTask() {
      this.createTask();
    },
    createTask() {
      $.ajax({
        url: window.tasksRoutes.store,
        type: 'POST',
      }).done(response => {
        this.tasks.unshift(response);
      }).error(response => {
        alert('Podczas tworzenia zadania wystąpił błąd');
      });
    },
    selectTask(task) {
      this.selected = task;
    },
    saveTask(task) {
      this.$set(task, 'loading', true);
      $.ajax({
        url: window.tasksRoutes.update + '/' + task.id,
        type: 'PUT',
        dataType: 'json',
        data: {
          title: task.title,
          content: task.content,
          due_date: task.due_date,
        }
      }).done(response => {
        this.$set(task, 'loading', false);
        task.title = response.title;
        task.content = response.content;
      }).error(response => {
        this.$set(task, 'loading', false);
        if (response.status !== 422) {
          alert('Podczas zapisywania zadania wystąpił błąd');
        }
        let errorMsg = '';
        const errors = JSON.parse(response.responseText);
        for (let index in errors) {
          errorMsg += errors[index][0] + '\n';
        }
        alert(errorMsg);
      });
    },
    markCompleted(task, completed) {
      this.$set(task, 'loading', true);
      $.ajax({
        url: window.tasksRoutes.markCompleted + '/' + task.id,
        type: 'POST',
        data: {completed}
      }).done(response => {
        this.$set(task, 'loading', false);
        this.tasks = this.tasks.filter(obj => task.id !== obj.id);
        this.selected = null;
      }).error(response => {
        this.$set(task, 'loading', false);
        alert('Podczas zapisywania zadania wystąpił błąd');
      });
    },
    saveOrder() {
      const order = this.tasks.map((value, index) => {
        return {id: value.id, order: index + 1};
      });
      $.ajax({
        url: window.tasksRoutes.storeOrder,
        type: 'POST',
        data: {tasks: order}
      }).done(response => {

      }).error(response => {
        alert('Podczas zapisywania kolejności listy wystąpił błąd');
      });
    },
    deleteTask(task) {
      $.ajax({
        url: window.tasksRoutes.destroy + '/' + task.id,
        type: 'DELETE',
      }).done(response => {
        this.$set(task, 'loading', false);
        this.tasks = this.tasks.filter(obj => task.id !== obj.id);
        this.selected = null;
      }).error(response => {
        this.$set(task, 'loading', false);
        alert('Podczas usuwania zadania wystąpił błąd');
      });
    },
    toggleUncompleted() {
      this.showUncompleted = !this.showUncompleted;
      this.page = 0;
      this.tasks = [];
      this.selected = null;
      if (!this.showUncompleted) {
        this.getTasks();
      }
    },
    infiniteHandler($state) {
      if (this.page >= this.totalPages) {
        $state.complete();
        return;
      }
      this.page++;
      this.getTasks().then(() => {
        $state.loaded();
      }).catch(error => {
        $state.loaded();
      });
    },
  }
}
</script>
