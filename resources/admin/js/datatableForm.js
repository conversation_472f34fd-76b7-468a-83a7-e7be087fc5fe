import { trans } from 'matice';

export default function (selector) {
  const $form = $(selector);
  if (!$form.length) {
    return;
  }

  if (! $form.hasClass('requires-confirmation')) {
    $form.submit(function (e) {
      e.preventDefault();
      formSubmit($form);
    });
  }

  $form.on('submit-confirmed', function () {
    formSubmit($form);
  });

  const formSubmit = function ($form) {
    const formData = new FormData($form[0]);
    if ($form.hasClass('remove-empty')) {
      for (const [key, value] of Array.from(formData.entries())) {
        if (value === '') {
          formData.delete(key);
        }
      }
    }
    const ids = window.currentDatable.rows({selected: true}).ids().toArray();
    for (let i = 0; i < ids.length; i++) {
      formData.append('ids[]', ids[i]);
    }

    $.ajax({
      type: 'POST',
      url: $form.attr('action'),
      processData: false,
      contentType: false,
      data: formData,
    }).done(function (response) {
      window.currentDatable.rows().deselect();
      window.currentDatable.ajax.reload(null, false);
      $form.trigger('reset');
      swal(trans('admin.common.alerts.success'), response.status, "success");
    }).fail(function (response) {
      alert(trans('admin.common.alerts.ajax_error'));
    });
  }
}
