<template>
  <div
      @click="$emit('active-order', order)"
      :class="[
          'order',
           { 'order--active': isActive },
           { 'order--last': isLast },
         ]"
  >
    <p class="order__date">{{ order.date }}</p>
    <p>
      Numer:
      <strong>
        <a
            @click.stop
            :href="order.link"
            class="order__link"
        >
          {{ order.number }}
        </a>
      </strong>
    </p>
    <p>
      {{ type }}
      <strong class="order__description">
        {{ descriptionLimited }}
      </strong>
    </p>
  </div>
</template>

<script>
export default {
  props: {
    order: Object,
    isActive: Boolean,
    isLast: Boolean,
  },
  computed: {
    type() {
      let type = this.order.type === 'vehicles' ? 'Pojazd' : 'Paczka';
      if (this.order.description) {
        type += ':';
      }

      return type;
    },
    descriptionLimited() {
      if (!this.order.description) {
        return '';
      }

      return this.order.description.length > 40 ? `${this.order.description.substring(0, 40)}...` : this.order.description;
    }
  },
};
</script>

<style lang="scss" scoped>
.order {
  cursor: pointer;
  border-radius: 10px;
  background-color: #526fa9;
  padding: 20px 30px;
  color: #cddbf7;
  margin-right: 10px;
  min-height: 135px;
  flex: 0 0 250px;

  @media (min-width: 992px) {
    margin-right: 0;
    margin-bottom: 9px;
    width: auto;
    flex: 0;
  }

  &--active {
    background-color: #ffffff;
    color: #19191a;

    .order__date {
      color: #85878a;
    }

    .order__link {
      color: #337ab7;
    }
  }

  &--last {
    margin-bottom: 0;
  }

  &__link {
    color: #cddbf7;
  }

  &__date {
    color: #a6bae2;
    margin-bottom: 15px !important;
  }

  p {
    margin-bottom: 0;
  }
}
</style>
