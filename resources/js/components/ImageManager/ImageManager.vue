<template>
  <div
    :class="{ 'image-manager': true, 'image-manager--user-mode': userMode, 'loading': loading }"
    @dragend.prevent.stop
    @dragover.prevent.stop
    @dragleave.prevent.stop
    @drop.prevent.stop="event => uploadFiles(event.dataTransfer.files)"
  >
    <draggable
      ref="gallery"
      class="image-manager__gallery row"
      v-model="images"
      :disabled="userMode"
      @end="saveOrder"
    >
      <div
        v-for="(image, index) in images"
        :key="image.uuid"
        class="image-manager__gallery-col col-sm-3 col-xs-6"
      >
        <image-preview
          :image="image"
          :should-upload="shouldUpload(image)"
          :add-image-url="addImageUrl"
          :delete-image-url="deleteImageUrl"
          :user-mode="userMode"
          @uploading="image.uploading = true"
          @uploaded="response => uploaded(image, response)"
          @error-uploading="errorUploading(image)"
          @deleted="deleted(image)"
          @open-gallery="openGallery(index)"
        />
      </div>
    </draggable>
    <images-input
      :input-text="inputText"
      @input="uploadFiles"
    />
  </div>
</template>

<script>
import ImagesInput from './ImagesInput.vue';
import ImagePreview from './ImagePreview.vue';
import Image from './Image';
import lightGallery from 'lightgallery';
import lgZoom from 'lightgallery/plugins/zoom';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgRotate from 'lightgallery/plugins/rotate';
import draggable from 'vuedraggable';

export default {
  components: {
    ImagesInput,
    ImagePreview,
    draggable,
  },
  props: {
    addImageUrl: String,
    deleteImageUrl: String,
    indexImagesUrl: String,
    orderImagesUrl: String,
    inputText: String,
    userMode: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      images: [],
      lightGalleryInstance: null,
      loading: false,
    };
  },
  mounted() {
    this.fetchImages();
    this.initLightGallery();
  },
  watch: {
    images: {
      handler() {
        this.updateGallery();
      },
      deep: true,
    }
  },
  methods: {
    openGallery(index) {
      this.lightGalleryInstance.openGallery(index);
    },
    updateGallery() {
      this.lightGalleryInstance.refresh(
        this.images.map(image => ({
          src: image.previewUrl,
          thumb: image.previewUrl,
        })),
      )
    },
    /**
     * @param {FileList} fileList
     */
    uploadFiles(fileList) {
      if (!fileList.length) {
        return;
      }
      this.images = this.images.concat(
        [...fileList].map(file => new Image(
          false,
          file,
          null,
          null
        ))
      );
      if (this.userMode) {
        this.$nextTick(() => {
          this.scrollToLastImage();
        });
      }
    },
    scrollToLastImage() {
      this.$refs.gallery.$el.scrollLeft = this.$refs.gallery.$el.scrollWidth;
    },
    /**
     * @param {Image} image
     */
    shouldUpload(image) {
      return this.images.filter(image => !image.uploaded && !image.errorUploading)
        .slice(0, 3)
        .includes(image);
    },
    /**
     * @param {Image} image
     * @param {Object} response
     */
    uploaded(image, response) {
      image.url = response.url;
      image.fileName = response.file_name;
      image.uploading = false;
      image.uploaded = true;
    },
    /**
     * @param {Image} image
     */
    errorUploading(image) {
      image.uploading = false;
      image.errorUploading = true;
    },
    /**
     * @param {Image} deletedImage
     */
    deleted(deletedImage) {
      this.images = this.images.filter(image => image !== deletedImage);
    },
    saveOrder() {
      const images = this.images.filter(image => image.uploaded)
        .map(image => image.fileName);
      $.ajax({
        type: 'POST',
        url: this.orderImagesUrl,
        data: { images },
      }).fail(() => {
        alert('An error occurred during reordering. Refresh the page and try again.');
      });
    },
    fetchImages() {
      if (this.loading) {
        return;
      }

      this.loading = true;
      $.ajax({
        type: 'GET',
        url: this.indexImagesUrl,
        dataType: 'json',
      }).done(response => {
        this.images = response.map(imageData => new Image(
          true,
          null,
          imageData.url,
          imageData.file_name
        ))
      }).fail(() => {
        alert('Error during loading images');
      }).always(() => {
        this.loading = false;
      });
    },
    initLightGallery() {
      this.lightGalleryInstance = lightGallery(
        this.$refs.gallery.$el,
        {
          plugins: [lgZoom, lgThumbnail, lgRotate],
          dynamic: true,
          dynamicEl: [],
          flipHorizontal: false,
          flipVertical: false,
          licenseKey: '123',
        }
      );
    },
  },
}
</script>

<style lang="scss" scoped>
.image-manager {
  &--user-mode {
    margin-top: -13px;

    .image-manager__gallery {
      width: 100%;
      display: flex;
      overflow-x: auto;
      padding-top: 13px;
    }

    .image-manager__gallery-col {
      height: 180px;
      width: 320px;
      flex: 0 0 auto;
    }
  }

  &__gallery {
    margin-right: -4px;
    margin-left: -4px;
  }

  &__gallery-col {
    padding-left: 4px;
    padding-right: 4px;
    margin-bottom: 8px;
  }

  &.loading, :deep(.loading) {
    position: relative;

    &.loading--absolute::after {
      top: 0 !important;
      bottom: 0;
      margin-top: auto;
      margin-bottom: auto;
    }

    &::after {
      content: "\f110";
      display: inline-block;
      font: normal normal normal 14px/1 FontAwesome;
      animation: fa-spin 2s infinite linear;
      font-size: 40px;
      position: absolute;
      width: 40px;
      height: 40px;
      left: 0;
      right: 0;
      top: 25px;
      margin-left: auto;
      margin-right: auto;
      z-index: 11;
    }
  }
}
</style>
