<template>
  <div>
    <form
      method="post"
      action=""
      novalidate
      ref="form"
    >
      <div>
        <input
          type="file"
          name="files[]"
          :id="`file-${inputUuid}`"
          multiple
          accept="image/*, image/heif, image/heic"
          @change="input"
        />
        <label :for="`file-${inputUuid}`">
          <span>{{ inputText }}</span>
        </label>
      </div>
    </form>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';

export default {
  props: {
    inputText: String,
  },
  data() {
    return {
      inputUuid: uuidv4(),
    };
  },
  methods: {
    input(e) {
      this.$emit('input', e.target.files);
      this.resetForm();
    },
    resetForm() {
      this.$refs.form.reset();
    }
  }
}
</script>

<style lang="scss" scoped>
input[type=file] {
  display: none;
}
form {
  margin: 0;
  margin-block-end: 0;
}
label {
  padding: 10px 5px;
  text-align: center;
  margin-top: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 25px;
  width: 100%;
  min-height: 60px;
  outline: 2px dashed lighten(#295492, 20);
  outline-offset: 1px;
  border-radius: 10px;
  background-color: #295492;
  color: white;
}
</style>
