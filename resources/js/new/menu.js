import { Alpine } from '../../../public/vendor/livewire/livewire.esm';
import gsap from 'gsap';

document.addEventListener('alpine:init', () => {
  let t1 = gsap.timeline({ paused: true });
  t1.to('.menu', { opacity: 1, duration: 0.5, top: 0, ease: 'power1.out' });
  t1.to('.menu__item', {
    opacity: 1,
    y: 0,
    duration: 1,
    ease: 'power1.out',
    stagger: 0.3,
  });
  t1.to(
    '.menu__lang-item',
    {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power1.out',
      stagger: 0.3,
    },
    '>-0.5'
  );

  Alpine.store('menu', {
    isMenuOpen: false,
    openMenu() {
      t1.play().timeScale(1);
      this.isMenuOpen = true;
    },
    closeMenu() {
      t1.timeScale(2);
      t1.reverse();
      this.isMenuOpen = false;
    },
  });
});
