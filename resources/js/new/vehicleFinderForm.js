import { showAlert } from '@/new/alert.js';
import { trans } from 'matice';
import { Alpine } from '../../../public/vendor/livewire/livewire.esm.js';

export default (data) => {
  return {
    vehicleData: data.vehicleModels || {},
    vehicleTypes: [],
    makeList: [],
    modelList: {},
    years: [],

    selectedType: 'Automobile',
    selectedMake: null,
    selectedModel: null,

    makeSelect: null,
    modelSelect: null,

    init() {
      this.populateYears();
      this.vehicleTypes = this.createTypeList();

      Alpine.nextTick(() => {
        this.initSelect(
          this.$refs.type,
          this.vehicleTypes,
          trans('web.landing.type'),
          this.selectedType
        );
        this.initSelect(this.$refs.yearFrom, this.years, trans('web.landing.year_from'));
        this.initSelect(this.$refs.yearTo, this.years, trans('web.landing.year_to'));

        this.makeSelect = this.initSelect(this.$refs.make, [], trans('web.landing.make'));
        this.modelSelect = this.initSelect(this.$refs.model, [], trans('web.landing.model'));

        this.makeSelect.disable();
        this.modelSelect.disable();
        this.updateMakes(this.selectedType);

        this.$refs.type.addEventListener('change', (event) => {
          this.updateMakes(event.target.value);
        });

        this.$refs.make.addEventListener('change', (event) => {
          this.updateModels(event.target.value);
        });
      });
    },

    populateYears() {
      const currentYear = new Date().getFullYear();
      for (let year = currentYear; year >= 1950; year--) {
        this.years.push({ value: year.toString(), label: year.toString() });
      }
    },

    createTypeList() {
      return Object.keys(this.vehicleData).map((type) => ({
        value: type,
        label: trans(`web.landing.vehicle_types.${type.toLowerCase().split(' ').join('_')}`),
        icon: getVehicleIcon(type.toLowerCase().split(' ').join('_')),
      }));
    },

    updateMakes(typeValue) {
      this.selectedType = typeValue;

      if (!this.vehicleData[typeValue]) {
        this.makeList = [];
        this.initSelect(this.$refs.make, this.makeList, trans('web.landing.make'));
        this.makeSelect.disable();
        this.modelSelect.disable();
        return;
      }

      const makes = Object.keys(this.vehicleData[typeValue]);

      this.makeList = makes.map((make) => ({
        value: make,
        label: make,
      }));

      this.makeSelect = this.initSelect(this.$refs.make, this.makeList, trans('web.landing.make'));
      this.makeSelect.enable();
      this.modelSelect.disable();
    },

    updateModels(makeValue) {
      this.selectedMake = makeValue;

      if (!this.selectedType || !this.vehicleData[this.selectedType]?.[makeValue]) {
        this.modelList = [];
        this.initSelect(this.$refs.model, this.modelList, trans('web.landing.model'));
        this.modelSelect.disable();
        return;
      }

      const models = this.vehicleData[this.selectedType][makeValue];

      this.modelList = models.map((model) => ({
        value: model,
        label: model === 'All Models' ? trans('web.landing.all_models') : model,
      }));

      this.modelSelect = this.initSelect(
        this.$refs.model,
        this.modelList,
        trans('web.landing.model')
      );
      this.modelSelect.enable();
    },

    initSelect(selectRef, optionsList, placeholder, defaultValue = null) {
      if (selectRef.tomselect) selectRef.tomselect.destroy();

      const selectInstance = new TomSelect(selectRef, {
        maxOptions: 500,
        options: optionsList,
        valueField: 'value',
        labelField: 'label',
        searchField: 'label',
        placeholder,
        allowEmptyOption: true,
        create: false,
        plugins: ['dropdown_input'],
        render: {
          option: this.renderOption,
          item: this.renderOption,
        },
      });

      if (defaultValue) {
        selectInstance.setValue(defaultValue);
      }

      return selectInstance;
    },

    renderOption(data, escape) {
      const icon = data.icon
        ? `<img src="${escape(data.icon)}" class="ts-select-icon" alt="${escape(data.label)}"> `
        : '';
      return `<div>${icon}${escape(data.label)}</div>`;
    },

    submitForm() {
      if (window.isUserLoggedIn) {
        showAlert(trans('web.landing.search_not_available'));
        return;
      }
      window.location.href = window.transRoute('login');
    },
  }
};
