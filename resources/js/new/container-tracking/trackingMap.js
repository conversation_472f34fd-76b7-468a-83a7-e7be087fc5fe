import { Alpine } from '@vendor/livewire/livewire.esm';
import axios from 'axios';
import L from 'leaflet';
import { trans } from 'matice';

import markerShadow from 'leaflet/dist/images/marker-shadow.png';
import cargoIcon from '@images/container-tracking/cargo-icon.svg';
import customMarker from '@images/container-tracking/custom-marker.svg';
import metaIcon from '@images/map/icons/meta.svg';
import startIcon from '@images/map/icons/port-crane.svg';

delete L.Icon.Default.prototype._getIconUrl;

L.Icon.Default.mergeOptions({
  iconRetinaUrl: customMarker,
  iconUrl: customMarker,
  shadowUrl: markerShadow,
  iconSize: window.innerWidth < 768 ? [26, 26] : [32, 32],
  popupAnchor: [4, -38],
});

document.addEventListener('alpine:init', () => {
  Alpine.data('fetchMapData', (url) => ({
    isLoading: false,
    error: false,
    trackData: {},
    activeTab: 'route',
    panelVisible: true,
    async getData() {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      this.error = false;

      try {
        const response = await axios.get(url);

        this.trackData = response.data;
        this.isLoading = false;
        this.error = false;

        await this.renderMap();
      } catch (error) {
        this.error = true;
        this.isLoading = false;
        this.trackData = [];
      }
    },

    async renderMap() {
      const mapContainer = document.getElementById('route-map');
      if (!mapContainer) return;

      const map = L.map(mapContainer, {
        center: [0, 0],
        zoom: 2,
        zoomControl: false,
        attributionControl: false,
      });

      L.control.zoom({ position: 'bottomright' }).addTo(map);
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

      if (window.matchMedia('(max-width: 768px)').matches) {
        mapContainer.style.borderWidth = '1px';
      }

      const boundsArray = [];
      const allLatLngs = [];
      const currentPos = this.trackData.routeCoordinates;
      const current = currentPos ? L.latLng(currentPos.lat, currentPos.lng) : null;

      if (Array.isArray(this.trackData.routeSegments)) {
        this.trackData.routeSegments.forEach((segment) => {
          if (!segment?.path?.length) return;

          let pointsForCurrentPolyline = [];
          let prevLat = null;
          let prevLng = null;

          for (let i = 0; i < segment.path.length; i++) {
            const point = segment.path[i];

            if (!point || typeof point.lat !== 'number' || typeof point.lng !== 'number') {
              if (pointsForCurrentPolyline.length > 1) {
                allLatLngs.push([...pointsForCurrentPolyline]);
              }
              pointsForCurrentPolyline = [];
              prevLat = null;
              prevLng = null;
              continue;
            }

            let lat = point.lat;
            let lng = point.lng;

            if (prevLat === null || prevLng === null) {
              pointsForCurrentPolyline.push([lat, lng]);
              prevLat = lat;
              prevLng = lng;
            } else {
              let lngDiff = lng - prevLng;

              if (Math.abs(lngDiff) > 180) {
                let exitMeridianLng, entryMeridianLng, lngMInterpolation;
                let startLngForInterpolation, endLngForInterpolation;

                if (lngDiff < -180) {
                  exitMeridianLng = 180;
                  entryMeridianLng = -180;
                  lngMInterpolation = 180;
                  startLngForInterpolation = prevLng;
                  endLngForInterpolation = lng + 360;
                } else {
                  exitMeridianLng = -180;
                  entryMeridianLng = 180;
                  lngMInterpolation = -180;
                  startLngForInterpolation = prevLng;
                  endLngForInterpolation = lng - 360;
                }

                let latIntersect;
                if (startLngForInterpolation === endLngForInterpolation) {
                  latIntersect = prevLat;
                } else if (startLngForInterpolation === lngMInterpolation) {
                  latIntersect = prevLat;
                } else if (endLngForInterpolation === lngMInterpolation) {
                  latIntersect = lat;
                } else {
                  let t =
                    (lngMInterpolation - startLngForInterpolation) /
                    (endLngForInterpolation - startLngForInterpolation);
                  if (t >= 0 && t <= 1) {
                    latIntersect = prevLat + (lat - prevLat) * t;
                  } else {
                    latIntersect = (prevLat + lat) / 2;
                  }
                }
                latIntersect = Math.max(-89.999999, Math.min(89.999999, latIntersect));

                pointsForCurrentPolyline.push([latIntersect, exitMeridianLng]);
                if (pointsForCurrentPolyline.length > 1) {
                  allLatLngs.push([...pointsForCurrentPolyline]);
                }

                pointsForCurrentPolyline = [];
                pointsForCurrentPolyline.push([latIntersect, entryMeridianLng]);
                pointsForCurrentPolyline.push([lat, lng]);
              } else {
                pointsForCurrentPolyline.push([lat, lng]);
              }
              prevLat = lat;
              prevLng = lng;
            }
          }

          if (pointsForCurrentPolyline.length > 1) {
            allLatLngs.push(pointsForCurrentPolyline);
          }
        });
      }

      if (allLatLngs.length > 0) {
        allLatLngs.forEach((line) => {
          boundsArray.push(...line);
        });
      }

      let hasPassedCurrentPointGlobal = false;

      allLatLngs.forEach((line) => {
        {
          let before = [];
          let after = [];
          let foundInThisLine = false;

          if (current && !hasPassedCurrentPointGlobal) {
            for (let i = 0; i < line.length; i++) {
              const [latPath, lngPath] = line[i];
              if (latPath === current.lat && lngPath === current.lng) {
                before = line.slice(0, i + 1);
                after = line.slice(i);
                foundInThisLine = true;
                hasPassedCurrentPointGlobal = true;
                break;
              }
            }
          }

          if (foundInThisLine) {
            if (before.length > 1) {
              L.polyline(before, { color: 'blue', weight: 4 }).addTo(map);
            }
            if (after.length > 1) {
              L.polyline(after, {
                color: 'white',
                weight: 3,
                dashArray: '6, 6',
                opacity: 0.6,
              }).addTo(map);
            }
          } else {
            if (hasPassedCurrentPointGlobal) {
              L.polyline(line, {
                color: 'white',
                weight: 3,
                dashArray: '6, 6',
                opacity: 0.6,
              }).addTo(map);
            } else {
              L.polyline(line, { color: 'blue', weight: 5 }).addTo(map);
            }
          }
        }
      });

      if (Array.isArray(this.trackData.locations)) {
        this.trackData.locations.forEach((location, index) => {
          const coords = location.coordinates;
          if (!coords || typeof coords.lat !== 'number' || typeof coords.lng !== 'number') return;

          let marker = L.marker([coords.lat, coords.lng]);
          const iconSize = window.innerWidth < 768 ? [26, 26] : [32, 32];
          const popupAnchor = [0, -14];

          const isFirstLocation = index === 0;
          const isLastLocation = index === this.trackData.locations.length - 1;
          if (isLastLocation) {
            marker = L.marker([coords.lat, coords.lng], {
              icon: L.icon({
                iconUrl: metaIcon,
                iconSize,
                popupAnchor
              }),
            });
          } else if (isFirstLocation) {
            marker = L.marker([coords.lat, coords.lng], {
              icon: L.icon({
                iconUrl: startIcon,
                iconSize,
                popupAnchor
              }),
            });
          }

          marker.addTo(map);
          this.addPopup(marker, `<b>${location.name}</b>, ${location.countryCode}`);

          boundsArray.push([coords.lat, coords.lng]);
        });
      }

      if (current) {
        const currentPositionIcon = L.divIcon({
          className: 'current-position-icon',
          html: `<div><img src="${cargoIcon}" alt=""/></div>`,
          iconSize: [28, 28],
          popupAnchor: [0, -14],
        });

        const containerMarker = L.marker([current.lat, current.lng], {
          icon: currentPositionIcon,
        }).addTo(map);
        this.addPopup(
          containerMarker,
          `<b>${this.trackData.shipmentType} ${this.trackData.shipmentNumber}</b>`
        );

        boundsArray.push([current.lat, current.lng]);
      }

      if (boundsArray.length > 0) {
        const latLngBounds = L.latLngBounds(boundsArray);
        const basePadding = 20;

        const desktopBreakpointWidth = 768;
        const leftPanelWidth = window.innerWidth * 0.35 > 500 ? 500 : window.innerWidth * 0.35;

        if (window.innerWidth >= desktopBreakpointWidth) {
          map.fitBounds(latLngBounds, {
            paddingTopLeft: [leftPanelWidth + basePadding, basePadding],
            paddingBottomRight: [basePadding, basePadding],
          });
        } else {
          map.fitBounds(latLngBounds, { padding: [basePadding, basePadding] });
        }
      } else if (current) {
        map.setView([current.lat, current.lng], 5);
      } else {
        map.setView([0, 0], 2);
      }
    },

    addPopup(marker, text) {
      marker.bindPopup(text, { closeButton: false });
      if (window.matchMedia('(pointer: fine)').matches) {
        marker.on('mouseover', () => marker.openPopup());
        marker.on('mouseout', () => marker.closePopup());
      }
    },

    polCity() {
      const pol = (this.trackData.route || []).find((r) => r.routeType === 'pol');
      return `${pol.location.name || trans('track-container.no_data')}, ${pol.location.countryCode || trans('track-container.no_data')}`;
    },

    podCity() {
      const pod = (this.trackData.route || []).find((r) => r.routeType === 'pod');
      return `${pod.location.name || trans('track-container.no_data')}, ${pod.location.countryCode || trans('track-container.no_data')}`;
    },

    atdDate() {
      const pol = (this.trackData.route || []).find((r) => r.routeType === 'pol');
      const date = pol?.date;
      return date ? this.formatDateTime(date) : trans('track-container.no_data');
    },

    etaDate() {
      const pod = (this.trackData.route || []).find((r) => r.routeType === 'pod');
      const date = pod?.predictiveEta || pod?.date;
      return date ? this.formatDateTime(date) : trans('track-container.no_data');
    },

    etaAtdDifference() {
      const pod = (this.trackData.route || []).find((r) => r.routeType === 'pod');
      const pod_date = pod?.predictiveEta || pod?.date;

      if (!pod_date || new Date(pod_date) < new Date()) {
        return 0;
      }

      const today = new Date();
      const etaDate = new Date(pod_date);

      const differenceInTime = etaDate - today;
      const differenceInDays = Math.ceil(differenceInTime / (1000 * 3600 * 24));

      return Math.max(differenceInDays, 0);
    },

    lastUpdatedDate() {
      const lastUpdated = this.trackData.updatedAt;
      if (lastUpdated) {
        return this.formatDateTime(lastUpdated);
      }
      return trans('track-container.no_data');
    },

    formattedVessels() {
      if (!this.trackData.vessels || this.trackData.vessels.length === 0) {
        return trans('track-container.no_data');
      }

      return this.trackData.vessels.map((vessel) => vessel.name).join(', ');
    },

    containerTitle() {
      if (this.trackData.shipmentType || this.trackData.shipmentNumber) {
        const title = `${this.trackData.shipmentType} ${this.trackData.shipmentNumber}`;
        return title.trim();
      } else {
        return trans('track-container.no_data');
      }
    },

    formatDateTime(dateStr) {
      const date = new Date(dateStr);
      if (isNaN(date)) return '[???]';

      const htmlLang = document.documentElement.lang || 'en';

      const options = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      };
      return new Intl.DateTimeFormat(htmlLang, options).format(date);
    },

    progress() {
      const pol = (this.trackData.route || []).find((r) => r.routeType === 'pol');
      const pod = (this.trackData.route || []).find((r) => r.routeType === 'pod');

      const atdStr = pol?.date;
      const etaStr = pod?.predictiveEta || pod?.date;

      if (!atdStr || !etaStr) {
        return 0;
      }

      const atd = new Date(atdStr);
      const eta = new Date(etaStr);
      const now = new Date();

      const totalTime = eta - atd;
      const passedTime = now - atd;

      if (totalTime <= 0) {
        return 0;
      }

      const percent = (passedTime / totalTime) * 100;

      return Math.min(Math.max(percent, 0), 100).toFixed(0);
    },

    routeDataHasAllActual(routeData) {
      return routeData.every((item) => item.isActual);
    },

    routeDataHasAllActualAndIsLastActual(routeData) {
      if (!routeData.every((item) => item.isActual)) {
        return false;
      }

      let lastIndex = -1;

      this.trackData.events.forEach((event, index) => {
        if (this.routeDataHasAllActual(event.routeData)) {
          lastIndex = index;
        }
      });

      return this.trackData.events[lastIndex]?.routeData === routeData;
    },

    showTab(tab) {
      this.activeTab = tab;
    },

    getFormattedEventPlace(event) {
      let fullName = event.location?.name || '';
      if (event.location?.countryCode) {
        fullName += `, ${event.location.countryCode}`;
      }
      return fullName ? fullName.trim() : trans('track-container.no_data');
    },
  }));
});
