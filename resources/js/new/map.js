import { trans } from 'matice';

const countryFlags = import.meta.glob('../../images/country/*.svg', { eager: true });

const createMarker = ({
  location,
  top,
  left,
  countryFlag = null,
  connectsTo = [
    'web.landing.location.central_america',
    'web.landing.location.south_america',
    'web.landing.location.north_europe',
    'web.landing.location.south_europe',
    'web.landing.location.east_europe',
    'web.landing.location.middle_east',
    'web.landing.location.north_africa',
    'web.landing.location.west_africa',
    'web.landing.location.central_africa',
    'web.landing.location.south_africa',
    'web.landing.location.east_africa',
    'web.landing.location.south_asia',
    'web.landing.location.east_asia',
  ],
  details = [],
}) => ({
  location: trans(location),
  top,
  left,
  countryFlag,
  connectsTo: connectsTo.map(trans),
  details,
});

const markers = [
  createMarker({
    location: 'web.landing.location.montreal',
    top: 34,
    left: 26,
    countryFlag: 'country/ca.svg',
  }),
  createMarker({
    location: 'web.landing.location.new_york',
    top: 37,
    left: 26,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.garden_city',
    top: 40,
    left: 25,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.bayport',
    top: 44,
    left: 20,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.jacksonville',
    top: 43,
    left: 24,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.long_beach',
    top: 41,
    left: 13,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.oakland',
    top: 37,
    left: 12,
    countryFlag: 'country/us.svg',
  }),
  createMarker({
    location: 'web.landing.location.central_america',
    top: 54,
    left: 23,
    connectsTo: [],
    details: [
      { countryFlag: 'country/bz.svg', location: trans('web.landing.location.belize') },
      { countryFlag: 'country/cr.svg', location: trans('web.landing.location.costa_rica') },
      { countryFlag: 'country/sv.svg', location: trans('web.landing.location.el_salvador') },
      { countryFlag: 'country/gt.svg', location: trans('web.landing.location.guatemala') },
      { countryFlag: 'country/hn.svg', location: trans('web.landing.location.honduras') },
      { countryFlag: 'country/ni.svg', location: trans('web.landing.location.nicaragua') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.south_america',
    top: 70,
    left: 30,
    connectsTo: [],
    details: [
      { countryFlag: 'country/ar.svg', location: trans('web.landing.location.argentina') },
      { countryFlag: 'country/br.svg', location: trans('web.landing.location.brazil') },
      { countryFlag: 'country/cl.svg', location: trans('web.landing.location.chile') },
      { countryFlag: 'country/co.svg', location: trans('web.landing.location.colombia') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.north_europe',
    top: 30,
    left: 50,
    connectsTo: [],
    details: [
      { countryFlag: 'country/be.svg', location: trans('web.landing.location.belgium') },
      { countryFlag: 'country/de.svg', location: trans('web.landing.location.germany') },
      { countryFlag: 'country/ee.svg', location: trans('web.landing.location.estonia') },
      { countryFlag: 'country/gb.svg', location: trans('web.landing.location.united_kingdom') },
      { countryFlag: 'country/lt.svg', location: trans('web.landing.location.lithuania') },
      { countryFlag: 'country/lv.svg', location: trans('web.landing.location.latvia') },
      { countryFlag: 'country/nl.svg', location: trans('web.landing.location.netherlands') },
      { countryFlag: 'country/pl.svg', location: trans('web.landing.location.poland') },
      { countryFlag: 'country/se.svg', location: trans('web.landing.location.sweden') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.south_europe',
    top: 36,
    left: 50,
    connectsTo: [],
    details: [
      { countryFlag: 'country/al.svg', location: trans('web.landing.location.albania') },
      { countryFlag: 'country/cy.svg', location: trans('web.landing.location.cypress') },
      { countryFlag: 'country/es.svg', location: trans('web.landing.location.spain') },
      { countryFlag: 'country/fr.svg', location: trans('web.landing.location.france') },
      { countryFlag: 'country/tr.svg', location: trans('web.landing.location.turkey') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.east_europe',
    top: 33,
    left: 53,
    connectsTo: [],
    details: [
      { countryFlag: 'country/ge.svg', location: trans('web.landing.location.georgia') },
      {
        countryFlag: 'country/ua.svg',
        location: `${trans('web.landing.location.ukraine')} (${trans('web.landing.suspended')})`,
      },
    ],
  }),
  createMarker({
    location: 'web.landing.location.middle_east',
    top: 43,
    left: 60,
    connectsTo: [],
    details: [
      {
        countryFlag: 'country/ae.svg',
        location: trans('web.landing.location.united_arab_emirates'),
      },
      { countryFlag: 'country/bh.svg', location: trans('web.landing.location.bahrain') },
      { countryFlag: 'country/il.svg', location: trans('web.landing.location.israel') },
      { countryFlag: 'country/iq.svg', location: trans('web.landing.location.iraq') },
      { countryFlag: 'country/jo.svg', location: trans('web.landing.location.jordan') },
      { countryFlag: 'country/kw.svg', location: trans('web.landing.location.kuwait') },
      { countryFlag: 'country/lb.svg', location: trans('web.landing.location.lebanon') },
      { countryFlag: 'country/om.svg', location: trans('web.landing.location.oman') },
      { countryFlag: 'country/qa.svg', location: trans('web.landing.location.qatar') },
      {
        countryFlag: 'country/sa.svg',
        location: `${trans('web.landing.location.saudi_arabia')} (${trans('web.landing.no_ev')})`,
      },
      { countryFlag: 'country/tn.svg', location: trans('web.landing.location.tunisia') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.north_africa',
    top: 46,
    left: 51,
    connectsTo: [],
    details: [
      { countryFlag: 'country/eg.svg', location: trans('web.landing.location.egypt') },
      { countryFlag: 'country/ly.svg', location: trans('web.landing.location.libya') },
      { countryFlag: 'country/ma.svg', location: trans('web.landing.location.morocco') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.west_africa',
    top: 57,
    left: 44,
    connectsTo: [],
    details: [
      { countryFlag: 'country/bj.svg', location: trans('web.landing.location.benin') },
      { countryFlag: 'country/ci.svg', location: trans('web.landing.location.ivory_coast') },
      { countryFlag: 'country/gh.svg', location: trans('web.landing.location.ghana') },
      { countryFlag: 'country/gm.svg', location: trans('web.landing.location.gambia') },
      { countryFlag: 'country/gn.svg', location: trans('web.landing.location.guinea') },
      { countryFlag: 'country/lr.svg', location: trans('web.landing.location.liberia') },
      { countryFlag: 'country/ng.svg', location: trans('web.landing.location.nigeria') },
      { countryFlag: 'country/sl.svg', location: trans('web.landing.location.sierra_leone') },
      { countryFlag: 'country/sn.svg', location: trans('web.landing.location.senegal') },
      { countryFlag: 'country/tg.svg', location: trans('web.landing.location.togo') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.central_africa',
    top: 59,
    left: 52,
    connectsTo: [],
    details: [
      { countryFlag: 'country/cm.svg', location: trans('web.landing.location.cameroon') },
      { countryFlag: 'country/cg.svg', location: trans('web.landing.location.congo') },
      {
        countryFlag: 'country/cd.svg',
        location: trans('web.landing.location.democratic_republic_of_congo'),
      },
      { countryFlag: 'country/gq.svg', location: trans('web.landing.location.equatorial_guinea') },
      { countryFlag: 'country/ga.svg', location: trans('web.landing.location.gabon') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.south_africa',
    top: 75,
    left: 53,
    connectsTo: [],
    details: [{ countryFlag: 'country/ao.svg', location: trans('web.landing.location.angola') }],
  }),
  createMarker({
    location: 'web.landing.location.east_africa',
    top: 57,
    left: 60,
    connectsTo: [],
    details: [
      { countryFlag: 'country/dj.svg', location: trans('web.landing.location.djibouti') },
      { countryFlag: 'country/tz.svg', location: trans('web.landing.location.tanzania') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.east_asia',
    top: 35,
    left: 82,
    connectsTo: [],
    details: [
      { countryFlag: 'country/kr.svg', location: trans('web.landing.location.south_korea') },
    ],
  }),
  createMarker({
    location: 'web.landing.location.south_asia',
    top: 53,
    left: 76,
    connectsTo: [],
    details: [
      { countryFlag: 'country/kh.svg', location: trans('web.landing.location.cambodia') },
      { countryFlag: 'country/my.svg', location: trans('web.landing.location.malaysia') },
    ],
  }),
];

const getFlag = (path) => (path ? countryFlags[`../../images/${path}`]?.default : null);

export default () => {
  return {
    tooltip: '',
    showTooltip: false,
    openModal: false,
    x: 0,
    y: 0,
    activeMarker: null,
    markers: [],
    isMobile: false,
    init() {
      this.checkIfMobile();
      this.markers = markers.map((marker) => ({
        ...marker,
        countryFlag: getFlag(marker.countryFlag),
        details: marker.details.map((detail) => ({
          ...detail,
          countryFlag: getFlag(detail.countryFlag),
        })),
      }));
      this.generateLines();
    },
    checkIfMobile() {
      this.isMobile = window.matchMedia('(pointer: coarse), (max-width: 767px)').matches;
    },
    generateLines() {
      const svgNS = 'http://www.w3.org/2000/svg';
      const svg = document.createElementNS(svgNS, 'svg');
      svg.setAttribute(
        'style',
        'position: absolute; width: 100%; height: 100%; pointer-events: none;'
      );
      document.querySelector('.map__image').appendChild(svg);

      this.markers.forEach((marker) => {
        marker.connectsTo.forEach((targetLocation) => {
          const targetMarker = this.markers.find((m) => m.location === targetLocation);
          if (targetMarker) {
            const line = document.createElementNS(svgNS, 'line');
            line.setAttribute('x1', `${marker.left + 1}%`);
            line.setAttribute('y1', `${marker.top + 2}%`);
            line.setAttribute('x2', `${targetMarker.left + 1}%`);
            line.setAttribute('y2', `${targetMarker.top + 2}%`);
            line.setAttribute('stroke', '#A2ACF3');
            line.setAttribute('stroke-width', '2');
            line.classList.add(
              `line-${marker.location.replace(/\s/g, '-')}-to-${targetMarker.location.replace(/\s/g, '-')}`,
              'hidden-line'
            );
            svg.appendChild(line);
          }
        });
      });
    },
    removeConnectionLines() {
      document.querySelectorAll('.visible-line').forEach((line) => {
        line.classList.replace('visible-line', 'hidden-line');
      });
    },
    showTooltipEvent(marker, event) {
      const mapRect = document.querySelector('.map__image').getBoundingClientRect();
      const markerRect = event.target.getBoundingClientRect();

      this.tooltip = marker;
      this.showTooltip = true;
      this.activeMarker = marker.location;
      this.x = markerRect.left + markerRect.width / 2 - mapRect.left;
      this.y = markerRect.top - mapRect.top - 10;

      marker.connectsTo.forEach((targetLocation) => {
        const targetMarker = this.markers.find((m) => m.location === targetLocation);
        if (targetMarker) {
          document
            .querySelector(
              `.line-${marker.location.replace(/\s/g, '-')}-to-${targetMarker.location.replace(/\s/g, '-')}`
            )
            ?.classList.replace('hidden-line', 'visible-line');
        }
      });
    },
    hideTooltipEvent() {
      if (!this.isMobile) {
        this.showTooltip = false;
        this.activeMarker = null;
        this.removeConnectionLines();
      }
    },
    showMobileList() {
      this.openModal = true;
    },
    hideMobileList() {
      this.openModal = false;
    },
  }
};
