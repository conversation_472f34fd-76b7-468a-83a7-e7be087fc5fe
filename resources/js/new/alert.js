import Swal from 'sweetalert2';

/**
 * @param {String} text
 * @param {String} icon
 * @param {String} title
 */
export function showAlert(text, icon = 'warning', title = '') {
  return Swal.fire({
    title,
    text,
    icon,
    customClass: {
      confirmButton: 'btn btn-primary',
    },
    didOpen: () => {
      const confirmButton = document.querySelector('.swal2-confirm');
      if (confirmButton) {
        confirmButton.classList.remove('swal2-styled', 'swal2-default-outline');
      }
    },
  });
}
