import { Alpine } from '../../../public/vendor/livewire/livewire.esm';
import { showAlert } from './alert';

document.addEventListener('alpine:init', () => {
  Alpine.data('alerts', (success, danger, warning, errors) => ({
    success: success || null,
    danger: danger || null,
    warning: warning || null,
    errors: Array.isArray(errors) ? errors : [],

    init() {
      if (this.success) {
        showAlert(this.success, 'success');
      }
      if (this.danger) {
        showAlert(this.danger, 'error');
      }
      if (this.warning) {
        showAlert(this.warning, 'warning');
      }
      if (this.errors.length > 0) {
        showAlert(this.errors.join('\n'), 'error');
      }
    },
  }));
});
