<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8"> <!-- utf-8 works for most cases -->
    <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
    <title></title> <!-- The title tag shows in email notifications, like Android 4.4. -->

    <!-- CSS Reset -->
    <style type="text/css">

        /* What it does: Remove spaces around the email design added by some email clients. */
        /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
        html,
        body {
            Margin: 0 !important;
            padding: 0 !important;
            height: 100% !important;
            width: 100% !important;
        }

        /* What it does: Stops email clients resizing small text. */
        * {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }

        /* What it does: Forces Outlook.com to display emails full width. */
        .ExternalClass {
            width: 100%;
        }

        /* What is does: Centers email on Android 4.4 */
        div[style*="margin: 16px 0"] {
            margin:0 !important;
        }

        /* What it does: Stops Outlook from adding extra spacing to tables. */
        table,
        td {
            mso-table-lspace: 0pt !important;
            mso-table-rspace: 0pt !important;
        }

        /* What it does: Fixes webkit padding issue. Fix for Yahoo mail table alignment bug. Applies table-layout to the first 2 tables then removes for anything nested deeper. */
        table {
            border-spacing: 0 !important;
            border-collapse: collapse !important;
            table-layout: fixed !important;
            Margin: 0 auto !important;
        }
        table table table {
            table-layout: auto;
        }

        /* What it does: Uses a better rendering method when resizing images in IE. */
        img {
            -ms-interpolation-mode:bicubic;
        }

        /* What it does: Overrides styles added when Yahoo's auto-senses a link. */
        .yshortcuts a {
            border-bottom: none !important;
        }

        /* What it does: Another work-around for iOS meddling in triggered links. */
        a[x-apple-data-detectors] {
            color:inherit !important;
        }

    </style>

    <!-- Progressive Enhancements -->
    <style>

        /* What it does: Hover styles for buttons */
        .button-td,
        .button-a {
            transition: all 100ms ease-in;
        }
        .button-td:hover,
        .button-a:hover {
            background: #555555 !important;
            border-color: #555555 !important;
        }

        /* Media Queries */
        @media screen and (max-width: 480px) {

            /* What it does: Forces elements to resize to the full width of their container. Useful for resizing images beyond their max-width. */
            .fluid,
            .fluid-centered {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                Margin-left: auto !important;
                Margin-right: auto !important;
            }
            /* And center justify these ones. */
            .fluid-centered {
                Margin-left: auto !important;
                Margin-right: auto !important;
            }

            /* What it does: Forces table cells into full-width rows. */
            .stack-column,
            .stack-column-center {
                display: block !important;
                width: 100% !important;
                max-width: 100% !important;
                direction: ltr !important;
            }
            /* And center justify these ones. */
            .stack-column-center {
                text-align: center !important;
            }

            /* What it does: Generic utility class for centering. Useful for images, buttons, and nested tables. */
            .center-on-narrow {
                text-align: center !important;
                display: block !important;
                Margin-left: auto !important;
                Margin-right: auto !important;
                float: none !important;
            }
            table.center-on-narrow {
                display: inline-block !important;
            }

        }

    </style>

</head>
<body width="100%" bgcolor="#FFFFFF" style="Margin: 0;">
<table cellpadding="0" cellspacing="0" border="0" height="100%" width="100%" bgcolor="#FFFFFF" style="border-collapse:collapse;"><tr><td valign="top">
            <center style="width: 100%;">
                <div style="max-width: 680px;">
                    <!--[if (gte mso 9)|(IE)]>
                    <table cellspacing="0" cellpadding="0" border="0" width="680" align="center">
                        <tr>
                            <td>
                    <![endif]-->

                    <!-- Email Body : BEGIN -->
                    <table cellspacing="0" cellpadding="0" border="0" align="center" bgcolor="#ffffff" width="100%" style="max-width: 680px;">

                        <!-- Two Even Columns : BEGIN -->
                        <tr>
                            <td bgcolor="#FFFFFF" align="center" height="100%" valign="top" width="100%">
                                <!--[if mso]>
                                <table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
                                    <tr>
                                        <td align="center" valign="top" width="100%">
                                <![endif]-->
                                <table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" style="max-width:100%;">
                                    <tr>
                                        <td align="center" valign="top" style="font-size:0; padding: 10px 0;">
                                            <!--[if mso]>
                                            <table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
                                                <tr>
                                                    <td align="left" valign="top" width="330">
                                            <![endif]-->
                                            <div style="display:inline-block; max-width:100%; min-width:200px; vertical-align:top; width:100%;" class="stack-column">
                                                <table cellspacing="0" cellpadding="0" border="0" width="100%">
                                                    <tr>
                                                        <td width="95" style="padding: 10px 10px; width: 95px;">
                                                            <table cellspacing="0" cellpadding="0" border="0" style="font-size: 14px;text-align: left;">
                                                                <tr>
                                                                    <td width="95" style="width: 95px;">
                                                                        <a href="{{ config('driver.domain') }}" style="text-decoration: none;">
                                                                            <img src="{{ config('driver.logo') }}" width="85" alt="" style="border: 0;width: 100%;max-width: 85px;height: auto;" class="center-on-narrow">
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                        <td style="font-family: sans-serif; mso-height-rule: exactly; font-size: 27px;">
                                                            <a href="{{ config('driver.domain') }}" style="color: black; text-decoration: none;">
                                                                {{ parse_url(config('driver.domain'))['host'] }}
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <!--[if mso]>
                                            </td>
                                            <td align="left" valign="top" width="330">
                                            <![endif]-->
                                            <!--[if mso]>
                                            </td>
                                            </tr>
                                            </table>
                                            <![endif]-->
                                        </td>
                                    </tr>
                                </table>
                                <!--[if mso]>
                                </td>
                                </tr>
                                </table>
                                <![endif]-->
                            </td>
                        </tr>
                        <!-- Two Even Columns : END -->

                        <!-- 1 Column Text : BEGIN -->
                        <tr>
                            <td>
                                <table cellspacing="0" cellpadding="0" border="0" width="100%" bgcolor="#FFFFFF">
                                    <tr>
                                        <td style="text-align: center; padding: 60px 15px 20px;">
                                            <img src="{{ config('driver.email_icon') }}" width="220" style="border: 0;width: 100%;max-width: 220px;height: auto;" class="center-on-narrow">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td bgcolor="#FFFFFF" style="padding: 10px 40px 40px; text-align: center; font-family: sans-serif; font-size: 15px; mso-height-rule: exactly; line-height: 20px; color: black;">
                                            <p style="font-size: 28px; font-weight: bold; color: black; line-height: 1.2">
                                                {{ $title ?? __('email.new_message') }}
                                            </p>

                                            @yield('content')
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <!-- 1 Column Text : BEGIN -->

                    </table>
                    <!-- Email Body : END -->

                    <!-- Email Footer : BEGIN -->
                    <table cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px;">
                        <tr>
                            <td style="padding: 10px;width: 100%;font-size: 12px; font-family: sans-serif; mso-height-rule: exactly; line-height:18px; text-align: center; color: #888888;">
                                <br><br>
                                <a href="{{ config('driver.domain') }}" style="color:#888888; text-decoration: none;">
                                    {{ parse_url(config('driver.domain'))['host'] }}
                                </a>
                                <br><br>
                            </td>
                        </tr>
                    </table>
                    <!-- Email Footer : END -->

                    <!--[if (gte mso 9)|(IE)]>
                    </td>
                    </tr>
                    </table>
                    <![endif]-->
                </div>
            </center>
        </td></tr></table>
</body>
</html>
