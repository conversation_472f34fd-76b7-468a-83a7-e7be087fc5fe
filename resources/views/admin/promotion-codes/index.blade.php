@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        <PERSON><PERSON> promocyjne

        <a href="{{ route('admin.promotion-codes.create') }}" class="btn btn-success pull-right">
            <i class="fa fa-plus fa-fw"></i> <span>Dodaj kod promocyjny</span>
        </a>
    </h1>

    <div class="table-responsive">
        <table class="table table-bordered" id="dataTable" width="100%"
               data-ajax-url="{{ route('admin.promotion-codes.datatable') }}"
               data-order="{{'[[0, "desc"]]'}}"
        >
            <thead>
            <tr>
                <th data-data="id" data-name="promotion_codes.id">ID</th>
                <th data-data="code" data-name="promotion_codes.code">Kod promocyjny</th>
                <th data-data="active" data-name="promotion_codes.active">Aktywny</th>
                <th data-data="valid_from" data-name="promotion_codes.valid_from">Ważny od</th>
                <th data-data="valid_to" data-name="promotion_codes.valid_to">Ważny do</th>
                <th data-data="action" data-name="action" data-orderable="false" data-searchable="false" class="col-md-2">Akcje</th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th>ID</th>
                <th>Kod promocyjny</th>
                <th>Aktywny</th>
                <th>Ważny od</th>
                <th>Ważny do</th>
                <th class="col-md-2">Akcje</th>
            </tr>
            </tfoot>
        </table>
    </div>
@endsection
