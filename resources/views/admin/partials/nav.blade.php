<div class="navbar-default sidebar" role="navigation">
    <div class="sidebar-nav navbar-collapse collapse" id="side-menu-collapse">
        <ul class="nav" id="side-menu">
            <li>
                <a href="{{ url('admin') }}">
                    <i data-toggle="tooltip" data-container="body" data-placement="top"
                       title="{{ __('admin/layout.nav.home') }}" class="fa fa-globe fa-fw"></i>
                    <span class="label-content">
                        {{ __('admin/layout.nav.home') }}
                    </span>
                </a>
            </li>
            @can('users.has-access')
                <li>
                    <a href="{{ route('admin.users.index') }}">
                        <i class="fa fa-users fa-fw" data-toggle="tooltip" data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.users') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.users') }}
                            @can('pa.can-see-counts')
                                <span class="label label-success" data-toggle="tooltip" data-placement="top"
                                      title="wszystkich">{{ $notifications['users'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @canany(['transactions.has-access', 'costs.has-access'])
                <li>
                    @can('transactions.has-access')
                        <a href="{{ route('admin.payments.orders') }}">
                    @elsecan('costs.has-access')
                        <a href="{{ route('admin.costs.index') }}">
                    @endcan
                        <i class="fa fa-dollar fa-fw" data-toggle="tooltip" data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.payments') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.payments') }}
                        </span>
                    </a>
                </li>
            @endcan
            @can('points.has-access')
                <li>
                    <a href="{{ route('admin.points.index') }}">
                        <i class="fa fa-star fa-fw" data-toggle="tooltip" data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.points') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.points') }}
                        </span>
                    </a>
                </li>
            @endcan
            @can('orders.has-access')
                <li>
                    <a href="{{ route('admin.orders.index') }}">
                        <i class="fa fa-shopping-cart fa-fw"
                           data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.orders') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.orders') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['orders'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('parcels.has-access')
                <li>
                    <a href="{{ route('admin.parcels.index') }}">
                        <i class="fa fa-archive fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.parcels') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.parcels') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['parcels'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('vehicles.has-access')
                <li>
                    <a href="{{ route('admin.vehicles.index') }}">
                        <i class="fa fa-car fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.vehicles') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.vehicles') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['vehicles'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('vehicle-calculators.has-access')
                <li>
                    <a href="{{ route('admin.vehicle-calculators.index') }}">
                        <i class="fa fa-calculator fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.vehicle_calculators') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.vehicle_calculators') }}
                        </span>
                    </a>
                </li>
            @endcan
            @can('messages.has-access')
                <li>
                    <a href="{{ route('admin.messages.index') }}">
                        <i class="fa fa-envelope fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.messages') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.messages') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['messages'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('promotion-codes.has-access')
                <li>
                    <a href="{{ route('admin.promotion-codes.index') }}">
                        <i class="fa fa-percent fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.promotion_codes') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.promotion_codes') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="aktywnych">{{ $notifications['promotion_codes'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('notices.has-access')
                <li>
                    <a href="{{ route('admin.notices.index') }}">
                        <i class="fa fa-exclamation-triangle fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.notices') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.notices') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="aktywnych">{{ $notifications['notices'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('drivers.has-access')
                <li>
                    <a href="{{ route('admin.drivers.index') }}">
                        <i class="fa fa-truck fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.drivers') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.drivers') }}
                            @can('pa.can-see-counts')
                                <span class="label label-success">{{ $notifications['drivers'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('vehicle-reports.has-access')
                <li>
                    <a href="{{ route('admin.vehicle-reports.index') }}">
                        <i class="fa fa-file-pdf-o fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.vehicle_reports') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.vehicle_reports') }}
                            @can('pa.can-see-counts')
                                <span class="label label-success">{{ $notifications['vehicle_reports'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('tasks.has-access')
                <li>
                    <a href="{{ route('admin.tasks.index') }}">
                        <i class="fa fa-tasks fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.tasks') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.tasks') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['tasks'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('companies.has-access')
                <li>
                    <a href="{{ route('admin.companies.index') }}">
                        <i class="fa fa-building fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.companies') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.companies') }}
                            @can('pa.can-see-counts')
                                <span class="label label-success" data-toggle="tooltip" data-placement="top">
                                    {{ $notifications['companies'] }}
                                </span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('shops.has-access')
                <li>
                    <a href="{{ route('admin.shops.index') }}">
                        <i class="fa fa-shopping-basket fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.shops') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.shops') }}
                            @can('pa.can-see-counts')
                                <span
                                    class="label label-success" data-toggle="tooltip" data-placement="top"
                                    title="wszystkich">{{ $notifications['shops'] }}</span>
                            @endcan
                        </span>
                    </a>
                </li>
            @endcan
            @can('settings.has-access')
                <li>
                    <a href="{{ route('admin.settings.edit') }}">
                        <i class="fa fa-cogs fa-fw" data-toggle="tooltip"
                           data-container="body" data-placement="top"
                           title="{{ __('admin/layout.nav.settings') }}"></i>
                        <span class="label-content">
                            {{ __('admin/layout.nav.settings') }}
                        </span>
                    </a>
                </li>
            @endcan
        </ul>
    </div>
</div>
