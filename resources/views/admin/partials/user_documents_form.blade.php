@php
    /** @var \App\Models\UserDocument[] $documents */
@endphp
@include('admin.partials.view-document', ['key' => 'id_card', 'encrypted' => true])
{!!
    Former::text('id_card_valid_until', __('admin/common.documents_form.id_card_valid_until'))
        ->addGroupClass('date')
        ->onGroupDataProvide('datepicker')
        ->append('<i class="fa fa-calendar fa-fw"></i>')
        ->forceValue($user->id_card_valid_until ? $user->id_card_valid_until->format('d.m.Y') : null)
!!}
<hr/>
@include('admin.partials.view-document', ['key' => 'terms'])
<hr/>
@include('admin.partials.view-document', ['key' => 'power_of_attorney'])
<hr/>
@include('admin.partials.view-document', ['key' => 'liability'])
<hr/>
@include('admin.partials.view-document', ['key' => 'declaration'])
<hr/>
{!! Former::textarea('comment', __('admin/common.documents_form.comment'))->addClass('ckeditor-custom') !!}
