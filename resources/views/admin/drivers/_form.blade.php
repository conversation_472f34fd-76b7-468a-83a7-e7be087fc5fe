@php
    /** @var \App\Models\Driver|null $driver */
    /** @var \App\Models\City|null $city */
@endphp
@if (isset($driver))
    {!! Former::populate($driver) !!}
    {!! Former::hidden('id', $driver->get<PERSON>ey()) !!}
@endif

{!! Former::text('company_name', __('admin/drivers.form.company_name'))->required() !!}
{!! Former::text('mc_number', __('admin/drivers.form.mc_number')) !!}
{!! Former::text('dot_number', __('admin/drivers.form.dot_number')) !!}
<div class="alert alert-info hidden" role="alert">
    {{ trans('admin/alerts.driver_exists') }} <a href="#" target="_blank" class="alert-link">{{ __('admin/alerts.driver_exists_link') }}</a>.
</div>
{!! Former::text('phone_number', __('admin/drivers.form.phone_number')) !!}
{!! Former::email('email', __('admin/drivers.form.email')) !!}

{!! Former::textarea('payments',  __('admin/drivers.form.payments'))->rows(3) !!}

<fieldset>
    <legend>{{ __('admin/drivers.form.header_w9_data') }}</legend>
    {!! Former::text('tax_company_name', __('admin/drivers.form.tax_company_name')) !!}
    {!! Former::text('secondary_company_name', __('admin/drivers.form.secondary_company_name')) !!}
    {!! Former::select('tax_number_type', __('admin/drivers.form.tax_number_type'))
            ->addOption(__('admin/common.select_option'), null)
            ->options($taxNumberTypes)
    !!}
    {!! Former::text('tax_number', __('admin/drivers.form.tax_number')) !!}
    {!! Former::text('address', __('admin/drivers.form.address')) !!}
    {!! Former::text('zip_code', __('admin/drivers.form.zip_code')) !!}
    {!! Former::select('city_id', __('admin/drivers.form.city_id'))->addClass('select2-ajax')
            ->dataAjaxUrl(route('admin.ajax.cities'))
            ->options($city !== null ? [$city->id => (new \App\Models\Presenters\CityPresenter($city))->displayFormat()] : [])
            ->select($city?->id)
    !!}
    <hr/>
</fieldset>

{!! Former::textarea('comment',  __('admin/drivers.form.comment'))->addClass('ckeditor-custom') !!}
@include('admin.partials.files_input', ['model' => $driver ?? null, 'fieldName' => 'w9', 'fieldDescription' => __('admin/drivers.form.w9'), 'isMultiple' => false])
<hr/>
@include('admin.partials.files_input', ['model' => $driver ?? null, 'fieldName' => 'insurance', 'fieldDescription' => __('admin/drivers.form.insurance'), 'isMultiple' => false])
<hr/>
@include('admin.partials.files_input', ['model' => $driver ?? null, 'fieldName' => 'irs', 'fieldDescription' => __('admin/drivers.form.irs'), 'isMultiple' => false])
<hr/>
@include('admin.partials.files_input', ['model' => $driver ?? null, 'fieldName' => 'dot_certificate', 'fieldDescription' => __('admin/drivers.form.dot_certificate'), 'isMultiple' => false])

<div class="text-center">
    {!! Former::large_primary_lg_submit(__('admin/common.save')) !!}
</div>

@section('scripts')
    <script type="module">
      $(document).ready(function () {
          $('#company_name, #dot_number, #mc_number').on('blur', function () {
              $.ajax({
                  type: 'GET',
                  url: '{{ route('admin.ajax.drivers.exists') }}',
                  data: {
                      driver_id: $('input[name="id"]').val(),
                      company_name: $('#company_name').val(),
                      dot_number: $('#dot_number').val(),
                      mc_number: $('#mc_number').val(),
                  },
                  dataType: 'json',
              }).done(function (data) {
                  if (data.url) {
                      $('.alert-info').removeClass('hidden');
                      $('.alert-info a').attr('href', data.url);

                      return;
                  }

                  $('.alert-info').addClass('hidden');
              });
            });
      });
    </script>
@append
