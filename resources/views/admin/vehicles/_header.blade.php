@php
    /** @var \App\Models\Vehicle $vehicle */
@endphp
<div class="clearfix page-header">
    <h3 class="pull-left" style="margin-top: 0;">
        @if(!empty($vehicle->vehicle_description))
            <span id="vehicle-desc-text">
                {{ $vehicle->description() }}
            </span>
            <button class="btn-link copy-btn" data-clipboard-target="#vehicle-desc-text" style="font-size: 0.6em">
                {{ __('admin/vehicles.edit.header.copy') }}
            </button>
        @else
            {{ __('admin/vehicles.edit.header.editing_vehicle', ['id' => $vehicle->order->id]) }}
        @endif
    </h3>
    <div class="pull-right">
        <a href="{{ route('admin.vehicles.index') }}" class="btn btn-success">
            <i class="fa fa-chevron-left fa-fw"></i>
            {{ __('admin/vehicles.edit.header.go_back') }}
        </a>
    </div>
</div>

<p>
    {{ __('admin/vehicles.edit.header.order_number', ['id' => $vehicle->order->id]) }}
    @can('users.can-view-personal-information')
        @if(!empty($vehicle->user->full_name))
            | {{ $vehicle->user->full_name }}
        @endif
        | <a href="{{ route('admin.users.edit', $vehicle->user) }}" target="_blank">{{ $vehicle->user->email }}</a>
        @if(!empty($vehicle->user->phone))
            | {{ $vehicle->user->phone }}
        @endif
    @endcan
    | {{ __('admin/vehicles.edit.header.deposit', ['amount' => $vehicle->user->formattedDepositBalance()]) }}
    | {{ __('admin/vehicles.edit.header.account_type', ['type' => (new \App\Models\Presenters\UserPresenter($vehicle->user))->accountTypeTranslatedForAdminPanel()]) }}
    @if(!empty($vehicle->auction_url))
        | <a href="{{ $vehicle->auction_url }}" target="_blank">
            @if($vehicle->auction !== null)
                @if($vehicle->auction->from === 'copart')
                    {{ __('admin/vehicles.edit.header.auction_link', ['auction' => 'Copart']) }}
                @elseif($vehicle->auction->from === 'iaa')
                    {{ __('admin/vehicles.edit.header.auction_link', ['auction' => 'IAA']) }}
                @else
                    {{ __('admin/vehicles.edit.header.vehicle_link') }}
                @endif
            @else
                {{ __('admin/vehicles.edit.header.vehicle_link') }}
            @endif
        </a>
    @endif
    |
    @if(!empty($vehicle->order->user_proof_of_payment))
        <a href="{{ \App\Project\Storage\CloudStorage::viewFilesRoute($vehicle->order, 'user_proof_of_payment') }}"
           target="_blank">
            {{ __('admin/vehicles.edit.header.user_proof_of_payment_link') }}
        </a>
    @else
        {{ __('admin/vehicles.edit.header.user_proof_of_payment_missing') }}
    @endif
    @if(!empty($vehicle->container_number))
        |
        <a href="" data-track-container="{{ $vehicle->container_number }}">
            {{ __('admin/vehicles.edit.header.track_container') }}
        </a>

        @include('partials._track-container-modal')
    @endif
    @if(!empty($vehicle->share_uuid))
        |
        <a href="{{ (new \App\Models\Presenters\VehiclePresenter($vehicle))->shareableUrl() }}" target="_blank">
            {{ __('admin/vehicles.edit.header.dealer_platform_link') }}
        </a>
    @endif
</p>
