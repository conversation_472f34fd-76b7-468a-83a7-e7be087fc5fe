<table class="table table-bordered" id="dataTable" width="100%"
       data-ajax-url="{{ $ajaxUrl }}"
       data-order="{{ '[[0, "desc"]]' }}"
>
    <thead>
    <tr>
        <th data-data="order_id" data-name="vehicles.order_id">{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th data-data="email" data-name="users.email">{{ __('admin/vehicles.index.table.columns.user') }}</th>
        <th data-data="vehicle_description" data-name="vehicles.vehicle_description">{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
        <th data-data="vin_number" data-name="vehicles.vin_number">{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th data-data="pickup_location" data-name="vehicles.pickup_location" data-orderable="false" data-searchable="false">{{ __('admin/vehicles.index.table.columns.seller_type') }}</th>
        <th data-data="buyer_number" data-name="vehicles.buyer_number">{{ __('admin/vehicles.index.table.columns.buyer_number') }}</th>
        <th data-data="lot_number" data-name="vehicles.lot_number">{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
        <th data-data="payment_date" data-name="vehicles.payment_date">{{ __('admin/vehicles.index.table.columns.payment_date') }}</th>
        <th data-data="receipt_date" data-name="vehicles.receipt_date">{{ __('admin/vehicles.index.table.columns.receipt_date') }}</th>
        <th data-data="comment" data-name="vehicles.comment">{{ __('admin/vehicles.index.table.columns.comment') }}</th>
        <th data-data="created_at" data-name="vehicles.created_at">{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
        <th data-data="action" data-name="action" class="col-md-2" data-orderable="false" data-searchable="false">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </thead>
    <tfoot>
    <tr>
        <th>{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.user') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.seller_type') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.buyer_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.payment_date') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.receipt_date') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.comment') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
        <th class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </tfoot>
</table>
