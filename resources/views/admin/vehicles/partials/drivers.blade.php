<div class="row">
    <div id="filters">
        {!! Former::open()->method('GET') !!}
            <div class="col-md-2">
                {!! Former::select('driver_vehicles_paid_at', __('admin/vehicles.index.filters.drivers_paid'))->options([false => __('admin/vehicles.index.filters.options.to_be_paid'), true => __('admin/vehicles.index.filters.options.paid'), null => __('admin/common.all')]) !!}
            </div>
            <div class="col-sm-2">
                {!! Former::select('driver_w9', __('admin/vehicles.index.filters.w9_status'))->options([null => __('admin/common.all'), true => __('admin/vehicles.index.filters.options.present'), false => __('admin/vehicles.index.filters.options.absent')]) !!}
            </div>
            <div class="col-sm-2">
                {!! Former::select('images_collection', __('admin/vehicles.index.filters.pickup_images'))->options([null => __('admin/common.all'), true => __('admin/vehicles.index.filters.options.present'), false => __('admin/vehicles.index.filters.options.absent')]) !!}
            </div>
        {!! Former::close() !!}
    </div>

    {!! Former::open(route('admin.vehicles.vehicle_drivers.update-many'))->method('PUT')->id('data-table-form') !!}
        <div class="col-md-3 col-md-offset-3">
            <div style="display: flex; justify-content: flex-end;">
                {!!
                       Former::text('driver_paid_at', 'Paid At')
                       ->addGroupClass('date')
                       ->onGroupDataProvide('datepicker')
                       ->onGroupStyle('margin-bottom: 15px;')
                       ->append('<i class="fa fa-calendar fa-fw"></i>')
                       ->value(now()->format('d.m.Y'))
               !!}
                <div style="margin-left: 15px;">
                    <p style="margin-bottom: 5px;"><strong>{{ __('admin/common.save') }}</strong></p>
                    {!! Former::primary_submit(__('admin/common.save')) !!}
                </div>
            </div>
        </div>
    {!! Former::close() !!}
</div>

<table class="table table-bordered" id="dataTable" width="100%"
       data-selectable="true"
       data-ajax-url="{{ $ajaxUrl }}"
       data-order="{{'[[1, "desc"]]'}}"
>
    <thead>
    <tr>
        <th
            data-data="null"
            data-default-content=""
            data-orderable="false"
            data-searchable="false"
            data-class-name="select-checkbox"
            class="select-checkbox"
        >
            <input class="select-all" type="checkbox">
        </th>
        <th data-data="order_id" data-name="vehicles.order_id">{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th data-data="terminal" data-name="vehicle_terminals.name">{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
        <th data-data="short_vin_number" data-name="vehicles.vin_number">{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th data-data="driver_company_name" data-name="drivers.company_name">{{ __('admin/vehicles.index.table.columns.driver_company_name') }}</th>
        <th data-data="driver_payments" data-name="drivers.payments">{{ __('admin/vehicles.index.table.columns.payments') }}</th>
        <th data-data="driver_email" data-name="drivers.email">{{ __('admin/vehicles.index.table.columns.driver_email') }}</th>
        <th data-data="driver_vehicles_transport_price" data-name="driver_vehicles.transport_price">{{ __('admin/vehicles.index.table.columns.transport_price') }}</th>
        <th data-data="has_collection_image" data-name="vehicles.images_collection">{{ __('admin/vehicles.index.table.columns.images_collection') }}</th>
        <th data-data="title_status" data-name="vehicles.title_status">{{ __('admin/vehicles.index.table.columns.title_status') }}</th>
        <th data-data="driver_vehicles_comment" data-name="driver_vehicles.comment">{{ __('admin/vehicles.index.table.columns.driver_comment') }}</th>
        <th data-data="driver_w9" data-name="drivers.w9" data-searchable="false">{{ __('admin/vehicles.index.table.columns.w9') }}</th>
        <th data-data="driver_vehicles_paid_at" data-name="driver_vehicles.paid_at">{{ __('admin/vehicles.index.table.columns.paid_at') }}</th>
        <th data-data="action" data-name="action" data-orderable="false" data-searchable="false" class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </thead>
    <tfoot>
    <tr>
        <th></th>
        <th>{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.driver_company_name') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.payments') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.driver_email') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.transport_price') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.images_collection') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.title_status') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.driver_comment') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.w9') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.paid_at') }}</th>
        <th class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </tfoot>
</table>
