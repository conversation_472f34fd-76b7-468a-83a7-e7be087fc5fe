<div class="row">
    <div id="filters">
        {!! Former::open()->method('GET') !!}
        <div class="col-sm-3">
            {!! Former::select('title_status', __('admin/vehicles.index.filters.title_status'))->options([null => __('admin/common.all'), true => __('admin/vehicles.index.filters.options.present'), false => __('admin/vehicles.index.filters.options.absent')]) !!}
        </div>
        <div class="col-sm-3">
            {!! Former::select('images', __('admin/vehicles.index.filters.images_from_terminal'))->options([null => __('admin/common.all'), true => __('admin/vehicles.index.filters.options.present'), false => __('admin/vehicles.index.filters.options.absent')]) !!}
        </div>
        {!! Former::close() !!}
    </div>
    <div class="col-sm-3">
        <p style="margin-bottom: 5px;"><strong>{{ __('admin/vehicles.index.forms.mark_title_status') }}</strong></p>
        <div class="btn-group" role="group">
            <button class="btn btn-success" data-url="{{ route('admin.vehicles.update-many') }}"
                    data-mark="title_status"
                    data-value="1"
            >
                {{ __('admin/vehicles.index.forms.options.present') }}
            </button>
            <button class="btn btn-danger" data-url="{{ route('admin.vehicles.update-many') }}"
                    data-mark="title_status"
                    data-value="0"
            >
                {{ __('admin/vehicles.index.forms.options.absent') }}
            </button>
        </div>
    </div>
    @if(!$forTerminalManager)
        <div class="col-sm-2">
            {{ __('admin/vehicles.index.filters.vehicles_length_sum') }}
            <span id="vehicles-length-sum">0</span>
            {{ __('admin/vehicles.index.filters.inch') }}
            <br/>
            <button id="vehicles-length-reset" style="margin-top: 5px;" class="btn btn-info block">
                {{ __('admin/common.reset') }}
            </button>
        </div>
        <div class="col-sm-1">
            <button id="vehicles-data-form-toggle" type="button" class="btn btn-primary pull-right">
                {{ __('admin/vehicles.index.filters.show_form') }}
            </button>
        </div>
    @endif
</div>
@if(!$forTerminalManager)
    {!! Former::open(route('admin.vehicles.update-many'))->method('PUT')->id('data-table-form')->class('remove-empty')->style('display: none;') !!}
    <hr/>
    <div class="row">
        <div class="col-sm-4">
            {!!
              Former::select('status', __('admin/vehicles.edit.shipment_form.status'))
              ->options([null => __('admin/vehicles.edit.shipment_form.select_placeholder')] + $statuses)
            !!}
            {!!
                Former::text('container_booking_number', __('admin/vehicles.edit.shipment_form.container_booking_number'))
                ->placeholder(__('admin/vehicles.edit.shipment_form.container_booking_number_placeholder'))
            !!}
        </div>
        <div class="col-sm-4">
            {!!
                Former::select('vehicle_shipping_line_id', __('admin/vehicles.edit.shipment_form.vehicle_shipping_line_id'))
                ->options([null => __('admin/vehicles.edit.shipment_form.select_placeholder')] + $shippingLines->pluck('name', 'id')->all())
            !!}
            {!!
                Former::text('eta', __('admin/vehicles.edit.shipment_form.eta'))
                ->addGroupClass('date')
                ->onGroupDataProvide('datepicker')
                ->onGroupStyle('margin-bottom: 15px;')
                ->append('<i class="fa fa-calendar fa-fw"></i>')
            !!}
        </div>
        <div class="col-sm-4">
            {!!
               Former::text('container_number', __('admin/vehicles.edit.shipment_form.container_number'))
               ->placeholder(__('admin/vehicles.edit.shipment_form.container_number_placeholder'))
            !!}
        </div>
        <div class="col-sm-4">
            {!!
               Former::select('vehicle_destination_agency_id', __('admin/vehicles.edit.shipment_form.vehicle_destination_agency_id'))
               ->options([null => __('admin/vehicles.edit.shipment_form.select_placeholder')] + $destinationAgencies->pluck('name', 'id')->all())
            !!}
        </div>
    </div>
    <button type="reset" class="btn btn-default" style="margin-right: 10px;">{{ __('admin/common.reset') }}</button>
    {!! Former::primary_submit(__('admin/common.save')) !!}
    <hr/>
    {!! Former::close() !!}
@endif

<table class="table table-bordered" id="dataTable" width="100%"
       data-selectable="true"
       data-ajax-url="{{ $ajaxUrl }}"
       data-order="{{'[[1, "desc"]]'}}"
>
    <thead>
    <tr>
        @if($forTerminalManager)
            <th data-data="order_id" data-name="vehicles.order_id">{{ __('admin/vehicles.index.table.columns.id') }}</th>
            <th data-data="terminal"
                data-name="vehicle_terminals.name">{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
            <th data-data="vehicle_description"
                data-name="vehicles.vehicle_description">{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
            <th data-data="vin_number"
                data-name="vehicles.vin_number">{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
            <th data-data="lot_number"
                data-name="vehicles.lot_number">{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
            <th data-data="container_booking_number"
                data-name="vehicles.container_booking_number">{{ __('admin/vehicles.index.table.columns.container_booking_number') }}</th>
            <th data-data="destination"
                data-name="vehicle_delivery_locations.name">{{ __('admin/vehicles.index.table.columns.delivery_location_name') }}</th>
            <th data-data="bill_of_sale" data-name="bill_of_sale" data-searchable="false">{{ __('admin/vehicles.index.table.columns.bill_of_sale') }}</th>
            <th data-data="title_file" data-name="title_file" data-searchable="false">{{ __('admin/vehicles.index.table.columns.title_file') }}</th>
        @else
            <th
                data-data="null"
                data-default-content=""
                data-orderable="false"
                data-searchable="false"
                data-class-name="select-checkbox"
                class="select-checkbox"
            >
                <input class="select-all" type="checkbox">
            </th>
            <th data-data="order_id" data-name="vehicles.order_id">{{ __('admin/vehicles.index.table.columns.id') }}</th>
            <th data-data="email" data-name="users.email">{{ __('admin/vehicles.index.table.columns.user') }}</th>
            <th data-data="vehicle_description"
                data-name="vehicles.vehicle_description">{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
            <th data-data="terminal"
                data-name="vehicle_terminals.name">{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
            <th data-data="vin_number"
                data-name="vehicles.vin_number">{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
            <th data-data="destination"
                data-name="vehicle_delivery_locations.name">{{ __('admin/vehicles.index.table.columns.delivery_location_name') }}</th>
            <th data-data="agency"
                data-name="vehicle_destination_agencies.name">{{ __('admin/vehicles.index.table.columns.destination_agency') }}</th>
            <th data-data="container_booking_number"
                data-name="vehicles.container_booking_number">{{ __('admin/vehicles.index.table.columns.container_booking_number') }}</th>
            <th data-data="vehicle_length"
                data-name="vehicles.vehicle_length">{{ __('admin/vehicles.index.table.columns.vehicle_length') }}</th>
            <th data-data="title_status"
                data-name="vehicles.title_status">{{ __('admin/vehicles.index.table.columns.title_status') }}</th>
            <th data-data="comment"
                data-name="vehicles.comment">{{ __('admin/vehicles.index.table.columns.comment') }}</th>
            <th data-data="created_at"
                data-name="vehicles.created_at">{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
            <th data-data="action" data-name="action" data-orderable="false" data-searchable="false"
                class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
        @endif
    </tr>
    </thead>
    <tfoot>
    <tr>
        @if($forTerminalManager)
            <th>{{ __('admin/vehicles.index.table.columns.id') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.container_booking_number') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.delivery_location_name') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.bill_of_sale') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.title_file') }}</th>
        @else
            <th></th>
            <th>{{ __('admin/vehicles.index.table.columns.id') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.user') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.terminal') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.delivery_location_name') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.destination_agency') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.container_booking_number') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.vehicle_length') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.title_status') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.comment') }}</th>
            <th>{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
            <th class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
        @endif
    </tr>
    </tfoot>
</table>

@section('scripts')
    <script type="module">
        $(document).ready(function () {
            function updateSum() {
                const sum = window.currentDatable.rows({selected: true}).data().pluck('vehicle_length').reduce((a, b) => a + b, 0);
                $('#vehicles-length-sum').text(sum);
            }

            $('#vehicles-length-reset').click(function () {
                window.currentDatable.rows().deselect();
            });

            window.currentDatable.on('select', function (e, dt, type) {
                if (type === 'row') {
                    updateSum();
                }
            });

            window.currentDatable.on('deselect', function (e, dt, type) {
                if (type === 'row') {
                    updateSum();
                }
            });

            $('#vehicles-data-form-toggle').click(function () {
                $('#data-table-form').slideToggle();
            });
        });
    </script>
@append
