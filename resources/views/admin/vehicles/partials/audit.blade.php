@php
    /** @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\Company> $companies */
@endphp
<div class="row">
    <div class="col-sm-2">
        <div id="filters">
            {!! Former::open()->method('GET') !!}
            {!!
                Former::select('orders[company_id]', __('admin/vehicles.index.filters.company'))
                    ->addOption(__('admin/common.all'), null)
                    ->options($companies->pluck('name', 'id'))
            !!}
            {!! Former::close() !!}
        </div>
    </div>
    <div class="col-sm-3 col-sm-offset-1">
        {!! Former::open(route('admin.vehicles.export'))->method('GET') !!}
        {!! Former::select('company_id', __('admin/vehicles.index.forms.company'))->required()->placeholder(__('admin/common.select_option'))->options($companies->pluck('name', 'id')) !!}
        <div class="row">
            <div class="col-sm-6">
                {!!
                   Former::text('from', __('admin/vehicles.index.forms.created_at_from'))
                       ->required()
                       ->addGroupClass('date')
                       ->onGroupDataProvide('datepicker')
                       ->append('<i class="fa fa-calendar fa-fw"></i>')
                       ->value(now()->startOfYear()->format('d.m.Y'))
                !!}
            </div>
            <div class="col-sm-6">
                {!!
                   Former::text('to', __('admin/vehicles.index.forms.created_at_to'))
                       ->required()
                       ->addGroupClass('date')
                       ->onGroupDataProvide('datepicker')
                       ->append('<i class="fa fa-calendar fa-fw"></i>')
                       ->value(now()->startOfDay()->format('d.m.Y'))
                !!}
            </div>
            <div class="col-sm-6">

            </div>
        </div>
        {!! Former::primary_submit(__('admin/common.export')) !!}
        {!! Former::close() !!}
    </div>
    {!! Former::openForFiles(route('admin.vehicles.update-many'))->method('PUT')->id('data-table-form') !!}
        <div class="col-sm-2 col-sm-offset-1">
            {!! Former::select('company_id', __('admin/vehicles.index.forms.company'))
                ->placeholder(__('admin/common.select_option'))
                ->options($companies->pluck('name', 'id'))
                ->addClass('form-control')
            !!}
        </div>
        <div class="col-sm-3">
            {!!
                Former::file('bill_of_sale[]', __('admin/vehicles.index.forms.bill_of_sale'))
                    ->help(__('admin/common.warnings.file_replacement'))
                    ->accept('application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint, text/plain, application/pdf, image/*')
                    ->multiple()
            !!}
            {!! Former::primary_submit(__('admin/common.save')) !!}
        </div>
    {!! Former::close() !!}
</div>

<table class="table table-bordered" id="dataTable" width="100%"
       data-selectable="true"
       data-ajax-url="{{ $ajaxUrl }}"
       data-order="{{ '[[1, "desc"]]' }}"
>
    <thead>
    <tr>
        <th
            data-data="null"
            data-default-content=""
            data-orderable="false"
            data-searchable="false"
            data-class-name="select-checkbox"
            class="select-checkbox"
        >
            <input class="select-all" type="checkbox">
        </th>
        <th data-data="order_id" data-name="vehicles.order_id">{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th data-data="vehicle_description"
            data-name="vehicles.vehicle_description">{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
        <th data-data="vin_number"
            data-name="vehicles.vin_number">{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th data-data="lot_number"
            data-name="vehicles.lot_number">{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
        <th data-data="buyer_number"
            data-name="vehicles.buyer_number">{{ __('admin/vehicles.index.table.columns.buyer_number') }}</th>
        <th data-data="pickup_location"
            data-name="vehicles.seller_type">{{ __('admin/vehicles.index.table.columns.seller_type') }}</th>
        <th data-data="title_file" data-name="title_file"
            data-searchable="false">{{ __('admin/vehicles.index.table.columns.title_file') }}</th>
        <th data-data="bill_of_landing" data-name="bill_of_landing"
            data-searchable="false">{{ __('admin/vehicles.index.table.columns.bill_of_landing') }}</th>
        <th data-data="bill_of_sale" data-name="bill_of_sale"
            data-searchable="false">{{ __('admin/vehicles.index.table.columns.bill_of_sale') }}</th>
        <th data-data="company_name" data-name="companies.name">
            {{ __('admin/vehicles.index.table.columns.company_name') }}
        </th>
        <th data-data="created_at"
            data-name="vehicles.created_at">{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
        <th data-data="action" data-name="action" data-orderable="false" data-searchable="false"
            class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </thead>
    <tfoot>
    <tr>
        <th></th>
        <th>{{ __('admin/vehicles.index.table.columns.id') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.vehicle') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.vin_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.lot_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.buyer_number') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.seller_type') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.title_file') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.bill_of_landing') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.bill_of_sale') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.company_name') }}</th>
        <th>{{ __('admin/vehicles.index.table.columns.created_at') }}</th>
        <th class="col-md-2">{{ __('admin/vehicles.index.table.columns.actions') }}</th>
    </tr>
    </tfoot>
</table>
