@php
    /** @const \App\ViewModels\Admin\Vehicles\VehiclesOrderViewModel $viewModel */
@endphp
@extends('admin.layouts.base')

@section('content')
    @include('admin.vehicles._header', ['vehicle' => $viewModel->vehicle->model()])

    @include('admin.vehicles._tabs', ['vehicle' => $viewModel->vehicle->model()])

    <div class="col-md-offset-3 col-md-6" style="margin-bottom: 30px">
        @if($viewModel->vehicle->deliveryLocation)
            {!! Former::open()->action(route('admin.vehicles.prices', [$viewModel->vehicle, '']))->id('vehicle-calculation') !!}
            {!! Former::hidden('terminal_city_id')->value($viewModel->vehicle->terminal ? $viewModel->vehicle->terminal->city_id : null) !!}
            <div class="row">
                <div class="col-sm-6">
                    {!! Former::select('city_id', __('admin/vehicles.edit.valuation_form.city_id'))
                            ->addClass('select2')
                            ->required()
                            ->placeholder(__('admin/vehicles.edit.valuation_form.city_id_placeholder'))
                            ->options($viewModel->pickUpCities->forSelect())
                            ->select($viewModel->preSelectedCity ? $viewModel->preSelectedCity->getKey() : null)
                    !!}
                </div>

                <div class="col-sm-6">
                    {!! Former::select('vehicle_type', __('admin/vehicles.edit.valuation_form.vehicle_type'))
                            ->required()
                            ->options($viewModel->vehicleTypes)
                            ->select($viewModel->preSelectedVehicleType)
                    !!}
                </div>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    {!! Former::select('port', __('admin/vehicles.edit.valuation_form.port'))
                            ->placeholder(__('admin/vehicles.edit.valuation_form.port_placeholder'))
                            ->required();
                    !!}
                </div>
            </div>

            <div class="row" style="margin-bottom: 15px;">
                <div class="col-sm-6">
                    <p><strong>{{ __('admin/vehicles.edit.valuation_form.price') }} </strong><span class="price"></span></p>
                    <p><strong>{{ __('admin/vehicles.edit.valuation_form.broker_fee') }} </strong>{{ $viewModel->brokerFeesAsPrice() }}</p>
                </div>
            </div>
            {!! Former::close() !!}
        @endif

        @include(
            'admin.orders._valuation-form',
             [
                 'order' => $viewModel->order->model(),
                 'statuses' => $viewModel->orderStatusForSelect(),
                 'promotionCodes' => $viewModel->promotionCodesForSelect(),
                 'companies' => $viewModel->companies,
             ]
         )
    </div>
@endsection

@section('scripts')
    <script type="module">
        let ports = [];
        $(function () {
            const $form = $('#vehicle-calculation');
            const $city = $form.find('select[name="city_id"]');
            const $vehicleType = $form.find('select[name="vehicle_type"]');
            const $port = $form.find('select[name="port"]');

            if ($form.length) {
                loadCalculationPrice();

                $city.on('change', loadCalculationPrice);
                $vehicleType.on('change', loadPrices);

                $port.on('change', loadPrice);
            }
        });

        function loadCalculationPrice() {
            const $form = $('#vehicle-calculation');
            const cityId = $form.find('select[name="city_id"]').val();
            let loading = false;
            if (cityId === null || loading) {
                return;
            }

            loading = true;
            $form.addClass('loading');

            $.ajax({
                url: `${$form.attr('action')}/${cityId}`,
            }).done(response => {
                ports = response;
                loadPrices();
            }).fail(() => {
                alert('Podczas ładowania wystąpił błąd.')
            }).always(() => {
                loading = false;
                $form.removeClass('loading');
            });
        }

        function loadPrices() {
            const $form = $('#vehicle-calculation');
            const $port = $form.find('select[name="port"]');
            $form.find('.price').text('');
            $port.find('option').not('[disabled]').remove();

            const vehicleType = $form.find('select[name="vehicle_type"]').val();
            if (!ports[vehicleType].length) {
                return;
            }
            const portsForType = ports[vehicleType].sort(
                (a, b) => parseFloat(a.price.replace('.', '').replace(',', '.')) - parseFloat(b.price.replace('.', '').replace(',', '.'))
            );
            $.each(portsForType, (i, port) => {
                $port.append($('<option>', {
                    value: port.city_id,
                    text: `${port.port} - $${port.price}`,
                    'data-price': port.price,
                }));
            });
            const preSelectedCity = $form.find('input[name="terminal_city_id"]').val();
            const $preSelectedPort = $port.find(`option[value="${preSelectedCity}"]`);
            if (!$preSelectedPort.length) {
                $port.find(`option[disabled]`).prop('selected', true);

                return;
            }
            $preSelectedPort.prop('selected', true);
            loadPrice();
        }

        function loadPrice() {
            const $form = $('#vehicle-calculation');
            const $port = $form.find('select[name="port"]');
            const $selected = $port.find('option:selected');
            $form.find('.price').text('');
            if ($selected.length && typeof $selected.data('price') !== 'undefined') {
                $form.find('.price').text(`$${$selected.data('price')}`);
            }
        }
    </script>
    @include('admin.orders._valuation-form-scripts', ['order' => $viewModel->order->model()])
@append
