@php
    /** @var \App\Models\Presenters\VehiclePresenter $vehicle */
@endphp
@extends('admin.layouts.base')

@section('content')
    <div class="clearfix page-header">
        <h2 class="pull-left" style="margin-top: 0;">
            {{ $vehicle->displayFormat() }}
        </h2>
        <div class="pull-right">
            <a href="{{ route('admin.vehicles.index') }}" class="btn btn-success">
                <i class="fa fa-chevron-left fa-fw"></i>
                {{ __('admin/vehicles.edit.header.go_back') }}
            </a>
        </div>
    </div>

    @include('partials._scroll-image-gallery', ['model' => $vehicle->model(), 'head' => __('admin/vehicles.edit.shipment_form.images_collection'), 'images' => $vehicle->collectionImagesUrls(), 'fieldName' => 'images_collection'])

    @include('partials._scroll-image-gallery', ['model' => $vehicle->model(), 'head' => __('admin/vehicles.edit.shipment_form.images_terminal'), 'images' => $vehicle->terminalImagesUrls(), 'fieldName' => 'images'])

    @include('partials._scroll-image-gallery', ['model' => $vehicle->model(), 'head' => __('admin/vehicles.edit.shipment_form.images_shipping'), 'images' => $vehicle->shippingImagesUrls(), 'fieldName' => 'images_shipping'])
@endsection
