@php
    /** @var \App\Models\Vehicle $vehicle */
    /** @var \Illuminate\Support\Collection|\App\Models\Parcel[] $parcels */
@endphp

@extends('admin.layouts.base')

@section('content')
    @include('admin.vehicles._header')

    @include('admin.vehicles._tabs')

    <div class="col-md-offset-3 col-md-6">
        <h3>
            {{ __('admin/vehicles.edit.parcels_form.currently_assigned') }}
            @forelse($vehicle->parcels as $parcel)
                <br/>
                <a href="{{ route('admin.parcels.edit', $parcel) }}" target="_blank">
                    {{ $parcel->tracking_number }}
                </a>
            @empty
                {{ __('admin/vehicles.edit.parcels_form.no_parcels_currently_assigned') }}
            @endforelse
        </h3>

        {!! Former::open()->action(route('admin.vehicles.parcels.store', $vehicle))->method('POST') !!}
        {!!
            Former::select('parcels[]', __('admin/vehicles.edit.parcels_form.assigned_parcels'))
            ->options($parcels->pluck('tracking_number', 'id'))
            ->select($vehicle->parcels->pluck('id'))
            ->addClass('select2')
            ->multiple()
            ->help(__('admin/vehicles.edit.parcels_form.change_assignment'))
        !!}
        <div class="float-save-btn">
            {!! Former::large_primary_lg_submit(__('admin/common.save')) !!}
        </div>
        {!! Former::close() !!}
    </div>
@endsection
