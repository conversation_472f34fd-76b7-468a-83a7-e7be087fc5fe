@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Użytkownik {{ $user->getUserCode() }}

        <div class="btn-group pull-right">
            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary"><i
                        class="fa fa-cogs fa-fw"></i> Ustawienia użytkownika</a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-danger">Wr<PERSON>ć do listy <i
                        class="fa fa-chevron-right fa-fw"></i></a>
        </div>
    </h1>

    <div class="row">
        <div class="col-md-12">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" @if($tab === null || $tab === 'parcels') class="active" @endif>
                    <a href="{{ route('admin.users.show', [$user, 'parcels']) }}" role="tab">
                        <PERSON><PERSON><PERSON> <span class="label label-success">{{$counts['parcels']}}</span>
                    </a>
                </li>
                <li role="presentation" @if($tab === 'orders') class="active" @endif>
                    <a href="{{ route('admin.users.show', [$user, 'orders']) }}" role="tab">
                        Zlecenia <span class="label label-success">{{$counts['orders']}}</span>
                    </a>
                </li>
                <li role="presentation" @if($tab === 'payments') class="active" @endif>
                    <a href="{{ route('admin.users.show', [$user, 'payments']) }}" role="tab">
                        Płatności <span class="label label-success">{{$counts['transactions']}}</span>
                    </a>
                </li>
            </ul>

            <div class="tab-content" style="margin-top: 15px;">
                <div role="tabpanel" class="tab-pane active">
                    @yield('tabpanel')
                </div>
            </div>
        </div>
    </div>
@endsection
