@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Użytkownicy
    </h1>

    @include('admin.users._tabs')

    <div class="table-responsive">
        <table class="table table-bordered" id="dataTable" width="100%"
               data-ajax-url="{{ route('admin.users.datatable') }}" data-order="{{'[[0, "desc"]]'}}">
            <thead>
            <tr>
                <th data-data="id" data-name="users.id">ID</th>
                <th data-data="email" data-name="users.email">E-mail</th>
                <th data-data="active" data-name="users.active">Aktywny</th>
                <th data-data="country" data-name="countries.name">Kraj</th>
                <th data-data="role" data-name="users.role">Rola</th>
                <th data-data="created_at" data-name="users.created_at">Data dodania</th>
                <th data-data="last_login" data-name="users.last_login">Ostatnie logowanie</th>
                <th data-data="action" data-name="action" data-orderable="false" data-searchable="false">
                    Akcje
                </th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th>ID</th>
                <th>E-mail</th>
                <th>Aktywny</th>
                <th>Kraj</th>
                <th>Rola</th>
                <th>Data dodania</th>
                <th>Ostatnie logowanie</th>
                <th>Akcje</th>
            </tr>
            </tfoot>
        </table>
    </div>
@endsection
