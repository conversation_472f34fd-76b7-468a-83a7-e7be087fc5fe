@php
    /** @var \Carbon\CarbonPeriod[] $periods */
@endphp
@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Prowizje użytkowników
    </h1>

    @include('admin.users._tabs')

    <div class="table-responsive">
        <table
            class="table table-bordered"
            id="dataTable" width="100%"
            data-ajax-url="{{ route('admin.users.commissions.datatable') }}"
            data-order="{{'[[2, "desc"]]'}}"
        >
            <thead>
            <tr>
                <th data-data="id" data-name="users.id">ID</th>
                <th data-data="email" data-name="users.email">E-mail</th>
                @foreach($periods as $periodKey => $period)
                    @php
                        $periodReversed = array_reverse($period->toArray());
                    @endphp
                    @foreach($periodReversed as $key => $date)
                        @if($key !== (count($period) - 1))
                            <th
                                data-data="date_{{ $periodKey }}_{{ $date->format('d_m_y') }}_count"
                                data-searchable="false"
                            >
                                @if($periodKey === 0)
                                    {{ $date->format('Y') }}
                                @else
                                    {{ $periodReversed[$key + 1]->translatedFormat('M-Y') }}
                                @endif
                            </th>
                        @endif
                    @endforeach
                @endforeach
                <th data-data="user_account_type" data-searchable="false">
                    Rodzaj konta
                </th>
                <th data-data="broker_fee_copart" data-searchable="false">
                    Copart
                </th>
                <th data-data="broker_fee_iaa" data-searchable="false">
                    IAA
                </th>
                <th data-data="broker_fee_other" data-searchable="false">
                    Inne
                </th>
                <th data-data="broker_fee_cw_pays" data-searchable="false">
                    CW opłaca
                </th>
                <th data-data="has_access_to_vehicle_reports" data-searchable="false">
                    Carfax
                </th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th>ID</th>
                <th>Email</th>
                @foreach($periods as $periodKey => $period)
                    @php
                        $periodReversed = array_reverse($period->toArray());
                    @endphp
                    @foreach($periodReversed as $key => $date)
                        @if($key !== (count($period) - 1))
                            <th>
                                @if($periodKey === 0)
                                    {{ $date->format('Y') }}
                                @else
                                    {{ $periodReversed[$key + 1]->translatedFormat('M-Y') }}
                                @endif
                            </th>
                        @endif
                    @endforeach
                @endforeach
                <th>Rodzaj konta</th>
                <th>Copart</th>
                <th>IAA</th>
                <th>Inne</th>
                <th>CW opłaca</th>
                <th>Carfax</th>
            </tr>
            </tfoot>
        </table>
    </div>
@endsection
