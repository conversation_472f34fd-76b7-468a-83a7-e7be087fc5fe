@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Katalog sklepów
        <a href="{{ route('admin.shops.create') }}" class="btn btn-success pull-right"><i
                    class="fa fa-plus fa-fw"></i> Dodaj nowy</a>
    </h1>

    <div id="filters" style="margin: 0 0 30px">
        {!! Former::open()->method('GET') !!}

        <div class="alert alert-warning" role="alert">
            <strong>Informacja:</strong> Kategorie modyfikowane są tylko i wyłącznie przez Administratora ze względu na
            potrzebę dobrania i dopasowania odpowiedniej ikony.
        </div>

        <div class="row">
            <div class="col-md-3">
                {!! Former::select('shop_category_id', 'Kategoria')->options($shopCategories) !!}
            </div>
        </div>

        {!! Former::close() !!}
    </div>
    <div class="table-responsive">
        <table class="table table-bordered" id="dataTable" width="100%" data-ajax-url="{{ route('admin.shops.datatable') }}" data-order="{{'[[0, "asc"]]'}}">
            <thead>
            <tr>
                <th data-data="id" data-name="shops.id">ID</th>
                <th data-data="name" data-name="shops.name">Nazwa</th>
                <th data-data="url" data-name="shops.url">URL</th>
                <th data-data="category_name" data-name="shop_categories.name">Kategoria</th>
                <th data-data="logo" data-name="shops.logo">Logo</th>
                <th data-data="created_at" data-name="shops.created_at">Data dodania</th>
                <th data-data="action" data-name="action" data-orderable="false" data-searchable="false" class="col-md-2">Akcje</th>
            </tr>
            </thead>
        </table>
    </div>
@endsection
