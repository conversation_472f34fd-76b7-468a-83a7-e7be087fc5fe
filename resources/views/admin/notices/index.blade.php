@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Komunikaty

        <a href="{{ route('admin.notices.create') }}" class="btn btn-success pull-right">
            <i class="fa fa-plus fa-fw"></i> <span>Do<PERSON>j komunikat</span>
        </a>
    </h1>

    <div class="table-responsive">
        <table class="table table-bordered" id="dataTable" width="100%"
               data-ajax-url="{{ route('admin.notices.datatable') }}"
               data-order="{{'[[0, "desc"]]'}}"
        >
            <thead>
            <tr>
                <th data-data="id" data-name="notices.id">ID</th>
                <th data-data="title" data-name="notices.title">Tytuł</th>
                <th data-data="content" data-name="notices.content">Treść</th>
                <th data-data="show_to_all_users" data-name="notices.show_to_all_users"><PERSON><PERSON><PERSON> w<PERSON>ystkim</th>
                <th data-data="send_notifications" data-name="notices.send_notifications">Wyślij powiadomienie</th>
                <th data-data="created_at" data-name="notices.created_at">Data utworzenia</th>
                <th data-data="action" data-name="action" data-orderable="false" data-searchable="false">Akcje</th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th>ID</th>
                <th>Tytuł</th>
                <th>Treść</th>
                <th>Pokaż wszystkim</th>
                <th>Wyślij powiadomienie</th>
                <th>Data utworzenia</th>
                <th>Akcje</th>
            </tr>
            </tfoot>
        </table>
    </div>
@endsection
