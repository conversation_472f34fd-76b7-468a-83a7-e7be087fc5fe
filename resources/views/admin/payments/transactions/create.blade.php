@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        <PERSON><PERSON><PERSON>
        <a href="{{ route('admin.payments.index') }}" class="btn btn-success pull-right"><i
                    class="fa fa-chevron-left fa-fw"></i> <PERSON><PERSON><PERSON><PERSON> do listy</a>
    </h1>

    <div class="col-md-offset-3 col-md-6">
        {!! Former::open()->action(route('admin.payments.store'))->addClass('repeatable-form fieldset')->method('POST') !!}

        {!!
            Former::select('company_id', __('admin/transactions.form.company_id'))
              ->addClass('select2')
              ->placeholder(__('admin/transactions.form.company_id_placeholder'))
              ->options($companies->pluck('name', 'id'))
              ->required()
        !!}

        {!!
            Former::text('paid_at', __('admin/transactions.form.paid_at'))
            ->addGroupClass('date')
            ->onGroupDataProvide('datepicker')
            ->append('<i class="fa fa-calendar fa-fw"></i>')
            ->forceValue(\Carbon\Carbon::now('America/Chicago')->format('d.m.Y'))
        !!}

        <hr/>

        <div style="display: none;" class="form-fields form-preset">

            {!!
                Former::select('transactions[][user_id]', __('admin/transactions.form.user_id'))
                    ->required()
                    ->disabled()
                    ->dataAjaxUrl(route('admin.ajax.users'))
                    ->addClass('select2-users')
                    ->addClass('select2-ajax')
            !!}
            {!!
                Former::text('transactions[][title]', __('admin/transactions.form.title'))
                    ->disabled()
                    ->blockHelp(__('admin/transactions.form.title_help'))
            !!}
            {!! Former::select('transactions[][template_id]', __('admin/transactions.form.template_id'))->disabled()->options($templates->pluck('title', 'id'))->placeholder(__('admin/transactions.form.template_id_placeholder')) !!}
            {!! Former::text('transactions[][amount]', __('admin/transactions.form.amount'))->disabled()->prepend('$')->required()->addClass('money-minus') !!}

            <button type="button" class="btn btn-danger remove-form-fields" style="margin-top: 10px;">
                {{ __('admin/common.delete') }}
            </button>
            <hr/>
        </div>

        <div class="fieldset-container">
            @foreach(old('transactions', [[]]) as $key => $transaction)
                <div class="form-fields">
                    {!!
                        Former::select("transactions[{$key}][user_id]", __('admin/transactions.form.user_id'))
                            ->required()
                            ->dataAjaxUrl(route('admin.ajax.users'))
                            ->addClass('select2-users')
                            ->addClass('select2-ajax')
                            ->options(isset($transaction['user_id']) ? [$transaction['user_id'] => \App\Models\User::findOrFail($transaction['user_id'])->selectRepresentation()] : [])
                    !!}
                    {!!
                        Former::text("transactions[{$key}][title]", __('admin/transactions.form.title'))
                            ->blockHelp(__('admin/transactions.form.title_help'))
                    !!}
                    {!! Former::select("transactions[{$key}][template_id]", __('admin/transactions.form.template_id'))->options($templates->pluck('title', 'id'))->placeholder(__('admin/transactions.form.template_id_placeholder')) !!}
                    {!! Former::text("transactions[{$key}][amount]", __('admin/transactions.form.amount'))->prepend('$')->required()->addClass('money-minus') !!}

                    <button type="button" class="btn btn-danger remove-form-fields" style="margin-top: 10px;">
                        {{ __('admin/common.delete') }}
                    </button>
                    <hr/>
                </div>
            @endforeach
        </div>
        <button type="button" class="btn btn-success add-preset" style="margin-bottom: 10px">
            {{ __('admin/transactions.form.add_transaction') }}
        </button>

        <div class="text-center" style="margin-top: 40px">
            {!! Former::large_primary_lg_submit(__('admin/common.save')) !!}
        </div>

        {!! Former::close() !!}
    </div>
@endsection
