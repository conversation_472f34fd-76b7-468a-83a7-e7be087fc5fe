@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Użytkownicy
    </h1>

    @include('admin.payments.partials._tabs')

    <div id="filters" style="margin: 0 0 30px">
        {!! Former::open()->method('GET') !!}
        <div class="row">
            <div class="col-md-2">
                {!!
                    Former::select('company_id', 'Firna')
                        ->addOption(__('admin/common.all'), null)
                        ->options($companies->pluck('name', 'id'))
                        ->onchange('this.form.submit()')
                        ->select($company?->getKey())
                !!}
            </div>
        </div>
        {!! Former::close() !!}
    </div>

    <h3>Suma stanów kont po opłaceniu:
        <strong>
            {{ '$ ' . price_to_string($sumPriced) }}
        </strong>
    </h3>

    @include('admin.payments.users._table')
@endsection
