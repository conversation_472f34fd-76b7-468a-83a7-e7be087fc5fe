@php
    /** @var \Illuminate\Database\Eloquent\Collection $companies */
    /** @var \App\Models\Company|null $company */
@endphp
<div class="table-responsive">
    <table class="table table-bordered" id="dataTable" width="100%"
           data-ajax-url="{{ route('admin.payments.datatable-users') }}"
           data-order="{{'[[4, "asc"]]'}}"
    >
        <thead>
        <tr>
            <th data-data="id" data-name="users.id">ID</th>
            <th data-data="email" data-name="users.email">Email</th>
            <th data-data="{{ $company ? "company_{$company->id}" : 'account_balance'}}" data-name="{{ $company ? "company_{$company->id}" : 'users.account_balance'}}" data-searchable="false">Stan konta</th>
            <th data-data="priced_sum" data-name="priced_sum" data-searchable="false"><PERSON><PERSON> wycen</th>
            <th data-data="priced_balance" data-name="priced_balance" data-searchable="false">Stan konta po opłaceniu</th>
            @foreach($company ? [$company] : $companies as $currentCompany)
                <th data-data="company_{{ $currentCompany->id }}" data-name="company_{{ $currentCompany->id }}" data-searchable="false">{{ $currentCompany->name }}</th>
            @endforeach
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>ID</th>
            <th>Email</th>
            <th>Stan konta</th>
            <th>Suma wycen</th>
            <th>Stan konta po opłaceniu</th>
            @foreach($company ? [$company] : $companies as $currentCompany)
                <th>{{ $currentCompany->name }}</th>
            @endforeach
        </tr>
        </tfoot>
    </table>
</div>
