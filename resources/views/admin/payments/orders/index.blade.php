@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    </h1>

    @include('admin.payments.partials._tabs')

    <div id="filters" style="margin: 0 0 30px">
        {!! Former::open()->method('GET') !!}
        <div class="row">
            <div class="col-md-2">
                {!! Former::select('order_type', 'Typ zlecenia')
                    ->options([null => 'Wszystkie'] + $orderTypes->pluck('name', 'id')->toArray())
                !!}
            </div>
            <div class="col-md-2">
                {!! Former::select('order_status', 'Status zlecenia')
                    ->options([null => 'Wszystkie'] + $orderStatuses->pluck('name', 'id')->toArray())
                !!}
            </div>
            <div class="col-md-2">
                {!! Former::select('company_id', 'Firma')->addOption(__('admin/common.all'), null)->options($companies->pluck('name', 'id')) !!}
            </div>
        </div>
        {!! Former::close() !!}
    </div>

    @include('admin.payments.orders._table')
@endsection

@section('scripts')
    <script type="module">
        $(document).ready(function () {
            var loadingPay = false;
            $(document).on('click', '.order-pay', function (e) {
                e.preventDefault();

                loadingPay = true;

                $.ajax({
                    type: 'GET',
                    url: $(this).attr('href'),
                    dataType: 'json',
                }).done(function (response) {
                    loadingPay = false;
                    currentDatable.ajax.reload();
                    swal("Sukces!", "Poprawnie opłacono zlecenie", "success");
                }).fail(function (response) {
                    loadingPay = false;
                    alert('Podczas ładowania wystąpił błąd.')
                });
            });
        });
    </script>
@append
