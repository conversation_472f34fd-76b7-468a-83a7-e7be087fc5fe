<!-- Nav tabs -->
<ul class="nav nav-tabs" role="tablist" style="margin-bottom: 25px;">
    @can('transactions.has-access')
        <li role="presentation" class="{{ Str::contains(Route::currentRouteAction(), '@orders') ? 'active' : '' }}">
            <a href="{{ route('admin.payments.orders') }}" role="tab">
              {{ __('admin/payments.tabs.orders') }}
            </a>
        </li>
        <li role="presentation" class="{{ Str::contains(Route::currentRouteAction(), 'PaymentsController@index') ? 'active' : '' }}">
            <a href="{{ route('admin.payments.index') }}" role="tab">
                {{ __('admin/payments.tabs.transactions') }}
            </a>
        </li>
        <li role="presentation" class="{{ Str::contains(Route::currentRouteAction(), '@users') ? 'active' : '' }}">
            <a href="{{ route('admin.payments.users') }}" role="tab">
              {{ __('admin/payments.tabs.users') }}
            </a>
        </li>
    @endcan
    @can('deposits.has-access')
        <li role="presentation" class="{{ Str::contains(Route::currentRouteAction(), 'DepositsController@index') ? 'active' : '' }}">
            <a href="{{ route('admin.deposits.index') }}" role="tab">
                {{ __('admin/payments.tabs.deposits') }}
            </a>
        </li>
    @endcan
    @can('costs.has-access')
        <li role="presentation" class="{{ Str::contains(Route::currentRouteAction(), 'CostsController@index') ? 'active' : '' }}">
            <a href="{{ route('admin.costs.index') }}" role="tab">
                {{ __('admin/payments.tabs.costs') }}
            </a>
        </li>
    @endcan
    @can('transactions.has-access')
        <li role="presentation" class="{{ \Illuminate\Support\Facades\Route::is('*payments.reports*') ? 'active' : '' }}">
            <a href="{{ route('admin.payments.reports') }}" role="tab">
                {{ __('admin/payments.tabs.reports') }}
            </a>
        </li>
    @endcan
</ul>
