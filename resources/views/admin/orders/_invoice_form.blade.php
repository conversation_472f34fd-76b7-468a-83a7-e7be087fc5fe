{!! Former::open()->action(route('admin.invoices.update', $order->id))->method('POST') !!}
{!! Former::populate($invoice) !!}
<?php $showCompany = Request::old('is_company') === null ? ($invoice && $invoice->is_company) : (Request::old('is_company') === '1') ?>
{!! Former::checkbox('is_company', '')->text(__('admin/common.invoice_form.is_company'))->value(1) !!}
{!! Former::text('company_name', __('admin/common.invoice_form.company_name'))->addGroupClass('company_data '. (!$showCompany ? 'soft-hidden' : '')) !!}
{!! Former::text('name', __('admin/common.invoice_form.name'))->addGroupClass('natural_person_data '. ($showCompany ? 'soft-hidden' : '')) !!}
{!! Former::text('city', __('admin/common.invoice_form.city')) !!}
{!! Former::text('zip_code', __('admin/common.invoice_form.zip_code')) !!}
{!! Former::text('address', __('admin/common.invoice_form.address')) !!}
{!! Former::text('phone', __('admin/common.invoice_form.phone')) !!}
{!! Former::text('nip', __('admin/common.invoice_form.nip'))->addGroupClass('company_data '. (!$showCompany ? 'soft-hidden' : '')) !!}
@if(in_array($order->type->slug, [\App\Models\Type::OTHER, \App\Models\Type::DEPOSIT]))
    {!! Former::text('item_name', __('admin/common.invoice_form.item_name'))->required() !!}
@endif
{!! Former::select('country_id', __('admin/common.invoice_form.country_id'))->addClass('select2')->options($countries)->placeholder(__('common.select_country_placeholder')) !!}

<div class="float-save-btn">
    {!! Former::large_primary_lg_submit(__('admin/common.save')) !!}
</div>
{!! Former::close() !!}

@section('scripts')
    @parent
    <script type="module">
      $(document).ready(function () {
        $('input[name="is_company"]').on('change', function () {
          if ($(this).is(':checked')) {
            $('.company_data').show();
            $('.natural_person_data').hide();
            return;
          }
          $('.company_data').hide();
          $('.natural_person_data').show();
        });
      });
    </script>
@append
