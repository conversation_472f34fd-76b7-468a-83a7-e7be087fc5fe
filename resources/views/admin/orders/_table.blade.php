@php
    $params = isset($user) ? ['user_id' => $user->id] : [];
    $ajaxUrl = route('admin.orders.datatable', $params);
@endphp
<div class="table-responsive">
    <table class="table table-bordered" data-ajax-url="{{$ajaxUrl}}" id="dataTable" width="100%" data-order="{{'[[0, "desc"]]'}}">
        <thead>
        <tr>
            <th data-data="id" data-name="orders.id">Numer zlecenia</th>
            <th data-data="warehouse_name" data-name="warehouses.name">Magazyn</th>
            <th data-data="type_name" data-name="types.name">Typ</th>
            <th data-data="service_name" data-name="services.name">Usługa</th>
            <th data-data="status_name" data-name="order_statuses.name">Status</th>
            <th data-data="price" data-name="orders.price">Cena</th>
            <th data-data="company_name" data-name="companies.name">Firma</th>
            <th data-data="order_number" data-name="orders.order_number">Numer zamówienia</th>
            <th data-data="tracking_number" data-name="orders.tracking_number">Numer śledzenia</th>
            <th data-data="created_at" data-name="orders.created_at">Data zamówienia</th>
            <th data-data="action" data-name="action" data-orderable="false" data-searchable="false" class="col-md-2">
                Akcje
            </th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Numer zlecenia</th>
            <th>Magazyn</th>
            <th>Typ</th>
            <th>Usługa</th>
            <th>Status</th>
            <th>Cena</th>
            <th>Firma</th>
            <th>Numer zamówienia</th>
            <th>Numer śledzenia</th>
            <th>Data zamówienia</th>
            <th class="col-md-3">Akcje</th>
        </tr>
        </tfoot>
    </table>
</div>
