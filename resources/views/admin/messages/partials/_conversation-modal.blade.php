<div class="modal fade conversation-modal" tabindex="-1" role="dialog" id="conversation-modal">
    <div class="modal-dialog" role="document" style="max-width: 900px;">
        <div class="modal-content">
            <div class="modal-header" style="display: flex; padding: 1rem;">
                <a id="title-link" href="#"><h4 class="modal-title" style="margin: 0; flex: 1;">{{ __('admin/messages.conversation_modal.default_title') }}</h4></a>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="margin: -1rem 0rem -1rem auto;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="height: 70vh; padding: 0;">
                <div class="conversation-content" style="height: 100%; overflow-y: auto; padding: 1rem;"></div>
            </div>
            <div class="modal-footer p-2 border-top">
                <form id="message-form" class="w-100 d-flex gap-2">
                    <input type="text"
                           class="form-control" 
                           id="message-input" 
                           placeholder="{{ __('admin/messages.conversation_modal.messages_placeholder') }}"
                           required>
                    <button type="submit" class="btn btn-primary" id="send-message-btn">
                        <span class="button-text">{{ __('admin/messages.conversation_modal.send_button') }}</span>
                        <span class="button-loader soft-hidden">
                            <i class="fa fa-spinner fa-spin"></i>
                            {{ __('admin/messages.conversation_modal.sending') }}
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section('scripts')
    <script type="module">
        function scrollToBottom() {
            const $content = $('.conversation-content');
            setTimeout(() => {
                $content.animate({
                    scrollTop: $content[0].scrollHeight
                }, 200);
            }, 50);
        }

        function focusMessageInput() {
            setTimeout(() => {
                $('#message-input').focus();
            }, 50);
        }

        $(document).on('click', 'a[data-conversation]', function (e) {
            e.preventDefault();
            const messageableId = $(this).data('conversation');
            const messageableType = $(this).data('type');
            const conversationTitle = $(this).data('title');
            const titleLink = $(this).data('url') || '#';
            const url = $(this).data('conversation-url');

            const $modal = $('#conversation-modal');
            const $content = $modal.find('.conversation-content');
            
            $modal.find('.modal-title').text(conversationTitle);
            $modal.find('#title-link').attr('href', titleLink);
            
            $modal.data('messageableId', messageableId);
            $modal.data('messageableType', messageableType);
            $modal.data('sendMessageUrl', $(this).data('send-message-url'));
            
            $content.empty();
            $content.html('<div class="text-center">{{ __('admin/messages.conversation_modal.loading') }}</div>');
            $modal.modal('show');
            $modal.on('hidden.bs.modal', function () {
                window.currentDatable.ajax.reload();
            });
            focusMessageInput();

            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    let content = '';

                    data.forEach(function (message) {
                        let messageClass = message.is_question ? 'is-question' : 'is-answer';
                        content += `
                            <div class="message ${messageClass}">
                                <div class="message-content">${message.content}</div>
                                <div class="message-time">${message.created_at}</div>
                            </div>
                        `;
                    });

                    $content.html(content);
                    scrollToBottom();
                    
                    $modal.on('shown.bs.modal', function () {
                        scrollToBottom();
                        focusMessageInput();
                    });
                },
                error: function () {
	                alert(trans('admin.common.alerts.ajax_error'));
                }
            });
        });

        function setLoadingState(loading = true) {
            const $btn = $('#send-message-btn');
            const $input = $('#message-input');
            const $btnText = $btn.find('.button-text');
            const $btnLoader = $btn.find('.button-loader');
            
            if (loading) {
                $btn.prop('disabled', true);
                $input.prop('disabled', true);
                $btnText.addClass('soft-hidden');
                $btnLoader.removeClass('soft-hidden');

                return;
            }

            $btn.prop('disabled', false);
            $input.prop('disabled', false);
            $btnText.removeClass('soft-hidden');
            $btnLoader.addClass('soft-hidden');
        }

        $('#message-form').on('submit', function(e) {
            e.preventDefault();
            
            const $modal = $('#conversation-modal');
            const $input = $('#message-input');
            const message = $input.val().trim();
            
            if (!message) return;

            const messageableId = $modal.data('messageableId');
            const messageableType = $modal.data('messageableType');
            const sendMessageUrl = $modal.data('sendMessageUrl');

            setLoadingState(true);

            $.ajax({
                url: sendMessageUrl,
                type: 'POST',
                data: {
                    messageable_id: messageableId,
                    messageable_type: messageableType,
                    content: message,
                },
                success: function() {
                    const $content = $modal.find('.conversation-content');
                    const newMessage = `
                        <div class="message is-answer">
                            <div class="message-content">${$('<div>').text(message).html()}</div>
                            <div class="message-time">` + trans('admin.messages.conversation_modal.just_now') + `</div>
                        </div>
                    `;
                    $content.append(newMessage);
                    $input.val('');
                    scrollToBottom();
                    focusMessageInput();
                    setLoadingState(false);
                },
                error: function() {
                    alert(trans('admin.common.alerts.ajax_error'));
                    setLoadingState(false);
                }
            });
        });
    </script>
@append
