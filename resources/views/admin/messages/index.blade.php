@extends('admin.layouts.base')

@section('content')
    <h1 class="page-header">
        Wiadomości
    </h1>

    <div class="row" id="filters">
        {!! Former::open()->method('GET') !!}
        <div class="col-sm-3">
            {!! Former::select('archived', 'Pokaż zarchiwizowane')->options([0 => 'Nie', 1 => 'Tak']) !!}
        </div>
        <div class="col-sm-3">
            <button id="mark-archived" class="btn btn-danger" data-url="{{ route('admin.messages.mark-archived') }}"
                    type="button"
                    data-archive="1" style="margin-top: 25px;">
                Oznacz jako zarchiwizowane
            </button>
        </div>
        <div class="col-sm-3 col-sm-offset-3">
            {!! Former::select('worker_id', 'Pracownicy')->addOption('Wszyscy', null)->options($workers->pluck('first_name', 'id')) !!}
        </div>
        {!! Former::close() !!}
    </div>

    <div class="table-responsive">
        <table class="table table-bordered" id="dataTable" width="100%"
               data-selectable="true"
               data-ajax-url="{{ route('admin.messages.datatable') }}"
               data-order="{{'[[6, "desc"]]'}}"
        >
            <thead>
            <tr>
                <th
                    data-data="null"
                    data-default-content=""
                    data-orderable="false"
                    data-searchable="false"
                    data-class-name="select-checkbox"
                    class="select-checkbox"
                >
                    <input class="select-all" type="checkbox">
                </th>
                <th data-data="messageable_id" data-name="messages.messageable_id">Powiązane ID</th>
                <th data-data="workers" data-name="workers" data-orderable="false" data-searchable="false">Pracownicy
                </th>
                <th data-data="content" data-name="messages.content">Wiadomość</th>
                <th data-data="email" data-name="email">Użytkownik</th>
                <th data-data="archived" data-name="messages.archived">Zarchiwizowano</th>
                <th data-data="max_created_at" data-name="max_created_at" data-searchable="false">Wysłane</th>
                <th data-data="action" data-name="action" data-orderable="false" data-searchable="false">Akcje</th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th></th>
                <th>Powiązane ID</th>
                <th>Pracownicy</th>
                <th>Wiadmość</th>
                <th>Użytkownik</th>
                <th>Zarchiwizowano</th>
                <th>Wysłane</th>
                <th>Akcje</th>
            </tr>
            </tfoot>
        </table>
    </div>
    @include('admin.messages.partials._conversation-modal')
@endsection

@section('scripts')
    <script type="module">
        $(document).ready(function () {
            var $markArchivedBtn = $('#mark-archived');
            $('select[name="archived"]').on('change', function (e) {
                e.preventDefault();
                if (!!parseInt($(this).val())) {
                    $markArchivedBtn.text('Przywróć zaznaczone');
                    $markArchivedBtn.data('archive', 0);
                } else {
                    $markArchivedBtn.text('Oznacz jako zarchiwizowane');
                    $markArchivedBtn.data('archive', 1);
                }
            });

            $markArchivedBtn.click(function () {
                var rowsData = currentDatable.rows({ selected: true }).data().toArray();
                var messageables = rowsData.map(function (row) {
                    return {
                        'type': row.messageable_type,
                        'id': row.messageable.id,
                    };
                });

                $.ajax({
                    type: 'POST',
                    url: $(this).data('url'),
                    data: {
                        messageables,
                        archive: $markArchivedBtn.data('archive'),
                    },
                    dataType: 'json',
                }).done(function (response) {
                    currentDatable.ajax.reload();
                    swal('{{ __('admin/alerts.success') }}!', response.status, 'success');
                }).fail(function (response) {
                    alert('{{ __('admin/alerts.loading_error') }}');
                });
            });

            window.currentDatable.on('draw.dt', function () {
                window.select2Ready(() => {
                    $('.select2').select2({
                        theme: 'bootstrap',
                    });

                    $('.select2').on('change', function (e) {
                        var $this = $(this);
                        var value = $this.val();
                        if ($this.data('loading') === true) {
                            e.preventDefault();

                            return;
                        }

                        $this.data('loading', true);
                        $this.next('.select2').addClass('loading');
                        $.ajax({
                            type: 'PUT',
                            url: '{{ route('admin.messages.assign-workers') }}',
                            data: {
                                message_id: $(this).data('message-id'),
                                workers: value,
                            },
                            dataType: 'json',
                        })
                            .done(function () {
                                $this.next('.select2').removeClass('loading');
                            })
                            .fail(function () {
                                alert('{{ __('common.alerts.ajax_error') }}');
                            })
                            .always(function () {
                                $this.data('loading', false);
                            });
                    });
                });
            });
        });
    </script>
@append
