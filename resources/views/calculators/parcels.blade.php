@extends('layouts.landing')

@section('content')
    <div id="page_calculators" class="bg-gray">
        <section class="">
            <div class="container">

                <div class="row">
                    <div class="text-center calculator__intro col-md-12">
                        <p class="lead">{{ __('calculators.parcels.shipment_transportation') }}</p>

                        <p>{{ __('calculators.parcels.calculate_the_transportation_cost_of_sea_and_air_shipments') }}</p>

                    </div>
                </div>

                <div class="box-bordered">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-offset-1 col-xs-10">

                                {!! Former::openVertical()->id('calculate') !!}

                                <div class="row">
                                    <div class="col-lg-6">
                                        {!! Former::select('country_id', __('calculators.parcels.country_of_delivery'))->addClass('select2')->options([]) !!}
                                    </div>

                                    <div class="col-lg-6 form-group">
                                        <label for="units[]" class="control-label" style="margin-bottom: 22px">
                                          {{ __('calculators.parcels.units') }}
                                        </label>
                                        {!! Former::inline_radios('units', '')->radios([
                                            ' lbs/in' => ['name' => 'units', 'value' => 'usa'],
                                            ' kg/cm' => ['name' => 'units', 'value' => 'european'],
                                        ])->check('usa') !!}
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        {!! Former::number('weight', __('calculators.parcels.weight_in_pounds'))->required() !!}
                                    </div>

                                    <div class="col-lg-6 form-group">
                                        <label for="length" class="control-label">{{ __('calculators.parcels.dimensions_in_inches') }}<sup>*</sup></label>

                                        <div class="row">
                                            <div class="col-xs-4">
                                                {!! Former::number('length', '')->required() !!}
                                            </div>
                                            <div class="col-xs-4">
                                                {!! Former::number('width', '')->required() !!}
                                            </div>
                                            <div class="col-xs-4">
                                                {!! Former::number('height', '')->required() !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center" style="margin-top: 40px; margin-bottom: 40px;">
                                    {!! Former::large_red_submit(__('calculators.parcels.calculate_the_cost')) !!}
                                </div>

                                {!! Former::close() !!}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="box-bordered no-padding calculator__table">
                    <p class="text-center calculator__table__help_text">
                      {{ __('calculators.parcels.enter_data_to_calculate_transportation_costs') }}
                    </p>
                    <div class="table-responsive">
                        <table class="table table-blue" id="prices-table">
                            <tbody>
                            <tr>
                                <th data-order="warehouse">{{ __('calculators.parcels.warehouse') }}</th>
                                <th data-order="delivery_time_days_max">{{ __('calculators.parcels.delivery_time') }}</th>
                                <th data-order="delivery_type">{{ __('calculators.parcels.type_of_delivery') }}</th>
                                <th data-order="insurance">{{ __('calculators.parcels.insurance') }}</th>
                                <th data-order="price" class="sort-up">{{ __('calculators.parcels.price') }}</th>
                                <th data-order="price">{{ __('calculators.parcels.price') }}*</th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="calculator__table__points">
                        <strong>{{ __('calculators.parcels.price') }}*</strong> - {{ __('calculators.parcels.price_description') }}
                        <br/>
                        <strong>{{ __('calculators.parcels.promotion') }}:</strong> {{ __('calculators.parcels.promotion_description') }}
                        <br/>
                        <br/>
                        <strong>{{ __('calculators.parcels.useful_links') }}:</strong>
                        <br/>
                        <a href="{{ route('parcels.address') }}" rel="nofollow">{{ __('calculators.parcels.warehouse_addresses') }}</a><br/>
                        <a href="{{ route('parcels.warehouses-comparison') }}" rel="nofollow">{{ __('calculators.parcels.comparison_warehouse') }}</a><br/>
                        <a href="{{ route('calculators.consolidation') }}">{{ __('calculators.parcels.cost_of_parcel_consolidation') }}</a><br/>
                        <a href="{{ route('points.index') }}" rel="nofollow">{{ __('calculators.parcels.how_the_points_works') }}</a>
                    </p>
                </div>
            </div>
        </section>
    </div>
@endsection

@section('scripts')
    <script type="module">
        var prices = [];
        var loading = false;
        var $form = $('form#calculate');
        var unit = 'usa';

        $form.find('input[type=radio][name=units]').on('change', function () {
            var value = $(this).val();
            changeUnits(value);
        });

        const changeUnits = function (newUnit) {
            unit = newUnit;
            var $lengthLabel = $form.find('label[for=length]');
            var $weightLabel = $form.find('label[for=weight]');
            if (unit === 'usa') {
                $lengthLabel.html("{{ __('calculators.parcels.dimensions_in_inches') }} . <sup>*</sup>");
                $weightLabel.html("{{ __('calculators.parcels.weight_in_pounds') }} . <sup>*</sup>");
            } else if (unit === 'european') {
                $lengthLabel.html("{{ __('calculators.parcels.dimensions_in_centimeters') }} . <sup>*</sup>");
                $weightLabel.html("{{ __('calculators.parcels.weight_in_kilograms') }} . <sup>*</sup>");
            }
        };

        $('[data-order]').click(function (e) {
            if (loading) {
                return false;
            }
            var orderKey = $(this).data('order');
            var orderType = 'asc';
            if ($(this).hasClass('sort-up')) {
                orderType = 'desc';
            }
            prices = prices.sort(function (a, b) {
                if (a[orderKey] > b[orderKey]) {
                    if (orderType === 'desc') {
                        return -1;
                    }
                    return 1;
                }
                if (a[orderKey] < b[orderKey]) {
                    if (orderType === 'desc') {
                        return 1;
                    }
                    return -1;
                }
                return 0;
            });
            var $table = $('#prices-table');
            $table.find('th').removeClass('sort-up sort-down');
            $(this).addClass(orderType === 'asc' ? 'sort-up' : 'sort-down');
            cleanTable();
            insertPricesToTable(prices);
        });

        $form.submit(function (e) {
            e.preventDefault();
            if (loading) {
                return false;
            }
            var form = getFormData(this);
            prices = calcPrices(form);
            showTable();
            cleanTable();
            // Imitate loading effect
            setTimeout(function () {
                insertPricesToTable(prices);
                loading = false;
            }, 300)
        });

        const showTable = function () {
            var $tableContainer = $('.calculator__table');
            $tableContainer.addClass('calculator__table--show_table');
        };

        const cleanTable = function () {
            var $table = $('#prices-table');
            $table.find('tr').not(':first').remove();
        };

        const insertPricesToTable = function (prices) {
            var $table = $('#prices-table');
            for (var i = 0; i < prices.length; i++) {
                $table.find('tbody').append('<tr>' +
                    '<td>' + prices[i].warehouse + '</td>' +
                    '<td>' + prices[i].delivery_time + '</td>' +
                    '<td>' + prices[i].delivery_type + '</td>' +
                    '<td>' + prices[i].insurance + '</td>' +
                    '<td>$ ' + parseFloat(prices[i].price).toFixed(2) + '</td>' +
                    '<td>' + pointsPromotionPrice(prices[i].price) + '</td>' +
                    '</tr>');
            }
        };

        const pointsPromotionPrice = function (price) {
            const discount = 25;

            if (price <= discount) {
                return '-';
            }

            return '$ ' + (price - discount).toFixed(2);
        };

        const calcPrices = function (form) {
            var weightConvert = unit === 'usa' ? 1 : 2.2046;
            var dimensionsConvert = unit === 'usa' ? 1 : 0.3937;
            var originalWeight = Math.round(parseInt(form.weight) * weightConvert);
            var weightFromDimensions = Math.round(((parseInt(form.length) * dimensionsConvert * parseInt(form.width) * dimensionsConvert * parseInt(form.height) * dimensionsConvert) / 366) / (unit === 'usa' ? 0.454 : 1) * weightConvert);
            var prices = [];
            var country = form.country_id;

            var weight = originalWeight;
            if (weightFromDimensions > originalWeight) {
                weight = weightFromDimensions;
            }

            if (country == 2) {
                prices = prices.concat(getAirShippingTypesForPolamer(weight));
                prices = prices.concat(getSeaShippingTypesForPolamer(weight));
            }

            prices = prices.concat(getAirShippingTypesForMelrosePark(weight));
            prices = prices.concat(getSeaShippingTypesForMelrosePark(weight));

            prices = prices.sort(function (a, b) {
                if (a.price > b.price) {
                    return 1;
                }
                if (a.price < b.price) {
                    return -1;
                }
                return 0;
            });

            return prices;
        };

        const getAirShippingTypesForMelrosePark = function (weight) {
            // Przesyłka lotnicza
            var price = 35.25;

            if (weight > 5 && weight <= 70) {
                price += ((weight - 5) * 2.59);
            }

            if (weight > 70 && weight <= 120) {
                price += ((weight - 70) * 3.39) + (65 * 2.59);
            }

            if (weight > 120) {
                price += ((weight - 120) * 2.59) + (50 * 3.39) + (65 * 2.59);
            }

            return [
                {
                    warehouse: "Melrose Park, IL",
                    delivery_time: "{{ __('calculators.parcels.airborne') }}",
                    delivery_time_days_max: '7',
                    delivery_type: "{{ __('calculators.parcels.door_to_door_delivery') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price
                },
            ];
        };

        const getSeaShippingTypesForMelrosePark = function (weight) {
            // Przesyłka morksa
            var price = 31.60;

            if (weight > 15 && weight <= 70) {
                price += ((weight - 15) * 0.84);
            }

            if (weight > 70 && weight <= 85) {
                price += ((weight - 70) * 1.69) + (55 * 0.84);
            }

            if (weight > 85 && weight <= 120) {
                price += ((weight - 85) * 1.69) + (15 * 1.69) + (55 * 0.84);
            }

            if (weight > 120) {
                price += ((weight - 120) * 0.84) + (35 * 1.69) + (15 * 1.69) + (55 * 0.84) - 10;
            }

            return [
                {
                    warehouse: "Melrose Park, IL",
                    delivery_time: "{{ __('calculators.parcels.marine') }}",
                    delivery_time_days_max: '42',
                    delivery_type: "{{ __('calculators.parcels.door_to_door_delivery') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price
                },
            ];
        };

        const getAirShippingTypesForPolamer = function (weight) {
            // Przesyłka lotnicza
            var price = 40.70;

            if (weight <= 2) {
                price = 26;
            }

            if (weight > 5 && weight <= 70) {
                price += ((weight - 5) * 2.98);
            }

            if (weight > 70 && weight <= 120) {
                price += ((weight - 70) * 3.63) + (65 * 2.98);
            }

            if (weight > 120) {
                price += ((weight - 120) * 2.98) + (50 * 3.63) + (65 * 2.98);
            }

            return [
                {
                    warehouse: "Des Plaines, IL",
                    delivery_time: "{{ __('calculators.parcels.airborne') }}",
                    delivery_time_days_max: '7',
                    delivery_type: "{{ __('calculators.parcels.door_to_door_delivery') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price
                },
                {
                    warehouse: "Des Plaines, IL",
                    delivery_time: "{{ __('calculators.parcels.airborne') }}",
                    delivery_time_days_max: '7',
                    delivery_type: "{{ __('calculators.parcels.pickup_at_point') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price - (price * 0.15)
                }
            ];
        };

        const getSeaShippingTypesForPolamer = function (weight) {
            // Przesyłka morksa
            var price = 37.10;

            if (weight > 15 && weight <= 70) {
                price += ((weight - 15) * 0.98);
            }

            if (weight > 70 && weight <= 85) {
                price += ((weight - 70) * 1.73) + (55 * 0.98);
            }

            if (weight > 85 && weight <= 120) {
                price += ((weight - 85) * 1.73) + (15 * 1.73) + (55 * 0.98);
            }

            if (weight > 120) {
                price += ((weight - 120) * 0.98) + (35 * 1.73) + (15 * 1.73) + (55 * 0.98) - 10;
            }

            return [
                {
                    warehouse: "Des Plaines, IL",
                    delivery_time: "{{ __('calculators.parcels.marine') }}",
                    delivery_time_days_max: '42',
                    delivery_type: "{{ __('calculators.parcels.door_to_door_delivery') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price
                },
                {
                    warehouse: "Des Plaines, IL",
                    delivery_time: "{{ __('calculators.parcels.marine') }}",
                    delivery_time_days_max: '42',
                    delivery_type: "{{ __('calculators.parcels.pickup_at_point') }}",
                    insurance: "{{ __('calculators.parcels.available') }}",
                    price: price - (price * 0.15)
                }
            ];
        };

        const getCountries = function () {
            var list = [];
            list.push({id: 2, text: "{{ __('calculators.parcels.poland') }}"});
            list.push({id: 5, text: "{{ __('calculators.parcels.germany') }}"});

            return list;
        };

        const populateSelect2 = function (target, data) {
            $(target).text('');

            $.each(data, function (key, value) {
                $(target).append($("<option></option>").attr("value", value.id).text(value.text));
            });

            $(target).select2('destroy');
            $(target).select2('');
        };

        window.select2Ready(() => populateSelect2('#country_id', getCountries()));
    </script>
@stop
