@extends('layouts.landing')

@section('content')
    <div id="page_calculators" class="bg-gray">
        <section class="">
            <div class="container">

                <div class="row">
                    <div class="text-center calculator__intro col-md-12">
                        <p class="lead">Opłaty celne pojazdów</p>

                        <p>Sprowadzasz pojazdy? Skorzystaj z przejrzystego symulatora kosztów opłat celnych.
                        </p>
                    </div>
                </div>

                <div class="box-bordered">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-offset-1 col-xs-10">

                                {!! Former::openVertical()->id('calculate') !!}

                                <p class="head">Kalkulator</p>

                                <div class="row">
                                    <div class="col-md-5">
                                        {!! Former::select('type', 'Rodzaj pojazdu')->addClass('select2')->options([2 => 'Motocykl', 1 => 'Samochód', 3 => 'Samochód zabytkowy']) !!}
                                    </div>

                                    <div class="col-md-3">
                                        {!! Former::number('price', 'Cena w dolarach')->required() !!}
                                    </div>

                                    <div class="col-md-4">
                                        {!! Former::select('engine', 'Pojemność silnika')->addClass('select2')->options([1 => '<2 litry', 2 => '>= 2 litry']) !!}
                                    </div>
                                </div>

                                {!! Former::select('country', 'Kraj dostawy')->addClass('select2')->options([1 => 'Polska', 2 => 'Niemcy']) !!}

                                <div class="text-center">
                                    {!! Former::large_red_submit('Oblicz koszt') !!}
                                </div>

                                {!! Former::close() !!}

                                <div class="calculator__results">

                                    <div id="calculated_results" class="text-center calculator__results__values"
                                         style="display: none">
                                        <p class="head text-left">Przewidywany koszt opłat celnych</p>

                                        <!--
                                        <p class="calculator__customs__thead text-left"><strong>Clenie samochodu w
                                                Polsce</strong></p>
                                        -->

                                        <div class="clearfix calculator__customs__calc">
                                            <p class="pull-left">Wartość samochodu</p>
                                            <p id="calcValue" class="pull-right text-bold">&nbsp;</p>
                                        </div>
                                        <div class="clearfix calculator__customs__calc">
                                            <p class="pull-left">Cło</p>
                                            <p id="calcCustom" class="pull-right">&nbsp;</p>
                                        </div>
                                        <div class="clearfix calculator__customs__calc">
                                            <p class="pull-left">Akcyza (bazując na pojemności silnika: <span
                                                        id="setExcise"></span>%)</p>
                                            <p id="calcExcise" class="pull-right">&nbsp;</p>
                                        </div>
                                        <div class="clearfix calculator__customs__calc">
                                            <p class="pull-left">VAT</p>
                                            <p id="calcVAT" class="pull-right">&nbsp;</p>
                                        </div>
                                        <div class="clearfix calculator__customs__calc">
                                            <p class="pull-left text-bold">Suma całkowita</p>
                                            <p id="calcSum" class="pull-right text-bold">&nbsp;</p>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

@section('scripts')
    <script type="module">
      $(function() {
        $('select#engine').closest('.form-group').hide();
      });
      $('form#calculate').submit(function (e) {
        e.preventDefault();
        var form = getFormData(this);
        var value = form.price;
        var excise = getExcise(form);
        var resultsBox = $('#calculated_results');

        if (resultsBox.is(':hidden')) {
          resultsBox.slideToggle();
        }


        $('#calcValue').text(value + '$');
        if (form.type != 3) {
          $('#calcCustom').closest('.calculator__customs__calc').show();
          $('#calcExcise').closest('.calculator__customs__calc').show();
          $('#calcCustom').html('$' + parseFloat(value).toFixed(2) + ' (+' + getCustom(form) + '%) = <strong>' + calcCustom(value, form) + '$</strong>');
          $('#calcExcise').html('$' + calcCustomSumPrice(value, form) + ' (+' + excise + '%) = <strong>' + calcExcise(calcCustom(value, form), excise, value) + '$</strong>');
          var sum = (parseFloat(calcCustom(value, form)) + parseFloat(calcExcise(calcCustom(value, form), excise, value)) + parseFloat(calcVAT(calcCustom(value, form), form, value))).toFixed(2);
        } else {
          $('#calcCustom').closest('.calculator__customs__calc').hide();
          $('#calcExcise').closest('.calculator__customs__calc').hide();
          var sum = parseFloat(calcVAT(calcCustom(value, form), form, value)).toFixed(2);
        }
        $('#calcVAT').html('$' + calcCustomSumPrice(value, form) + ' (+ ' + getVAT(form) + '%) = <strong>' + calcVAT(calcCustom(value, form), form, value) + '$</strong>');

        $('#calcSum').text(sum + '$');
        $('#setExcise').text(excise);
      });

      const formulaCustom = function (form) {
        if (form.type == 1) {
          return 0.1;
        }
        else if (form.type == 2) {
          return 0.06;
        } else if (form.type == 3) {
          return 0;
        }

        return 'NaN';
      };

      const formulaVAT = function (form) {
        if (form.type == 3) {
          return 0.07;
        }
        if (form.country == 1) {
          if (form.type == 1) {
            return 0.23;
          }
          else if (form.type == 2) {
            return 0.23;
          }
        }
        else if (form.country == 2) {
          if (form.type == 1) {
            return 0.19;
          }
          else if (form.type == 2) {
            return 0.19;
          }
        }

        return 'NaN';
      };

      const calcCustomSumPrice = function (price, form) {
        var custom = calcCustom(price, form);
        return (parseFloat(custom) + parseFloat(price)).toFixed(2);
      };

      const calcCustom = function (price, form) {
        var value = formulaCustom(form);

        return (parseFloat(price) * value).toFixed(2);
      };

      const calcExcise = function (price, excise, value) {
        return ((parseFloat(value) + parseFloat(price)) * (excise / 100)).toFixed(2);
      };

      const calcVAT = function (price, form, value) {
        var vat = formulaVAT(form);

        return ((parseFloat(value) + (parseFloat(price))) * vat).toFixed(2);
      };

      const getExcise = function (form) {
        if (form.type == 1) {
          if (form.engine == 1) {
            return 3.1;
          }

          else if (form.engine == 2) {
            return 18.6;
          }
        }

        else if (form.type == 2) {
          return 0;
        }

        return 'NaN';
      };

      const getCustom = function (form) {
        return formulaCustom(form) * 100;
      };

      const getVAT = function (form) {
        return (formulaVAT(form) * 100).toFixed(0);
      };

      $('select#type').change(function () {
        if ($(this).val() == 1) {
          $('select#engine').closest('.form-group').show();
        }
        else {
          $('select#engine').closest('.form-group').hide();
        }
      });
    </script>
@stop
