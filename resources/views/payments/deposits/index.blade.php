@php
    /** @var \App\ViewModels\Dashboard\Payments\PaymentsDepositsViewModel $viewModel */
@endphp

@extends('layouts.base')

@section('content')
    <div id="payments" class="bg-gray">
        <section class="narrow-padded">
            <div class="container">
                @include('payments.partials._tabs')

                @include('payments.partials._account-balance', [
                    'balances' => $viewModel->balances,
                ])

                <div class="page-actions">
                    <div class="page-actions__btns">
                        <a href="{{ route('payments.info') }}" class="btn btn-red">
                            <strong>{{ __('payments.index.make_a_deposit') }}</strong>
                        </a>
                    </div>

                    <div class="page-actions__filters">
                        {!! Former::openInline()->method('GET')->id('filters')->action(route('deposits.index')) !!}
                        @if($viewModel->companies->hasMany())
                            {!! Former::select('company')->options($viewModel->companies->forSelect()) !!}
                        @endif
                        {!! Former::select('amount_type')->options(['all' => __('payments.index.outgoings_and_income'), 'outgoings' => __('payments.index.outgoings'), 'income' => __('payments.index.income')])->select('all') !!}
                        {!! Former::text('search')->placeholder(__('payments.index.search'))->prepend('<i class="fa fa-search" aria-hidden="true"></i>') !!}
                        {!! Former::close() !!}
                    </div>
                </div>
                <div class="no-padding">
                    <div class="table-ajax">
                        @include('payments.deposits.partials.table-content')
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
