@php
    /** @var int $key */
    /** @var \App\Models\Presenters\CompanyPresenter $company */
@endphp
<div class="panel-group accordion" id="methods-{{$key}}" role="tablist" aria-multiselectable="true">
    @if(!empty($company->wise_account_name))
        <div class="panel panel-default">
            <a class="panel-heading collapsed" role="tab" id="methods-{{$key}}-wise" data-toggle="collapse"
               data-parent="#methods-{{$key}}" href="#collapse-wise-{{$key}}" aria-expanded="true"
               aria-controls="collapse-wise-{{$key}}">
                <img src="{{ asset('img/wise.svg') }}" class="svg accordion__icon">
                <span>
                    {{ __('payments.info.wise.fund_your_account') }}
                </span>
                <div class="accordion__toggle-icon"></div>
            </a>
            <div id="collapse-wise-{{$key}}" class="panel-collapse collapse" role="tabpanel"
                 aria-labelledby="methods-{{$key}}-wise">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p class="text-center">
                                <strong>{{ __('payments.info.headers.enter_the_customer_number_in_payment_title') }}</strong>
                            </p>
                            <div class="text-center">
                                <div class="client-number">
                                    {{ auth()->user()->getUserCode() }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8 content">
                            <p>
                                {!! __('payments.info.wise.description') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.how_its_working') }}</strong><br/>
                                {!! __('payments.info.wise.how_its_working_url') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.customer_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.wise.customer_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.order_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.wise.order_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.remember') }}</strong><br/>
                                {{ __('payments.info.wise.remember_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.promo_code') }}</strong><br/>
                                {!! __('payments.info.wise.promo_code_description') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.bank_transfer_details') }}</strong><br/>
                                {!! __('payments.info.recipient') !!} {{ $company->name }}<br/>
                                @if(!empty($company->bank_account_number))
                                    {!! __('payments.info.account_number') !!} {{ $company->bank_account_number }}<br/>
                                @endif
                                @if(!empty($company->bank_branch_number))
                                    {!! __('payments.info.branch_number') !!} {{ $company->bank_branch_number }}<br/>
                                @endif
                                E-mail: {{ $company->wise_account_name }}<br/>
                            </p>

                            @if(!empty($company->bank_address))
                                <p>
                                    <strong>{{ __('payments.info.headers.bank_address') }}</strong><br/>
                                    {!! nl2br($company->bank_address) !!}
                                </p>
                            @endif

                            <p>
                                <strong>{{ __('payments.info.headers.returns') }}</strong><br/>
                                {{ __('payments.info.wise.returns_description') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(!empty($company->revoult_account_name))
        <div class="panel panel-default">
            <a class="panel-heading collapsed" role="tab" id="methods-{{$key}}-revoult" data-toggle="collapse"
               data-parent="#methods-{{$key}}" href="#collapse-revoult-{{$key}}" aria-expanded="false"
               aria-controls="collapse-revoult-{{$key}}">
                <img src="{{ asset('img/revolut-icon.svg') }}" class="svg accordion__icon">
                <span>
                    {{ __('payments.info.revolut.fund_your_account') }}
                </span>
                <div class="accordion__toggle-icon"></div>
            </a>
            <div id="collapse-revoult-{{$key}}" class="panel-collapse collapse" role="tabpanel"
                 aria-labelledby="methods-{{$key}}-revoult">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p class="text-center">
                                <strong>{{ __('payments.info.headers.enter_the_customer_number_in_payment_title') }}</strong>
                            </p>
                            <div class="text-center">
                                <div class="client-number">
                                    {{ auth()->user()->getUserCode() }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8 content">
                            <p>
                                {!! __('payments.info.revolut.description') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.customer_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.revolut.customer_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.order_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.revolut.order_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.remember') }}</strong><br/>
                                {{ __('payments.info.revolut.remember_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.bank_transfer_details') }}</strong><br/>
                                {!! __('payments.info.recipient') !!} {{ $company->name }}<br/>
                                @if(!empty($company->bank_account_number))
                                    {!! __('payments.info.account_number') !!} {{ $company->bank_account_number }}<br/>
                                @endif
                                @if(!empty($company->bank_branch_number))
                                    {!! __('payments.info.branch_number') !!} {{ $company->bank_branch_number }}<br/>
                                @endif
                                E-mail: {{ $company->revoult_account_name }}<br/>
                            </p>

                            @if(!empty($company->bank_address))
                                <p>
                                    <strong>{{ __('payments.info.headers.bank_address') }}</strong><br/>
                                    {!! nl2br($company->bank_address) !!}
                                </p>
                            @endif

                            <p>
                                <strong>{{ __('payments.info.headers.returns') }}</strong><br/>
                                {{ __('payments.info.revolut.returns_description') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(!empty($company->bank_account_number))
        <div class="panel panel-default">
            <a class="panel-heading collapsed" role="tab" id="methods-{{$key}}-bank-transfer" data-toggle="collapse"
               data-parent="#methods-{{$key}}" href="#collapse-bank-transfer-{{$key}}" aria-expanded="false"
               aria-controls="collapse-bank-transfer-{{$key}}">
                <img src="{{ asset('img/money.svg') }}" class="svg accordion__icon accordion__icon--red">
                <span>
                    {{ __('payments.info.bank_transfer.fund_your_account') }}
                </span>
                <div class="accordion__toggle-icon"></div>
            </a>
            <div id="collapse-bank-transfer-{{$key}}" class="panel-collapse collapse" role="tabpanel"
                 aria-labelledby="methods-{{$key}}-bank-transfer">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p class="text-center">
                                <strong>{{ __('payments.info.headers.enter_the_customer_number_in_payment_title') }}</strong>
                            </p>
                            <div class="text-center">
                                <div class="client-number">
                                    {{ auth()->user()->getUserCode() }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8 content">
                            <p>
                                {!! __('payments.info.bank_transfer.description') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.customer_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.bank_transfer.customer_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.order_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.bank_transfer.order_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.bank_transfer_details') }}</strong><br/>
                                {!! __('payments.info.recipient') !!} {{ $company->name }}<br/>
                                {!! __('payments.info.account_number') !!} {{ $company->bank_account_number }}<br/>
                                @if(!empty($company->bank_swift_code))
                                    {!! __('payments.info.swift_code') !!} {{ $company->bank_swift_code }}<br/>
                                @endif
                            </p>

                            @if(!empty($company->bank_address))
                                <p>
                                    <strong>{{ __('payments.info.headers.bank_address') }}</strong><br/>
                                    {!! nl2br($company->bank_address) !!}
                                </p>
                            @endif

                            <p>
                                <strong>{{ __('payments.info.headers.returns') }}</strong><br/>
                                {{ __('payments.info.bank_transfer.returns_description') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(!empty($company->zelle_account_name))
        <div class="panel panel-default">
            <a class="panel-heading collapsed" role="tab" id="methods-{{$key}}-zelle" data-toggle="collapse"
               data-parent="#methods-{{$key}}" href="#collapse-zelle-{{$key}}" aria-expanded="false"
               aria-controls="collapse-zelle-{{$key}}">
                <img src="{{ asset('img/icons8-zelle.svg') }}"
                     class="svg accordion__icon">
                <span>
                    {{ __('payments.info.zelle.fund_your_account') }}
                </span>
                <div class="accordion__toggle-icon"></div>
            </a>
            <div id="collapse-zelle-{{$key}}" class="panel-collapse collapse" role="tabpanel"
                 aria-labelledby="methods-{{$key}}-zelle">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p class="text-center">
                                <strong>{{ __('payments.info.headers.enter_the_customer_number_in_payment_title') }}</strong>
                            </p>
                            <div class="text-center">
                                <div class="client-number">
                                    {{ auth()->user()->getUserCode() }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8 content">
                            <p>
                                {!! __('payments.info.zelle.description') !!}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.customer_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.zelle.customer_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.order_number_in_payment_title') }}</strong><br/>
                                {{ __('payments.info.zelle.order_number_in_payment_title_description') }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.bank_transfer_details') }}</strong><br/>
                                {{ $company->zelle_account_name }}
                            </p>

                            <p>
                                <strong>{{ __('payments.info.headers.returns') }}</strong><br/>
                                {{ __('payments.info.zelle.returns_description') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
