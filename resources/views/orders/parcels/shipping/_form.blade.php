<div class="form-wizard-container">
    <div id="intro">
        <p class="head head-intro text-center">{{ __('parcels.form.shipping.title') }}</p>

        <div class="row">
            <div class="col-md-offset-2 col-md-8">
                <p class="text-center">
                    {!! __('parcels.form.shipping.description', ['siteName' => config('site.name')]) !!}
                    <br><strong>{{ $currentWarehouse->name . ', ' .  $currentWarehouse->address }}</strong></p>
                <p class="text-center" style="margin-top: 30px">{!! __('parcels.form.shipping.warning') !!}</p>
            </div>
        </div>

        <div class="text-center commercial-buttons">
            <button class="btn btn-red selectScenario" data-scenario="{{ \App\Models\OrderShopping::ONLINE_CART }}">
              {{ __('parcels.form.shipping.start') }}</button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-offset-1 col-xs-10">
        {!! Former::open_for_files()->id('wizard')->style('display: none;')->addClass('repeatable-form fieldset')->action(route('warehouse.orders.store', [$currentWarehouse->slug, 'parcels']))->method('POST') !!}
        {!! Former::hidden('service')->value('shipping') !!}

        <h1>1</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipping.where_are_you_ordering_the_pickup_from') }}?</p>

                <p>{{ __('parcels.form.shipping.fill_data') }}</p>
            </div>

            <p><strong>{{ __('parcels.form.shipping.shipping_from') }}:</strong></p>

            {!! Former::select('from[type]', __('parcels.form.shipping.type_of_location'))->addClass('select2')
            ->options([__('parcels.form.shipping.private_person') => __('parcels.form.shipping.private_person'), __('parcels.form.shipping.business_with_a_forklift_or_a_transhipment_terminal') => __('parcels.form.shipping.business_with_a_forklift_or_a_transhipment_terminal')]) !!}

            <div class="row">
                <div class="col-md-4">
                    {!! Former::text('from[postcode]', __('parcels.form.shipping.postal_code'))->addClass('required') !!}
                </div>

                <div class="col-md-4">
                    {!! Former::text('from[city]', __('parcels.form.shipping.city'))->addClass('required') !!}
                </div>

                <div class="col-md-4">
                    {!! Former::text('from[state]', __('parcels.form.shipping.state'))->addClass('required') !!}
                </div>
            </div>

            <p><strong>{{ __('parcels.form.shipping.shipping_to') }}:</strong></p>

            <p>{{ $currentWarehouse->name . ', ' .  $currentWarehouse->address }}</p>

        </div>

        <h1>2</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipping.what_are_you_sending') }}</p>

                <p>{{ __('parcels.form.shipping.what_are_you_sending_description') }}</p>
            </div>

            <div style="display: none;" class="single-item form-fields form-preset">
                <div class="row">
                    <div class="col-md-8">
                        {!! Former::select('items[][item_type]', __('parcels.form.shipping.shipping_type'))->options([__('parcels.form.shipping.carton') => __('parcels.form.shipping.carton')])->disabled() !!}
                    </div>
                    <div class="col-md-4">
                        {!! Former::select('items[][dangerous]', __('parcels.form.shipping.hazardous_material'))->options([__('parcels.form.shipping.no') => __('parcels.form.shipping.no'), __('parcels.form.shipping.yes') => __('parcels.form.shipping.yes')])->disabled() !!}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-4">
                                {!! Former::text('items[][length]', __('parcels.form.shipping.length_in_inches'))->disabled() !!}
                            </div>
                            <div class="col-md-4">
                                {!! Former::text('items[][width]', __('parcels.form.shipping.width_in_inches'))->disabled() !!}
                            </div>
                            <div class="col-md-4">
                                {!! Former::text('items[][height]', __('parcels.form.shipping.height_in_inches'))->disabled() !!}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        {!! Former::text('items[][weight]', __('parcels.form.shipping.weight_in_pounds'))->disabled() !!}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-5">
                        {!! Former::text('items[][url]', __('parcels.form.shipping.link_to_auction'))->disabled() !!}
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ __('parcels.form.shipping.photo_of_product') }}</label>
                        <div class="btn btn-default btn-file btn-wizard-attachments">
                            <label for="items[][files][]"><i class="fa fa-paperclip"></i> <span>{{ __('parcels.form.shipping.add_attachment') }}</span></label>
                            <input multiple="true" id="items[][files][]" type="file" name="items[][files][]" accept="application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint,
                text/plain, application/pdf, image/*" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        {!! Former::text('items[][desc]', __('parcels.form.shipping.additional_description')) !!}
                    </div>
                </div>

                <button class="btn btn-red btn-delete remove-form-fields"><i class="fa fa-times"></i></button>
            </div>

            <div class="items fieldset-container">
                <div class="single-item form-fields">
                    <div class="row">
                        <div class="col-md-8">
                            {!! Former::select('items[0][item_type]', __('parcels.warehouses_comparison.type_of_shipping'))->options([__('parcels.form.shipping.carton') => __('parcels.form.shipping.carton')]) !!}
                        </div>
                        <div class="col-md-4">
                            {!! Former::select('items[0][dangerous]', __('parcels.form.shipping.hazardous_material'))->options([__('parcels.form.shipping.no') => __('parcels.form.shipping.no'), __('parcels.form.shipping.yes') => __('parcels.form.shipping.yes')]) !!}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-4">
                                    {!! Former::text('items[0][length]', __('parcels.form.shipping.length_in_inches')) !!}
                                </div>
                                <div class="col-md-4">
                                    {!! Former::text('items[0][width]', __('parcels.form.shipping.width_in_inches')) !!}
                                </div>
                                <div class="col-md-4">
                                    {!! Former::text('items[0][height]', __('parcels.form.shipping.height_in_inches')) !!}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            {!! Former::text('items[0][weight]', __('parcels.form.shipping.weight_in_pounds')) !!}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-5">
                            {!! Former::text('items[0][url]', __('parcels.form.shipping.link_to_auction')) !!}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ __('parcels.form.shipping.photo_of_product') }}</label>
                            <div class="btn btn-default btn-file btn-wizard-attachments">
                                <label for="items[0][files][]"><i class="fa fa-paperclip"></i> <span>{{ __('parcels.form.shipping.add_attachment') }}</span></label>
                                <input multiple="true" id="items[0][files][]" type="file" name="items[0][files][]" accept="application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint,
                text/plain, application/pdf, image/*" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-4">
                            {!! Former::text('items[0][desc]', __('parcels.form.shipping.additional_description')) !!}
                        </div>
                    </div>

                    <button class="btn btn-red btn-delete remove-form-fields"><i class="fa fa-times"></i></button>
                </div>
            </div>

            <div class="text-right">
                <button type="button" class="btn btn-blue add-preset"><i class="fa fa-plus fa-fw"></i> {{ __('parcels.form.shipping.add_next_package') }}</button>
            </div>
        </div>

        <h1>3</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipping.forwarder') }}</p>

                <p>{{ __('parcels.form.shipping.fill_out_the_sender_details') }}</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('shipper[company_name]', __('parcels.form.shipping.company_name')) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('shipper[company_address]', __('parcels.form.shipping.company_address')) !!}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('shipper[contact_person]' , __('parcels.form.shipping.first_and_last_name_to_contact_person')) !!}
                </div>
                <div class="col-md-3">
                    {!! Former::text('shipper[phone]' , __('parcels.form.shipping.phone')) !!}

                </div>
                <div class="col-md-3">
                    {!! Former::text('shipper[phone_prefix]', __('parcels.form.shipping.internal')) !!}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('shipper[email]' , __('parcels.form.shipping.email')) !!}
                </div>
                <div class="col-md-3">
                    {!! Former::text('shipper[hours_from]' , __('parcels.form.shipping.pickup_time_from')) !!}

                </div>
                <div class="col-md-3">
                    {!! Former::text('shipper[hours_to]', __('parcels.form.shipping.pickup_time_to')) !!}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('shipper[days_from]', __('parcels.form.shipping.days_of_week_from')) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('shipper[days_to]', __('parcels.form.shipping.days_of_week_to')) !!}
                </div>
            </div>

            {!! Former::text('shipper[articles_desc]', __('parcels.form.shipping.description_of_articles')) !!}

            {!! Former::text('shipper[comment]', __('parcels.form.shipping.additional_information')) !!}
        </div>

        <h1>4</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipping.customer_data') }}</p>

                <p>{{ __('parcels.form.shipping.customer_data_description') }}</p>
            </div>

            <?php
                $billing = $user->billing;
                $showCompany = $billing && $billing->is_company;
            ?>
            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('purchaser[first_name]', __('parcels.form.shipping.first_name'))->addClass('required')->value($user->first_name) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('purchaser[last_name]', __('parcels.form.shipping.last_name'))->addClass('required')->value($user->last_name) !!}
                </div>
            </div>

            {!! Former::text('purchaser[address]', __('parcels.form.shipping.address'))->addClass('required')->value(!$showCompany && $billing ? $billing->address : null) !!}

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('purchaser[city]', __('parcels.form.shipping.city'))->addClass('required')->value(!$showCompany && $billing ? $billing->city : null) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('purchaser[postcode]', __('parcels.form.shipping.postal_code'))->addClass('required')->value(!$showCompany && $billing ? $billing->zip_code : null) !!}
                </div>
            </div>

            {!! Former::text('purchaser[phone]', __('parcels.form.shipping.phone'))->addClass('required')->value(!$showCompany && $billing ? $billing->phone : null) !!}

            {!! Former::checkbox('purchaser[is_company]', '')->text(__('parcels.form.shipping.is_company'))->id('toggleCompany') !!}

            <div id="companyForm" style="display: none">
                {!! Former::text('purchaser[company_name]', __('parcels.form.shipping.company_name'))->value($showCompany ? $billing->company_name : null) !!}
                {!! Former::text('purchaser[company_address]', __('parcels.form.shipping.address'))->value($showCompany ? $billing->address : null) !!}

                <div class="row">
                    <div class="col-md-6">
                        {!! Former::text('purchaser[company_city]', __('parcels.form.shipping.city'))->value($showCompany ? $billing->city : null) !!}
                    </div>
                    <div class="col-md-6">
                        {!! Former::text('purchaser[company_postcode]', __('parcels.form.shipping.postal_code'))->value($showCompany ? $billing->zip_code : null) !!}
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        {!! Former::text('purchaser[nip]', __('parcels.form.shipping.nip'))->value($showCompany ? $billing->nip : null) !!}
                    </div>
                    <div class="col-md-6">
                        {!! Former::text('purchaser[regon]', __('parcels.form.shipping.regon')) !!}
                    </div>
                </div>

            </div>
        </div>

        <h1>5</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipping.account_details') }}</p>

                <p>
                  {{ __('parcels.form.shipping.account_details_description') }}
                </p>
            </div>

            @include('orders.partials._invoice_form')
        </div>

        {!! Former::close() !!}
    </div>
</div>
