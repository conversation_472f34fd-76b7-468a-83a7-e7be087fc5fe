<div class="form-wizard-container">
    <div id="intro">
        <p class="head head-intro text-center">{{$currentWarehouse->name}}</p>

        <div class="row">
            <div class="col-md-offset-2 col-md-8">
                <p class="text-center intro">{!! __('parcels.form.shipment.title', ['siteName' => config('site.name')]) !!}</p>
            </div>
        </div>

        <div class="row text-center commercial-buttons">
            <button class="btn btn-red isCommercial">{{ __('parcels.form.shipping.start') }}</button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-offset-1 col-xs-10">
        {!! Former::open_for_files()->id('wizard')->style('display: none;')->action(route('warehouse.orders.store', [$currentWarehouse->slug, 'parcels']))->method('POST') !!}
        {!! Former::hidden('service')->value('shipping') !!}

        <h1>1</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro.title') }}</p>

                <p>{{ __('parcels.form.shipment.intro.subtitle') }}</p>
            </div>

            <div class="alert alert-danger alert-dismissable alert-models" style="display: none">
                <button type="button" class="close" data-dismiss="alert">×</button>
                {{ __('parcels.form.shipment.validation_warning') }}
            </div>

            <div class="models">
                {!! Former::hidden('items') !!}
            </div>

            {!! Former::select('parcels', '')->fromQuery($parcels, 'tracking_number', 'id')->addClass('select2-m')->style('width: 100%')->placeholder(__('parcels.form.shipment.select_package')) !!}
            <button type="button" class="btn btn-primary select-2-add-all">
                {{ __('parcels.form.shipment.add_all_packages') }}
            </button>

            <p class="user_help_info">
                <i class="fa fa-exclamation-circle text-danger" aria-hidden="true"></i>
                <strong>{{ __('parcels.form.shipment.user_help_info') }}</strong>
            </p>
        </div>

        <h1>2</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro_2.title') }}</p>
                <p>{!! __('parcels.form.shipment.intro_2.subtitle', ['warehouseName' => $currentWarehouse->name]) !!}</p>
            </div>

            <div class="switch">
                <p class="switch__option">
                  {{ __('parcels.form.shipment.private_person') }}
                </p>
                <label class="switch">
                    <input type="hidden" value="0" name="sender[is_company]">
                    <input type="checkbox" value="1" name="sender[is_company]" id="toggleCompanySender">
                    <span class="switch__slider switch__slider--round"></span>
                </label>
                <p class="switch__option">
                  {{ __('parcels.form.shipment.company') }}
                </p>
            </div>

            <div class="row" id="naturalPersonSender">
                <div class="col-md-6">
                    {!! Former::text('sender[first_name]', __('parcels.form.shipment.first_name'))->addClass('required') !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('sender[last_name]', __('parcels.form.shipment.last_name'))->addClass('required') !!}
                </div>
            </div>

            <div class="row" id="companySender" style="display: none;">
                <div class="col-md-12">
                    {!! Former::text('sender[company_name]', __('parcels.form.shipment.company_name'))->addClass('required') !!}
                </div>
            </div>

            {!! Former::text('sender[address]', __('parcels.form.shipment.address'))->addClass('required') !!}

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('sender[city]', __('parcels.form.shipment.city'))->addClass('required') !!}
                </div>
                <div class="col-md-3">
                    {!! Former::text('sender[state]', __('parcels.form.shipment.state'))->addClass('required') !!}
                </div>
                <div class="col-md-3">
                    {!! Former::text('sender[postcode]', __('parcels.form.shipment.postal_code'))->addClass('required') !!}
                </div>
            </div>

            {!! Former::text('sender[phone]', __('parcels.form.shipment.phone'))->addClass('required') !!}
        </div>

        <h1>3</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro_3.title') }}</p>

                <p>{{ __('parcels.form.shipment.intro_3.subtitle') }}</p>
            </div>

            <?php
                $billing = $user->billing;
                $showCompany = $billing && $billing->is_company;
            ?>
            <div class="switch">
                <p class="switch__option">
                  {{ __('parcels.form.shipment.private_person') }}
                </p>
                <label class="switch">
                    <input type="hidden" value="0" name="recipient[is_company]">
                    <input type="checkbox" value="1" name="recipient[is_company]" id="toggleCompanyRecipient" @if($showCompany) checked="checked" @endif>
                    <span class="switch__slider switch__slider--round"></span>
                </label>
                <p class="switch__option">
                  {{ __('parcels.form.shipment.company') }}
                </p>
            </div>

            <div class="row" id="naturalPersonRecipient">
                <div class="col-md-6">
                    {!! Former::text('recipient[first_name]', __('parcels.form.shipment.first_name'))->addClass('required')->value($user->first_name) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('recipient[last_name]', __('parcels.form.shipment.last_name'))->addClass('required')->value($user->last_name) !!}
                </div>
            </div>

            <div class="row" id="companyRecipient" style="display: none;">
                <div class="col-md-12">
                    {!! Former::text('recipient[company_name]', __('parcels.form.shipment.company_name'))->value($billing ? $billing->company_name : null)->addClass('required') !!}
                </div>
            </div>

            {!! Former::text('recipient[address]', __('parcels.form.shipment.address'))->addClass('required')->value($billing ? $billing->address : null) !!}

            <div class="row">
                <div class="col-md-6">
                    {!! Former::text('recipient[city]', __('parcels.form.shipment.city'))->addClass('required')->value($billing ? $billing->city : null) !!}
                </div>
                <div class="col-md-6">
                    {!! Former::text('recipient[postcode]', __('parcels.form.shipment.postal_code'))->addClass('required')->value($billing ? $billing->zip_code : null) !!}
                </div>
            </div>

            {!! Former::select('recipient[country]', __('parcels.form.shipment.country'))->addClass('required select2')->options($countries->where('available_for_shipment', 1)->pluck('name', 'name'))->value(app()->getLocale() === 'pl' ? 'Polska' : 'Poland') !!}

            {!! Former::text('recipient[phone]', __('parcels.form.shipment.phone'))->addClass('required')->value($billing ? $billing->phone : null) !!}
        </div>

        <h1>4</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro_4.title') }}</p>

                <p>{{ __('parcels.form.shipment.intro_4.subtitle') }}</p>
            </div>

            {!! Former::text('customs_value', __('parcels.form.shipment.customs_value'))->addClass('required')->prepend('<i class="fa fa-usd fa-fw"></i>')->number('true') !!}
            {!! Former::text('customs_contains', __('parcels.form.shipment.customs_contains'))->addClass('required')->atleastoneletter('true') !!}
        </div>

        <h1>5</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro_5.title') }}</p>

                <p>
                    {{ __('parcels.form.shipment.intro_5.subtitle') }}
                </p>
            </div>

            @include('orders.partials._invoice_form')
        </div>

        <h1>6</h1>

        <div>
            <div class="intro">
                <p class="head">{{ __('parcels.form.shipment.intro_6.title') }}</p>

                <p>{!! __('parcels.form.shipment.intro_6.subtitle') !!}</p>
            </div>

            {!!
            Former::select('shipping_type', __('parcels.form.shipping.shipping_type'))->options(
                array_merge([__('parcels.form.shipping.select') => ['value' => '', 'disabled' => 'disabled']], array_map(static function($type) {
                    return __('parcels.shipping_types.' . $type);
                }, \App\Models\OrderShipment::SHIPPING_TYPES))
            )->addClass('required')->select('');
           !!}

            {!!
            Former::select('insurance', __('parcels.form.shipment.insurance'))->options([
                __('parcels.form.shipping.select') => ['value' => '', 'disabled' => 'disabled'],
                __('parcels.form.shipment.insurance_included_in_shipping_price', ['price' => '$100']) => __('parcels.form.shipment.insurance_included_in_shipping_price', ['price' => '$100']),
             ])->addClass('required')->select('')
             !!}

            <div class="form-group">
                <label class="control-label">{{ __('parcels.form.shipment.bill') }}</label>

                <p style="margin-bottom: 15px;">
                    {{ __('parcels.form.shipment.bill_info') }}
                </p>

                <div class="btn btn-default btn-file btn-wizard-attachments">
                    <label for="customs_attachments"><i class="fa fa-paperclip"></i>
                        <span>{{ __('parcels.form.shipment.add_a_document') }}</span></label>
                    <input multiple="true" id="customs_attachments" type="file" name="customs_attachments[]" accept="application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint,
                text/plain, application/pdf, image/*">
                </div>
            </div>
            {!! Former::textarea('comment_user', __('parcels.form.shipment.additional_information')) !!}

            {!! Former::text('discount_code', __('parcels.form.shipment.discount_code')) !!}

            <p class="user_help_info">
                <i class="fa fa-exclamation-circle text-danger" aria-hidden="true"></i>
                <strong>
                    {{ __('parcels.form.shipment.user_help_info_2') }}
                </strong>
            </p>

            <p class="user_help_info">
                <i class="fa fa-exclamation-circle text-danger" aria-hidden="true"></i>
                <strong>
                    {{ __('parcels.form.shipment.user_help_info_3') }}
                </strong>
            </p>
        </div>

        {!! Former::close() !!}
    </div>
</div>
