@extends('layouts.base')

@section('content')
    <div id="page_services" class="bg-gray">
        <section class="orders_create">
            <div class="container">

                @include('partials.alerts')

                <nav class="navbar navbar-default nav-bordered">
                    <div class="container-fluid">
                        <div class="navbar-header">
                            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"
                                    data-target="#section-nav" aria-expanded="false">
                                <span class="sr-only">Toggle navigation</span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                            </button>
                        </div>

                        <div class="collapse navbar-collapse" id="section-nav">
                            <ul class="nav navbar-nav">
                                <li>
                                    <a href="{{ route('orders.index', ['parcels', $currentWarehouse->slug]) }}">
                                      {{ __('common.cancel_and_return_to_list') }}
                                    </a></li>
                            </ul>
                        </div>
                    </div>
                </nav>

                <div class="box-bordered">
                    @include("orders.parcels.shopping._form")
                </div>

            </div>
        </section>
    </div>
@endsection

@section('scripts')
    @vite('node_modules/jquery-steps/build/jquery.steps.min.js')
    @vite('resources/js/validation.js')
    <script type="module">
        var form = $("#wizard");
        form.steps({
            titleTemplate: "#title#",
            labels: {
                previous: '{{ __('parcels.form.back') }}',
                next: '{{ __('parcels.form.next_step') }}',
                finish: '{{ __('parcels.form.order_shopping') }}'
            },
            onStepChanging: function (event, currentIndex, newIndex) {
                if (currentIndex > newIndex) {
                    return true;
                }

                if (currentIndex < newIndex) {
                    form.find(".body:eq(" + newIndex + ") label.error").remove();
                    form.find(".body:eq(" + newIndex + ") .has-error").removeClass("has-error");
                }

                return form.valid();
            },
            onStepChanged: function (event, currentIndex, priorIndex) {
            },
            onFinishing: function (event, currentIndex) {
                if (form.valid()) {
                    $(this).submit();
                }
            }
        }).validate();

        $(document).on('click', '.selectScenario', function (e) {
            e.preventDefault();

            var scenario = $(this).data('scenario');

            $('.scenario').hide();
            $('.scenario-' + scenario).show();

            $('.scenario').each(function () {
                if (!$(this).hasClass('scenario-' + scenario)) {
                    $(this).remove();
                }
            });

            $('.steps > ul > li').each(function (i, val) {
                $(val).find('a').text(i + 1);
            });

            $('input[name=scenario]').val(scenario);

            $('#intro').fadeOut('slow', function () {
                $('#wizard').fadeIn();
            });
        });
    </script>
@append
