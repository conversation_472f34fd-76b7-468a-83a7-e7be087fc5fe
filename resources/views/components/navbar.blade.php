<nav
  class="navbar"
  x-data="{ open: false }"
  aria-label="{{ __('web/landing.main_navigation') }}"
>
  <div class="navbar__logo">
    <a x-show="!open" href="{{ route('pages.home') }}" aria-label="{{ __('web/landing.site_title') }}">
      <x-logo />
    </a>
    @if (! $hideSearch)
      <div x-show="open" class="navbar__search-form navbar__search-form--mobile">
        @include('new.partials.search-vehicle', ['closeAction' => 'open = false'])
      </div>
    @endif
  </div>

  @if (! $hideSearch)
    <div class="navbar__search-form navbar__search-form--desktop">
      @include('new.partials.search-vehicle')
    </div>
  @endif

  <div class="navbar__actions">
    <div class="navbar__lang-switch">
      @include('new.partials.lang-switch')
    </div>

    @if (auth()->check())
      <a href="{{ route('profile.dashboard') }}" class="btn btn-primary navbar__login-btn">
        {{ __('web/landing.dashboard') }}
      </a>
      <div class="navbar__lang-switch">
        @include('new.partials.user-menu')
      </div>
    @else
      @if (! $hideLoginBtn)
        <a href="{{ route('login') }}" class="btn btn-primary navbar__login-btn">
          {{ __('web/landing.login') }}
        </a>
      @endif
    @endif

    @if (! $hideSearch)
      <button x-show="!open" class="navbar__btn navbar__search-btn" @click="open = true">
        <x-icons.search-icon />
      </button>
    @endif

    <button
      id="mobile-menu-open"
      class="navbar__btn navbar__hamburger-btn"
      @click="$store.menu.openMenu()"
    >
      <x-icons.menu-icon />
    </button>
  </div>
</nav>
@include('new.partials.menu')
