@props([
  'name',
])

@if ($name === 'assistance')
  <svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="hero-icon">
      <path
        d="M11.9887 60.1196C8.79269 56.9237 6.25751 53.1295 4.52787 48.9538C2.79822 44.778 1.90798 40.3025 1.90798 35.7827C1.90798 31.2629 2.79822 26.7874 4.52787 22.6117C6.25751 18.4359 8.79269 14.6418 11.9887 11.4458C15.1846 8.24984 18.9788 5.71466 23.1545 3.98502C27.3303 2.25537 31.8058 1.36513 36.3256 1.36513C40.8453 1.36513 45.3209 2.25537 49.4966 3.98502C53.6723 5.71466 57.4665 8.24984 60.6625 11.4458"
        stroke="url(#paint0_linear_815_30641)"
        stroke-width="0.791209"
      />
      <path
        d="M60.6617 11.4458C63.8577 14.6418 66.3929 18.4359 68.1225 22.6117C69.8522 26.7874 70.7424 31.2629 70.7424 35.7827C70.7424 40.3025 69.8522 44.778 68.1225 48.9538C66.3929 53.1295 63.8577 56.9237 60.6617 60.1196C57.4658 63.3156 53.6716 65.8508 49.4959 67.5804C45.3201 69.3101 40.8446 70.2003 36.3248 70.2003C31.805 70.2003 27.3295 69.3101 23.1538 67.5804C18.9781 65.8508 15.1839 63.3156 11.9879 60.1196"
        stroke="url(#paint1_linear_815_30641)"
        stroke-width="0.791209"
      />
      <g filter="url(#filter0_bd_815_30641)">
        <circle cx="60.5458" cy="11.2558" r="1.58242" fill="#465AE8" />
        <circle cx="60.5458" cy="11.2558" r="2.58242" stroke="white" stroke-width="2" />
      </g>
      <g filter="url(#filter1_bd_815_30641)">
        <circle cx="12.1356" cy="59.9765" r="1.58242" fill="#465AE8" />
        <circle cx="12.1356" cy="59.9765" r="2.58242" stroke="white" stroke-width="2" />
      </g>
    </g>
    <path
      d="M38.9442 40.5245V39.1596V39.0796C38.9442 37.975 38.0488 37.0796 36.9442 37.0796H27.4204C26.0122 37.0796 25.3252 37.7309 25.3252 38.76C25.3252 39.9088 26.0122 40.4921 27.4204 40.5245H28.4681M38.9442 40.5245C38.9442 40.5245 38.0062 40.5245 36.849 40.5245H35.8014M38.9442 40.5245H40.5157M38.9442 40.5245C38.3657 40.5245 38.9442 40.5245 38.9442 39.4845V30.6445C38.9442 30.0701 39.4133 29.6045 39.9919 29.6045H42.6312C42.9495 29.6045 43.2505 29.7481 43.4493 29.9948L47.0956 34.5196C47.2442 34.704 47.3252 34.9332 47.3252 35.1693V39.4845C47.3252 40.0589 46.8562 40.5245 46.2776 40.5245M34.7538 40.0045C34.7538 41.4404 33.5812 42.6045 32.1347 42.6045C30.6883 42.6045 29.5157 41.4404 29.5157 40.0045C29.5157 38.5686 30.6883 37.4045 32.1347 37.4045C33.5812 37.4045 34.7538 38.5686 34.7538 40.0045ZM45.7538 40.0045C45.7538 41.4404 44.5812 42.6045 43.1347 42.6045C41.6883 42.6045 40.5157 41.4404 40.5157 40.0045C40.5157 38.5686 41.6883 37.4045 43.1347 37.4045C44.5812 37.4045 45.7538 38.5686 45.7538 40.0045Z"
      stroke="#72799F"
      stroke-width="1.5"
      stroke-linecap="round"
    />
    <defs>
      <filter
        id="filter0_bd_815_30641"
        x="52.9634"
        y="4.5085"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30641" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30641"
          result="effect2_dropShadow_815_30641"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30641"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bd_815_30641"
        x="4.55322"
        y="53.2292"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30641" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30641"
          result="effect2_dropShadow_815_30641"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30641"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_815_30641"
        x1="60.9422"
        y1="11.1661"
        x2="11.7089"
        y2="60.3994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_815_30641"
        x1="11.7082"
        y1="60.3994"
        x2="60.9415"
        y2="11.1661"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
    </defs>
  </svg>
@elseif ($name === 'consolidation')
  <svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="hero-icon">
      <path
        d="M11.9887 60.1196C8.79269 56.9237 6.25751 53.1295 4.52787 48.9538C2.79822 44.778 1.90798 40.3025 1.90798 35.7827C1.90798 31.2629 2.79822 26.7874 4.52787 22.6117C6.25751 18.4359 8.79269 14.6418 11.9887 11.4458C15.1846 8.24984 18.9788 5.71466 23.1545 3.98502C27.3303 2.25537 31.8058 1.36513 36.3256 1.36513C40.8453 1.36513 45.3209 2.25537 49.4966 3.98502C53.6723 5.71466 57.4665 8.24984 60.6625 11.4458"
        stroke="url(#paint0_linear_815_30641)"
        stroke-width="0.791209"
      />
      <path
        d="M60.6617 11.4458C63.8577 14.6418 66.3929 18.4359 68.1225 22.6117C69.8522 26.7874 70.7424 31.2629 70.7424 35.7827C70.7424 40.3025 69.8522 44.778 68.1225 48.9538C66.3929 53.1295 63.8577 56.9237 60.6617 60.1196C57.4658 63.3156 53.6716 65.8508 49.4959 67.5804C45.3201 69.3101 40.8446 70.2003 36.3248 70.2003C31.805 70.2003 27.3295 69.3101 23.1538 67.5804C18.9781 65.8508 15.1839 63.3156 11.9879 60.1196"
        stroke="url(#paint1_linear_815_30641)"
        stroke-width="0.791209"
      />
      <g filter="url(#filter0_bd_815_30641)">
        <circle cx="60.5458" cy="11.2558" r="1.58242" fill="#465AE8" />
        <circle cx="60.5458" cy="11.2558" r="2.58242" stroke="white" stroke-width="2" />
      </g>
      <g filter="url(#filter1_bd_815_30641)">
        <circle cx="12.1356" cy="59.9765" r="1.58242" fill="#465AE8" />
        <circle cx="12.1356" cy="59.9765" r="2.58242" stroke="white" stroke-width="2" />
      </g>
    </g>
    <path
      d="M47.4374 30.2279L37.4812 27.3835C37.3798 27.3545 37.2734 27.3471 37.169 27.362L27.313 28.7701C26.9562 28.8226 26.63 29.0013 26.3936 29.2737C26.1573 29.5462 26.0264 29.8944 26.0249 30.2551V40.9538C26.0264 41.3145 26.1573 41.6627 26.3936 41.9352C26.63 42.2076 26.9562 42.3863 27.313 42.4388L37.169 43.8451C37.204 43.8506 37.2394 43.8537 37.2749 43.8545C37.3447 43.8544 37.4141 43.8446 37.4812 43.8254L47.4374 40.981C47.7497 40.8906 48.0243 40.7016 48.2203 40.4422C48.4163 40.1829 48.5232 39.867 48.5249 39.542V31.667C48.5232 31.3419 48.4163 31.0261 48.2203 30.7667C48.0243 30.5073 47.7497 30.3183 47.4374 30.2279ZM34.2749 34.8545H32.7749V29.5051L36.5249 28.9688V42.2401L32.7749 41.7038V36.3545H34.2749C34.4738 36.3545 34.6646 36.2754 34.8052 36.1348C34.9459 35.9941 35.0249 35.8034 35.0249 35.6045C35.0249 35.4055 34.9459 35.2148 34.8052 35.0741C34.6646 34.9335 34.4738 34.8545 34.2749 34.8545ZM27.5249 30.2551L31.2749 29.7188V34.8545H29.7749C29.576 34.8545 29.3852 34.9335 29.2446 35.0741C29.1039 35.2148 29.0249 35.4055 29.0249 35.6045C29.0249 35.8034 29.1039 35.9941 29.2446 36.1348C29.3852 36.2754 29.576 36.3545 29.7749 36.3545H31.2749V41.4901L27.5249 40.9538V30.2551ZM38.0249 42.1098V29.0991L47.0249 31.667V39.542L38.0249 42.1098Z"
      fill="#72799F"
    />
    <defs>
      <filter
        id="filter0_bd_815_30651"
        x="53.9131"
        y="4.5085"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30651" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30651"
          result="effect2_dropShadow_815_30651"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30651"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bd_815_30651"
        x="5.50391"
        y="53.2292"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30651" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30651"
          result="effect2_dropShadow_815_30651"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30651"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_815_30651"
        x1="61.8929"
        y1="11.1661"
        x2="12.6596"
        y2="60.3994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_815_30651"
        x1="12.6589"
        y1="60.3994"
        x2="61.8921"
        y2="11.1661"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
    </defs>
  </svg>
@elseif ($name === 'inventory')
  <svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="hero-icon">
      <path
        d="M11.9887 60.1196C8.79269 56.9237 6.25751 53.1295 4.52787 48.9538C2.79822 44.778 1.90798 40.3025 1.90798 35.7827C1.90798 31.2629 2.79822 26.7874 4.52787 22.6117C6.25751 18.4359 8.79269 14.6418 11.9887 11.4458C15.1846 8.24984 18.9788 5.71466 23.1545 3.98502C27.3303 2.25537 31.8058 1.36513 36.3256 1.36513C40.8453 1.36513 45.3209 2.25537 49.4966 3.98502C53.6723 5.71466 57.4665 8.24984 60.6625 11.4458"
        stroke="url(#paint0_linear_815_30641)"
        stroke-width="0.791209"
      />
      <path
        d="M60.6617 11.4458C63.8577 14.6418 66.3929 18.4359 68.1225 22.6117C69.8522 26.7874 70.7424 31.2629 70.7424 35.7827C70.7424 40.3025 69.8522 44.778 68.1225 48.9538C66.3929 53.1295 63.8577 56.9237 60.6617 60.1196C57.4658 63.3156 53.6716 65.8508 49.4959 67.5804C45.3201 69.3101 40.8446 70.2003 36.3248 70.2003C31.805 70.2003 27.3295 69.3101 23.1538 67.5804C18.9781 65.8508 15.1839 63.3156 11.9879 60.1196"
        stroke="url(#paint1_linear_815_30641)"
        stroke-width="0.791209"
      />
      <g filter="url(#filter0_bd_815_30641)">
        <circle cx="60.5458" cy="11.2558" r="1.58242" fill="#465AE8" />
        <circle cx="60.5458" cy="11.2558" r="2.58242" stroke="white" stroke-width="2" />
      </g>
      <g filter="url(#filter1_bd_815_30641)">
        <circle cx="12.1356" cy="59.9765" r="1.58242" fill="#465AE8" />
        <circle cx="12.1356" cy="59.9765" r="2.58242" stroke="white" stroke-width="2" />
      </g>
    </g>
    <path
      d="M28.541 38.1045V35.7752C28.541 34.6437 29.4364 33.7264 30.541 33.7264M28.541 38.1045C28.541 39.236 29.4364 40.1533 30.541 40.1533H42.541C43.6456 40.1533 44.541 39.236 44.541 38.1045M28.541 38.1045V41.2386C28.541 41.993 29.138 42.6045 29.8743 42.6045H31.2077C31.9441 42.6045 32.541 41.993 32.541 41.2386V40.1533M44.541 38.1045V35.7752C44.541 34.6437 43.6456 33.7264 42.541 33.7264H30.541M44.541 38.1045V41.2386C44.541 41.993 43.9441 42.6045 43.2077 42.6045H41.8743C41.138 42.6045 40.541 41.993 40.541 41.2386V40.1533M30.541 33.7264L31.6104 29.6184C31.7661 29.0205 32.2947 28.6045 32.8987 28.6045H40.2467C40.8206 28.6045 41.3301 28.9807 41.5116 29.5384L42.8743 33.7264M30.8743 36.7996H33.2077M39.8743 36.7996H42.2077"
      stroke="#72799F"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <defs>
      <filter
        id="filter0_bd_815_30631"
        x="53.1792"
        y="4.5085"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30631" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30631"
          result="effect2_dropShadow_815_30631"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30631"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bd_815_30631"
        x="4.77002"
        y="53.2292"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30631" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30631"
          result="effect2_dropShadow_815_30631"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30631"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_815_30631"
        x1="61.158"
        y1="11.1661"
        x2="11.9247"
        y2="60.3994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_815_30631"
        x1="11.925"
        y1="60.3994"
        x2="61.1583"
        y2="11.1661"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
    </defs>
  </svg>
@elseif ($name === 'shipping')
  <svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="hero-icon">
      <path
        d="M11.9887 60.1196C8.79269 56.9237 6.25751 53.1295 4.52787 48.9538C2.79822 44.778 1.90798 40.3025 1.90798 35.7827C1.90798 31.2629 2.79822 26.7874 4.52787 22.6117C6.25751 18.4359 8.79269 14.6418 11.9887 11.4458C15.1846 8.24984 18.9788 5.71466 23.1545 3.98502C27.3303 2.25537 31.8058 1.36513 36.3256 1.36513C40.8453 1.36513 45.3209 2.25537 49.4966 3.98502C53.6723 5.71466 57.4665 8.24984 60.6625 11.4458"
        stroke="url(#paint0_linear_815_30641)"
        stroke-width="0.791209"
      />
      <path
        d="M60.6617 11.4458C63.8577 14.6418 66.3929 18.4359 68.1225 22.6117C69.8522 26.7874 70.7424 31.2629 70.7424 35.7827C70.7424 40.3025 69.8522 44.778 68.1225 48.9538C66.3929 53.1295 63.8577 56.9237 60.6617 60.1196C57.4658 63.3156 53.6716 65.8508 49.4959 67.5804C45.3201 69.3101 40.8446 70.2003 36.3248 70.2003C31.805 70.2003 27.3295 69.3101 23.1538 67.5804C18.9781 65.8508 15.1839 63.3156 11.9879 60.1196"
        stroke="url(#paint1_linear_815_30641)"
        stroke-width="0.791209"
      />
      <g filter="url(#filter0_bd_815_30641)">
        <circle cx="60.5458" cy="11.2558" r="1.58242" fill="#465AE8" />
        <circle cx="60.5458" cy="11.2558" r="2.58242" stroke="white" stroke-width="2" />
      </g>
      <g filter="url(#filter1_bd_815_30641)">
        <circle cx="12.1356" cy="59.9765" r="1.58242" fill="#465AE8" />
        <circle cx="12.1356" cy="59.9765" r="2.58242" stroke="white" stroke-width="2" />
      </g>
    </g>
    <path
      d="M28.7246 32.6045C28.3104 32.6045 27.9746 32.9403 27.9746 33.3545C27.9746 33.7687 28.3104 34.1045 28.7246 34.1045V32.6045ZM44.4746 34.1045C44.8888 34.1045 45.2246 33.7687 45.2246 33.3545C45.2246 32.9403 44.8888 32.6045 44.4746 32.6045V34.1045ZM28.8191 37.6669C28.4049 37.6669 28.0691 38.0027 28.0691 38.4169C28.0691 38.8311 28.4049 39.1669 28.8191 39.1669V37.6669ZM44.5691 39.1669C44.9833 39.1669 45.3191 38.8311 45.3191 38.4169C45.3191 38.0027 44.9833 37.6669 44.5691 37.6669V39.1669ZM44.8496 35.6045C44.8496 40.1608 41.156 43.8545 36.5996 43.8545V45.3545C41.9844 45.3545 46.3496 40.9893 46.3496 35.6045H44.8496ZM36.5996 43.8545C32.0433 43.8545 28.3496 40.1608 28.3496 35.6045H26.8496C26.8496 40.9893 31.2148 45.3545 36.5996 45.3545V43.8545ZM28.3496 35.6045C28.3496 31.0481 32.0433 27.3545 36.5996 27.3545V25.8545C31.2148 25.8545 26.8496 30.2197 26.8496 35.6045H28.3496ZM36.5996 27.3545C41.156 27.3545 44.8496 31.0481 44.8496 35.6045H46.3496C46.3496 30.2197 41.9844 25.8545 36.5996 25.8545V27.3545ZM36.5996 43.8545C36.3041 43.8545 35.9635 43.7208 35.5883 43.358C35.2094 42.9918 34.834 42.4256 34.5025 41.6678C33.8404 40.1545 33.4121 38.0114 33.4121 35.6045H31.9121C31.9121 38.1682 32.3653 40.5251 33.1283 42.2691C33.5093 43.14 33.9823 43.8918 34.5457 44.4364C35.1126 44.9845 35.8078 45.3545 36.5996 45.3545V43.8545ZM33.4121 35.6045C33.4121 33.1976 33.8404 31.0545 34.5025 29.5411C34.834 28.7834 35.2094 28.2172 35.5883 27.851C35.9635 27.4882 36.3041 27.3545 36.5996 27.3545V25.8545C35.8078 25.8545 35.1126 26.2244 34.5457 26.7725C33.9823 27.3172 33.5093 28.069 33.1283 28.9399C32.3653 30.6839 31.9121 33.0408 31.9121 35.6045H33.4121ZM36.5996 45.3545C37.3914 45.3545 38.0866 44.9845 38.6536 44.4364C39.2169 43.8918 39.6899 43.14 40.071 42.2691C40.834 40.5251 41.2871 38.1682 41.2871 35.6045H39.7871C39.7871 38.0114 39.3588 40.1545 38.6967 41.6678C38.3652 42.4256 37.9898 42.9918 37.611 43.358C37.2358 43.7208 36.8951 43.8545 36.5996 43.8545V45.3545ZM41.2871 35.6045C41.2871 33.0408 40.834 30.6839 40.071 28.9399C39.6899 28.069 39.2169 27.3172 38.6536 26.7725C38.0866 26.2244 37.3914 25.8545 36.5996 25.8545V27.3545C36.8951 27.3545 37.2358 27.4882 37.611 27.851C37.9898 28.2172 38.3652 28.7834 38.6967 29.5411C39.3588 31.0545 39.7871 33.1976 39.7871 35.6045H41.2871ZM28.7246 34.1045H44.4746V32.6045H28.7246V34.1045ZM28.8191 39.1669H44.5691V37.6669H28.8191V39.1669Z"
      fill="#72799F"
    />
    <defs>
      <filter
        id="filter0_bd_815_30661"
        x="53.2378"
        y="4.5085"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30661" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30661"
          result="effect2_dropShadow_815_30661"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30661"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bd_815_30661"
        x="4.82764"
        y="53.2292"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30661" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30661"
          result="effect2_dropShadow_815_30661"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30661"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_815_30661"
        x1="61.2166"
        y1="11.1661"
        x2="11.9833"
        y2="60.3994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_815_30661"
        x1="11.9826"
        y1="60.3994"
        x2="61.2159"
        y2="11.1661"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
    </defs>
  </svg>
@else
  <svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="hero-icon">
      <path
        d="M11.9887 60.1196C8.79269 56.9237 6.25751 53.1295 4.52787 48.9538C2.79822 44.778 1.90798 40.3025 1.90798 35.7827C1.90798 31.2629 2.79822 26.7874 4.52787 22.6117C6.25751 18.4359 8.79269 14.6418 11.9887 11.4458C15.1846 8.24984 18.9788 5.71466 23.1545 3.98502C27.3303 2.25537 31.8058 1.36513 36.3256 1.36513C40.8453 1.36513 45.3209 2.25537 49.4966 3.98502C53.6723 5.71466 57.4665 8.24984 60.6625 11.4458"
        stroke="url(#paint0_linear_815_30641)"
        stroke-width="0.791209"
      />
      <path
        d="M60.6617 11.4458C63.8577 14.6418 66.3929 18.4359 68.1225 22.6117C69.8522 26.7874 70.7424 31.2629 70.7424 35.7827C70.7424 40.3025 69.8522 44.778 68.1225 48.9538C66.3929 53.1295 63.8577 56.9237 60.6617 60.1196C57.4658 63.3156 53.6716 65.8508 49.4959 67.5804C45.3201 69.3101 40.8446 70.2003 36.3248 70.2003C31.805 70.2003 27.3295 69.3101 23.1538 67.5804C18.9781 65.8508 15.1839 63.3156 11.9879 60.1196"
        stroke="url(#paint1_linear_815_30641)"
        stroke-width="0.791209"
      />
      <g filter="url(#filter0_bd_815_30641)">
        <circle cx="60.5458" cy="11.2558" r="1.58242" fill="#465AE8" />
        <circle cx="60.5458" cy="11.2558" r="2.58242" stroke="white" stroke-width="2" />
      </g>
      <g filter="url(#filter1_bd_815_30641)">
        <circle cx="12.1356" cy="59.9765" r="1.58242" fill="#465AE8" />
        <circle cx="12.1356" cy="59.9765" r="2.58242" stroke="white" stroke-width="2" />
      </g>
    </g>
    <path
      d="M26.9595 45.2044L26.9599 41.604C26.9601 39.6159 28.5718 38.0044 30.5599 38.0044H37.7593C41.5591 38.0044 41.5591 40.6045 41.5591 40.6045M40.7595 44.0044L41.9595 45.2044L46.1595 41.0044M38.9595 29.6044C38.9595 31.5926 37.3477 33.2044 35.3595 33.2044C33.3712 33.2044 31.7595 31.5926 31.7595 29.6044C31.7595 27.6162 33.3712 26.0044 35.3595 26.0044C37.3477 26.0044 38.9595 27.6162 38.9595 29.6044Z"
      stroke="#72799F"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <defs>
      <filter
        id="filter0_bd_815_30622"
        x="53.1978"
        y="4.5085"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30622" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30622"
          result="effect2_dropShadow_815_30622"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30622"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bd_815_30622"
        x="4.7876"
        y="53.2292"
        width="15.165"
        height="15.3296"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.58242" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_815_30622" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_815_30622"
          result="effect2_dropShadow_815_30622"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_815_30622"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_815_30622"
        x1="61.1766"
        y1="11.1661"
        x2="11.9433"
        y2="60.3994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_815_30622"
        x1="11.9426"
        y1="60.3994"
        x2="61.1758"
        y2="11.1661"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#99A4F5" />
        <stop offset="1" stop-color="#C3C8EB" stop-opacity="0.1" />
      </linearGradient>
    </defs>
  </svg>
@endif
