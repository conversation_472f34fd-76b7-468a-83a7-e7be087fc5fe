@php
  $locale = app()->getLocale();

  $items = [
    [
      'name' => 'slide_1',
      'image' => [
        'en' => 'main_slider/en/slide_1.png',
        'pl' => 'main_slider/pl/slide_1.png',
      ],
      'image_mobile' => [
        'en' => 'main_slider/en/mobile_slide_1.png',
        'pl' => 'main_slider/pl/mobile_slide_1.png',
      ],
    ],
    [
      'name' => 'slide_2',
      'image' => [
        'en' => 'main_slider/en/slide_2.png',
        'pl' => 'main_slider/pl/slide_2.png',
      ],
      'image_mobile' => [
        'en' => 'main_slider/en/mobile_slide_2.png',
        'pl' => 'main_slider/pl/mobile_slide_2.png',
      ],
    ],
    [
      'name' => 'slide_3',
      'image' => [
        'en' => 'main_slider/en/slide_3.png',
        'pl' => 'main_slider/pl/slide_3.png',
      ],
      'image_mobile' => [
        'en' => 'main_slider/en/mobile_slide_3.png',
        'pl' => 'main_slider/pl/mobile_slide_3.png',
      ],
    ],
    [
      'name' => 'slide_4',
      'image' => [
        'en' => 'main_slider/en/slide_4.png',
        'pl' => 'main_slider/pl/slide_4.png',
      ],
      'image_mobile' => [
        'en' => 'main_slider/en/mobile_slide_4.png',
        'pl' => 'main_slider/pl/mobile_slide_4.png',
      ],
    ],
    [
      'name' => 'slide_5',
      'image' => [
        'en' => 'main_slider/en/slide_5.png',
        'pl' => 'main_slider/pl/slide_5.png',
      ],
      'image_mobile' => [
        'en' => 'main_slider/en/mobile_slide_5.png',
        'pl' => 'main_slider/pl/mobile_slide_5.png',
      ],
    ],
  ];
@endphp

<section class="main-slider">
  <div id="main-slider" class="slider swiper-container">
    <div class="main-slider__wrapper swiper-wrapper">
      @foreach ($items as $item)
        <div class="main-slider__item swiper-slide">
          <picture>
            <source
              media="(max-width: 768px)"
              srcset="{{ Vite::image($item['image_mobile'][$locale]) }}"
            />
            <img
              class="main-slider__image"
              src="{{ Vite::image($item['image'][$locale]) }}"
              alt="{{ __('web/landing.' . $item['name']) }}"
            />
          </picture>
        </div>
      @endforeach
    </div>
    <div class="swiper-pagination"></div>
    <button
      class="main-slider__nav-button main-slider__nav-button--left"
      aria-label="{{ __('web/landing.prev_slide') }}"
    >
      <x-icons.arrow-left-icon />
    </button>
    <button
      class="main-slider__nav-button main-slider__nav-button--right"
      aria-label="{{ __('web/landing.next_slide') }}"
    >
      <x-icons.arrow-right-icon />
    </button>
  </div>
</section>

<div class="cursor">
  <img width="30" height="30" src="{{ Vite::image('cursor_swipe.svg') }}" alt="Cursor swipe" />
</div>
