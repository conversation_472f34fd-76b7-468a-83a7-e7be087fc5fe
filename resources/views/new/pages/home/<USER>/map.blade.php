<section id="map" class="map">
  <div class="map__content">
    <h2>{{ __('web/landing.map_title') }}</h2>
    <p>{{ __('web/landing.map_desc') }}</p>
  </div>
  <div
    x-cloak
    class="map__image"
    :style="'background-image: url({{ Vite::image('map/map.svg') }})'"
    x-load
    x-data="mapComponent"
    @resize.window="checkIfMobile"
  >
    <template x-for="marker in markers" :key="marker.location">
      <div
        class="map__marker"
        :class="{'opacity-50': activeMarker && activeMarker !== marker.location, 'opacity-100': activeMarker === marker.location || !activeMarker}"
        :style="{ top: marker.top + '%', left: marker.left + '%' }"
        @mouseenter="!isMobile && showTooltipEvent(marker, $event)"
        @mouseleave="!isMobile && hideTooltipEvent()"
      >
        <img src="{{ Vite::image('map/marker.svg') }}" :alt="marker.location" />
      </div>
    </template>
    <div class="map__tooltip" x-show="showTooltip" :style="{ top: y + 'px', left: x + 'px' }">
      <div class="map__tooltip-title">
        <template x-if="tooltip.countryFlag">
          <img :src="tooltip.countryFlag" :alt="tooltip.location" />
        </template>
        <span x-text="tooltip.location"></span>
      </div>
      <template x-if="tooltip.details?.length">
        <ul>
          <template x-for="detail in tooltip.details" :key="detail.location">
            <li>
              <template x-if="detail.countryFlag">
                <img :src="detail.countryFlag" :alt="detail.location" />
              </template>
              <span x-text="detail.location"></span>
            </li>
          </template>
        </ul>
      </template>
    </div>
    <div class="map__mobile-list-button">
      <button class="btn btn-primary" @click="openModal = true">
        {{ __('web/landing.map_port_list') }}
      </button>
    </div>
    <div x-show="openModal" class="map__modal-overlay" @click.away="openModal = false" x-cloak>
      <div class="map__modal-content">
        <div class="map__modal-header">
          <span>{{ __('web/landing.map_port_list') }}</span>
          <button class="map__modal-close" @click="openModal = false">
            <x-icons.close-icon />
          </button>
        </div>
        <div class="map__modal-scrollable">
          <div class="map__modal-title map__modal-title--without-icon">
            <span>{{ __('web/landing.location.north_america') }}</span>
          </div>
          <template x-for="marker in markers" :key="marker.location">
            <div>
              <div
                class="map__modal-title"
                :class="{'map__modal-title--without-icon': !marker.countryFlag}"
              >
                <template x-if="marker.countryFlag">
                  <img :src="marker.countryFlag" :alt="marker.location" />
                </template>
                <span x-text="marker.location"></span>
              </div>
              <template x-if="marker.details?.length">
                <ul class="map__modal-list">
                  <template x-for="detail in marker.details" :key="detail.location">
                    <li>
                      <template x-if="detail.countryFlag">
                        <img :src="detail.countryFlag" :alt="detail.location" />
                      </template>
                      <span x-text="detail.location"></span>
                    </li>
                  </template>
                </ul>
              </template>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</section>
