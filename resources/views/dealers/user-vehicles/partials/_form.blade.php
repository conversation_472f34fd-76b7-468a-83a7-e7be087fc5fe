@php
    /** @var \App\Models\Presenters\UserVehiclePresenter|null $userVehicle */
@endphp
{!!
   Former::select('status', __('user-vehicles.form.status'))
        ->options($statuses)
!!}
{!! Former::hidden('port_id') !!}
{!!
    Former::select('port_id', __('user-vehicles.form.port_id'))
        ->options($ports)
        ->placeholder(__('user-vehicles.form.port_id_placeholder'))
        ->addClass('select2')
!!}
{!!
    Former::number('year', __('user-vehicles.form.year'))
        ->min(1900)
        ->max((int) date('Y') + 1)
        ->value((int) date('Y') - 5)
        ->placeholder(__('user-vehicles.form.year_placeholder'))
        ->required()
!!}
{!!
    Former::text('brand', __('user-vehicles.form.brand'))
        ->placeholder(__('user-vehicles.form.brand_placeholder'))
        ->required()
!!}
{!!
    Former::text('model', __('user-vehicles.form.model'))
        ->placeholder(__('user-vehicles.form.model_placeholder'))
        ->required()
!!}
{!!
    Former::text('vin_number', __('user-vehicles.form.vin_number'))
        ->placeholder(__('user-vehicles.form.vin_number_placeholder'))
        ->required()
!!}
{!! Former::hidden('keys') !!}
{!!
    Former::select('keys', __('user-vehicles.form.keys'))
        ->options($keys)
        ->placeholder(__('user-vehicles.form.keys_placeholder'))
!!}
{!!
    Former::text('container_number', __('user-vehicles.form.container_number'))
        ->placeholder(__('user-vehicles.form.container_number_placeholder'))
!!}
{!!
    Former::text('eta', __('user-vehicles.form.eta'))
        ->placeholder(__('user-vehicles.form.eta_placeholder'))
        ->addGroupClass('date')
        ->onGroupDataProvide('datepicker')
        ->append('<i class="fa fa-calendar fa-fw"></i>')
        ->forceValue(isset($userVehicle) ? $userVehicle->eta?->format('d.m.Y') : '')
!!}
{!! Former::hidden('vehicle_shipping_line_id') !!}
{!!
     Former::select('vehicle_shipping_line_id', __('user-vehicles.form.vehicle_shipping_line_id'))
        ->options($vehicleShippingLines)
        ->placeholder(__('user-vehicles.form.vehicle_shipping_line_id_placeholder'))
        ->addClass('select2')
!!}
