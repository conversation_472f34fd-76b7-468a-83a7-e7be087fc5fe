@php
    /** @var \App\Models\Presenters\UserVehiclePresenter $userVehicle */
@endphp
@extends('layouts.base')

@section('content')
    <div id="page_services" class="bg-gray">
        <section class="orders_show narrow-padded">
            <nav class="navbar navbar-default navbar-bordered">
                <div class="container">
                    <ul class="nav navbar-nav">
                        <li class="active full-width">
                            <a href="{{ route('dealers.index') }}">
                                {{ __('common.cancel_and_return_to_list') }}
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="container">
                <div class="box-bordered border-bottom">
                    <div class="box-body">
                        <h2 class="text-headline text-headline--red"
                            style="margin-top: 0 !important; margin-bottom: 30px;">
                            {{ $userVehicle->vehicleDescription() }}
                        </h2>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.vin_number') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ $userVehicle->vin_number }}
                            </div>
                        </div>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.status') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ \App\Project\Vehicle\Enums\VehicleTabs::humanReadableForDealer($userVehicle->status) }}
                            </div>
                        </div>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.keys') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ $userVehicle->keys ? \App\Project\Vehicle\Enums\VehicleKeys::humanReadable($userVehicle->keys) : 'N/A' }}
                            </div>
                        </div>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.eta') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ $userVehicle->eta?->format('d.m.Y') ?? 'N/A' }}
                            </div>
                        </div>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.port') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ $userVehicle->port?->displayFormat() ?? 'N/A' }}
                            </div>
                        </div>

                        <div class="labeled-content">
                            <p class="labeled-content__label">
                                {{ __('user-vehicles.show.vehicle_shipping_line') }}
                            </p>
                            <div class="labeled-content__content">
                                {{ $userVehicle->shippingLine?->name ?? 'N/A' }}
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="{{ route('dealers.user-vehicles.edit', $userVehicle) }}"
                               class="btn btn-blue btn-lg">
                                <strong>{{ __('common.edit') }}</strong>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container" id="add-photos">
                <div class="box-bordered spaced no-border">
                    <div class="box-body" id="vue" style="padding: 20px 0 40px;">
                        <p class="head-blue">
                            {{ __('user-vehicles.show.images_collection') }}
                        </p>
                        <div>
                            @php
                                $routeParams = [\App\Project\Storage\CloudStorageModelsMapper::alias($userVehicle->model()), $userVehicle->getKey(), 'images_collection'];
                            @endphp
                            <image-manager
                                    :index-images-url="'{{ route('images.index', $routeParams) }}'"
                                    :add-image-url="'{{ route('images.store', $routeParams) }}'"
                                    :delete-image-url="'{{ route('files.remove', $routeParams) }}'"
                                    :order-images-url="'{{ route('images.order', $routeParams) }}'"
                                    :input-text="'{{ __('vehicles.show.select_photos') }}'"
                                    :user-mode="true"
                            />
                        </div>
                        <p class="head-blue">
                            {{ __('user-vehicles.show.images_terminal') }}
                        </p>
                        <div>
                            @php
                                $routeParams = [\App\Project\Storage\CloudStorageModelsMapper::alias($userVehicle->model()), $userVehicle->getKey(), 'images_terminal'];
                            @endphp
                            <image-manager
                                :index-images-url="'{{ route('images.index', $routeParams) }}'"
                                :add-image-url="'{{ route('images.store', $routeParams) }}'"
                                :delete-image-url="'{{ route('files.remove', $routeParams) }}'"
                                :order-images-url="'{{ route('images.order', $routeParams) }}'"
                                :input-text="'{{ __('vehicles.show.select_photos') }}'"
                                :user-mode="true"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
