@extends('layouts.base')

@section('content')
    <div class="bg-gray">
        <section class="narrow-padded">
            <div class="container">
                <div class="page-actions">
                    <div class="page-actions__btns">
                        <a href="{{ route('dealers.index') }}" class="btn btn-red">
                            {{ __('dealer-clients.index.dealer_account') }}
                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                        </a>
                        <a href="" class="btn btn-blue add-client" data-toggle="modal" data-target="#add-client-modal">
                            {{ __('dealer-clients.index.add_client') }}
                        </a>
                    </div>

                    <div class="page-actions__filters">
                        {!! Former::openInline()->method('GET')->id('filters')->action(route('dealer-clients.index')) !!}
                        {!! Former::text('search')->placeholder(__('common.tables.search'))->prepend('<i class="fa fa-search" aria-hidden="true"></i>') !!}
                        {!! Former::close() !!}
                    </div>
                </div>
                <div class="no-padding">
                    <div class="table-ajax">
                        @include('dealers.clients.partials.table-content')
                    </div>
                </div>
            </div>
        </section>
    </div>

    @include('dealers.clients.partials._add-client-modal')
    @include('dealers.clients.partials._edit-client-modal')
@endsection

@section('scripts')
    <script type="module">
        $(function () {
            var $editModal = $('#edit-client-modal');
            var $editForm = $editModal.find('form');

            $(document).on('click', '.edit-client', function (e) {
                e.preventDefault();
                $.ajax({
                    url: $(this).attr('href'),
                    method: 'GET',
                }).done(function (response) {
                    $editForm.attr('action', response.update_url);
                    $editForm.find('[name="email"]').val(response.email);
                    $editForm.find('[name="first_name"]').val(response.first_name);
                    $editForm.find('[name="last_name"]').val(response.last_name);
                    $editModal.modal('show');
                }).fail(function () {
                    alert('{{ __('common.alerts.ajax_error') }}');
                });
            });

            $(document).on('click', '.delete-client', function (e) {
                e.preventDefault();
                var $this = $(this);
                swal({
                    title: '{{ __('common.alerts.delete') }}',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: '{{ __('common.yes') }}',
                    cancelButtonText: '{{ __('common.cancel') }}',
                    closeOnConfirm: false
                }, function (isConfirm) {
                    if (!isConfirm) {
                        return;
                    }
                    $.ajax({
                        url: $this.attr('href'),
                        method: 'DELETE',
                    }).done(function () {
                        $('.table-ajax')[0].dispatchEvent(new Event('refresh'));
                    }).fail(function () {
                        alert('{{ __('common.alerts.ajax_error') }}');
                    }).always(function () {
                        swal.close();
                    });
                });
            });
        });
    </script>
@endsection
