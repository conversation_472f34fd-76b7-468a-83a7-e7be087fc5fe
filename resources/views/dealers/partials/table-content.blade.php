@php
    /** @var \App\Models\Presenters\BaseVehiclePresenter[]|\App\Project\Core\Presenters\PaginatorPresenter $vehicles */
@endphp
@if($vehicles->count())
    <table class="table table-white">
        <thead>
        <tr>
            <th></th>
            <th>ID</th>
            <th>{{ __('vehicles.table.header.vehicle_photos') }}</th>
            <th>{{ __('vehicles.table.header.terminal') }}</th>
            <th>{{ __('vehicles.table.header.status') }}</th>
            <th>{{ __('vehicles.table.header.vehicle') }}</th>
            <th>VIN</th>
            <th>{{ __('vehicles.table.header.container_number') }}</th>
            <th>ETA</th>
            <th>{{ __('vehicles.table.header.client') }}</th>
        </tr>
        </thead>
        <tbody>
        @foreach($vehicles as $vehicle)
            <tr @if($vehicle instanceof \App\Models\Presenters\UserVehiclePresenter) class="warning" @endif>
                <td class="actions">
                    <div class="btn-group">
                        <button
                            class="dropdown-toggle btn btn-link"
                            type="button" data-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false"
                        >
                            <i class="fa fa-bars" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="{{ $vehicle->dashboardShowUrl() }}">
                                    {{ __('vehicles.table_actions.click_for_details') }}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="{{ $vehicle->shareInfoUrl() }}"
                                    class="get-shareable-url"
                                >
                                    {{ __('dealers.table.actions.get_shareable_url') }}
                                </a>
                            </li>
                            @if ($vehicle->editUrl())
                                <li>
                                    <a href="{{ $vehicle->editUrl() }}">
                                        {{ __('common.edit') }}
                                    </a>
                                </li>
                            @endif
                            @if ($vehicle instanceof \App\Models\Presenters\UserVehiclePresenter)
                                <li>
                                    <a href="{{ $vehicle->dashboardShowUrl() }}#add-photos">
                                        {{ __('dealers.table.actions.add_photos') }}
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>
                </td>
                <td>
                    {{ $vehicle->displayId() }}
                </td>
                <td class="narrow">
                    @include('vehicles.partials._table_thumbnail')
                </td>
                <td>
                    {{ $vehicle->terminalDisplayFormat() }}
                </td>
                <td>
                    {{ $vehicle->statusDisplayFormat() }}
                </td>
                <td>
                    @include('vehicles.partials._vehicle-description')
                </td>
                <td>
                    <a tabindex="0" data-toggle="popover" data-placement="top"
                       data-content="{{ $vehicle->vin_number }}">
                        {{ $vehicle->vinShort()}}
                    </a>
                </td>
                <td>
                    <a href="" data-track-container="{{ $vehicle->container_number }}">
                        {{ $vehicle->container_number }}
                    </a>
                </td>
                <td>{{ $vehicle->eta ? $vehicle->eta->format('d.m.Y') : '' }}</td>
                <td>
                    {{ $vehicle->dealerClient?->displayFormat() }}
                    @if ($vehicle->shared_to_client)
                        <a tabindex="0" role="button" data-toggle="tooltip" data-placement="top"
                           title="{{ __('dealers.table.actions.marked_as_shared_to_client') }}">
                            <i class="fa fa-check-circle-o text-success"
                               style="font-size: 20px; margin-left: 5px; vertical-align: middle;"
                               aria-hidden="true"></i>
                        </a>
                    @endif
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
@else
    @include('partials._no_results')
@endif
@if($vehicles->hasPages())
    <div class="text-center">
        {!! $vehicles->render() !!}
    </div>
@endif
