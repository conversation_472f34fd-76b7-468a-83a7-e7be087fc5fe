@php
    /** @var \App\Models\UserBusinessCard|null $businessCard */
    $businessCard = auth()->user()?->businessCard;
@endphp
<div class="modal fade form-modal" id="business-card-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <strong>{{ __('dealers.business_card_modal.title') }}</strong>
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning text-justify" role="alert">
                    {!! __('dealers.business_card_modal.warning') !!}
                </div>
                {!! Former::openForFiles()->action(route('business-card.store'))->method('PUT') !!}

                {!! Former::file('desktop_image', __('dealers.business_card_modal.form.desktop_image'))
                        ->accept('image/*, image/heic, image/heif')
                !!}
                @if($businessCard?->desktop_image)
                    <div style="margin-bottom: 15px;" class="image-actions">
                        <a href="{{ \App\Project\Storage\CloudStorage::viewFileRoute($businessCard->desktop_image, $businessCard, 'desktop_image') }}" class="btn btn-blue" target="_blank">
                            {{ __('dealers.business_card_modal.form.current_file') }}
                        </a>
                        <a href="{{ route('files.remove', ['modelAlias' => \App\Project\Storage\CloudStorageModelsMapper::alias($businessCard), 'id' => $businessCard->getKey(), 'attributeName' => 'desktop_image']) }}" class="btn btn-red delete-image">
                            {{ __('dealers.business_card_modal.form.delete_image') }}
                        </a>
                    </div>
                @endif

                {!! Former::file('mobile_image', __('dealers.business_card_modal.form.mobile_image'))
                    ->accept('image/*, image/heic, image/heif')
                !!}
                @if($businessCard?->mobile_image)
                    <div style="margin-bottom: 15px;" class="image-actions">
                        <a href="{{ \App\Project\Storage\CloudStorage::viewFileRoute($businessCard->mobile_image, $businessCard, 'mobile_image') }}" class="btn btn-blue" target="_blank">
                            {{ __('dealers.business_card_modal.form.current_file') }}
                        </a>
                        <a href="{{ route('files.remove', ['modelAlias' => \App\Project\Storage\CloudStorageModelsMapper::alias($businessCard), 'id' => $businessCard->getKey(), 'attributeName' => 'mobile_image']) }}" class="btn btn-red delete-image">
                            {{ __('dealers.business_card_modal.form.delete_image') }}
                        </a>
                    </div>
                @endif

                <button type="submit" class="btn btn-red center-block">
                    {{ __('common.forms.submit') }}
                </button>
                {!! Former::close() !!}
            </div>
        </div>
    </div>
</div>
