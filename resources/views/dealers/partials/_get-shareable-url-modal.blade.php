<div class="modal fade form-modal" id="get-shareable-url-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <strong>{{ __('dealers.get_shareable_url_modal.title') }}</strong>
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <strong>{{ __('dealers.get_shareable_url_modal.warning') }}</strong>
                </div>
                {!! Former::open()->method('PUT') !!}
                <div style="display: flex; align-items: baseline;">
                    {!!
                        Former::text('shareable_url', '')
                            ->id('shareable_url')
                            ->addGroupClass('width-100')
                            ->style('width: 100%;')
                            ->dataActiveText(__('dealers.get_shareable_url_modal.form.shareable_url_active_help'))
                            ->dataInactiveText(__('dealers.get_shareable_url_modal.form.shareable_url_inactive_help'))
                            ->readonly()
                    !!}
                    <button
                        type="button"
                        class="btn btn-blue copy-btn"
                        data-toggle="tooltip"
                        data-trigger="manual"
                        title="{{ __('common.copied') }}"
                        data-clipboard-target="#shareable_url"
                        style="margin-left: 5px;"
                    >
                        {{ __('common.copy') }}
                    </button>
                    <button
                        type="button"
                        class="btn btn-red"
                        id="deactivate-shareable-url-btn"
                        style="margin-left: 5px;"
                        data-deactivate-text="{{ __('dealers.get_shareable_url_modal.deactivate_shareable_link') }}"
                        data-enable-text="{{ __('dealers.get_shareable_url_modal.activate_shareable_link') }}"
                    >
                    </button>
                </div>

                <div style="display: flex; align-items: center;">
                    {!! Former::hidden('dealer_client_id', null) !!}
                    {!!
                        Former::select('dealer_client_id', __('dealers.get_shareable_url_modal.form.dealer_client_id').'<span data-toggle="tooltip" data-placement="top" style="margin-left: 5px;" title="" data-original-title="'.__('dealers.get_shareable_url_modal.form.dealer_client_id_help').'"><i class="fa fa-info-circle" aria-hidden="true"></i></span>')
                            ->addGroupClass('width-100')
                            ->style('width: 100%;')
                    !!}

                    <button id="send-share-url" type="button" class="btn btn-blue" style="margin-left: 5px; margin-top: 10px;">
                        {{ __('dealers.get_shareable_url_modal.send_shareable_link') }}
                    </button>
                </div>
                {!! Former::checkbox('create_client', '')
                    ->addGroupClass('hidden')
                    ->text(__('dealers.get_shareable_url_modal.form.create_client'))
                    ->value(1)
                !!}
                {!! Former::checkbox('shared_to_client', '')
                        ->text(__('dealers.get_shareable_url_modal.form.shared_to_client'))
                        ->value(1)
                !!}

                <button type="submit" class="btn btn-red center-block">
                    {{ __('common.forms.submit') }}
                </button>
                {!! Former::close() !!}
            </div>
        </div>
    </div>
</div>

@section('scripts')
    <script type="module">
        $(function () {
            var $getShareableUrlModal = $('#get-shareable-url-modal');

            $(document).on('click', '.get-shareable-url', function (e) {
                e.preventDefault();
                var url = $(this).attr('href');
                var $clientsSelect = $getShareableUrlModal.find('select[name="dealer_client_id"]');

                $getShareableUrlModal.find('input[name="create_client"]').not('type', 'hidden').prop('checked', false);
                $getShareableUrlModal.find('input[name="create_client"]').closest('.form-group').addClass('hidden');

                $.ajax({
                    url: url,
                    method: 'GET',
                }).done(function (response) {
                    var $shareableUrlInput = $getShareableUrlModal.find('input[name="shareable_url"]');
                    $shareableUrlInput.val(response.shareable_url);
                    $getShareableUrlModal.find('input[name="shared_to_client"]').prop('checked', response.shared_to_client);
                    $getShareableUrlModal.data('sendSharableUrl', response.send_shareable_url);
                    $getShareableUrlModal.data('shareLinkDisabled', response.share_link_disabled);
                    $getShareableUrlModal.data('setShareLinkDisabledUrl', response.set_share_link_disabled_url);
                    $getShareableUrlModal.find('form').attr('action', response.assign_client_url);

                    var $deactivateBtn = $getShareableUrlModal.find('#deactivate-shareable-url-btn');
                    $deactivateBtn.text(response.share_link_disabled ? $deactivateBtn.data('enableText') : $deactivateBtn.data('deactivateText'));
                    $shareableUrlInput.next('.help-block').remove();
                    $shareableUrlInput.after('<span class="help-block"><strong>' + (response.share_link_disabled ? $shareableUrlInput.data('inactiveText') : $shareableUrlInput.data('activeText')) + '</strong></span>');

                    $clientsSelect.empty();
                    var assignedClient = response.assigned_client;
                    var option = new Option('', '', false, false);
                    if (assignedClient) {
                        option = new Option(assignedClient.text, assignedClient.id, true, true);
                    }
                    $(option).data('data', assignedClient);
                    $clientsSelect.append(option).trigger('change');

                    $clientsSelect.off('select2:select');
                    $clientsSelect.select2({
                        dropdownParent: $getShareableUrlModal,
                        minimumInputLength: 2,
                        maximumSelectionLength: 10,
                        width: '100%',
                        placeholder: `{!! __('dealers.get_shareable_url_modal.form.dealer_client_id_placeholder') !!}`,
                        allowClear: true,
                        tags: true,
                        ajax: {
                            url: response.clients_list_url,
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    q: params.term
                                };
                            },
                            processResults: function (data) {
                                return {
                                    results: data
                                };
                            },
                        }
                    }).on('select2:select', function (e) {
                        var data = e.params.data;
                        $getShareableUrlModal.find('input[name="create_client"]').closest('.form-group').toggleClass('hidden', !!data.email);
                    });

                    $getShareableUrlModal.modal('show');
                }).fail(function () {
                    alert('{{ __('common.alerts.ajax_error') }}');
                });
            });

            var loading = false;

            $getShareableUrlModal.find('#deactivate-shareable-url-btn').click(function () {
                var $btn = $(this);
                var $shareableUrlInput = $getShareableUrlModal.find('input[name="shareable_url"]');
                var url = $getShareableUrlModal.data('setShareLinkDisabledUrl');
                var shareLinkDisabled = $getShareableUrlModal.data('shareLinkDisabled');
                if (loading) {
                    return;
                }
                loading = true;
                $getShareableUrlModal.addClass('loading');

                $.ajax({
                    url: url,
                    type: 'PUT',
                    data: {
                        share_link_disabled: !shareLinkDisabled,
                    },
                    dataType: 'json',
                }).done(function () {
                    $btn.text(shareLinkDisabled ? $btn.data('deactivateText') : $btn.data('enableText'));
                    $getShareableUrlModal.data('shareLinkDisabled', !shareLinkDisabled);
                    $shareableUrlInput.next('.help-block').remove();
                    $shareableUrlInput.after('<span class="help-block"><strong>' + (shareLinkDisabled ? $shareableUrlInput.data('activeText') : $shareableUrlInput.data('inactiveText')) + '</strong></span>');
                }).fail(function () {
                    alert('{{ __('common.alerts.ajax_error') }}');
                }).always(function () {
                    loading = false;
                    $getShareableUrlModal.removeClass('loading');
                });
            });

            $getShareableUrlModal.find('#send-share-url').click(function () {
                var selected = $getShareableUrlModal.find('select[name="dealer_client_id"]').select2('data')[0] || {};
                var email = selected.email || selected.text;
                if (loading) {
                    return;
                }
                loading = true;
                $getShareableUrlModal.addClass('loading');

                $.ajax({
                    url: $getShareableUrlModal.data('sendSharableUrl'),
                    type: 'POST',
                    data: {
                        email,
                    },
                    dataType: 'json',
                }).done(function () {
                    swal('{{ __('common.success') }}', '{{ __('dealers.get_shareable_url_modal.success_msg') }}', 'success');
                }).fail(function (response) {
                    if (response.status === 422) {
                        alert(response.responseJSON.errors.email[0]);

                        return;
                    }
                    alert('{{ __('common.alerts.ajax_error') }}');
                }).always(function () {
                    loading = false;
                    $getShareableUrlModal.removeClass('loading');
                });
            });
        });
    </script>
@append
