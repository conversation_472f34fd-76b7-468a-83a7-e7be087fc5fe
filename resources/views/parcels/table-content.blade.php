@if($parcels->count())
    <div class="table-responsive">
        <table class="table table-white">
            <thead>
            <tr>
                <th></th>
                <th>{{ __('parcels.table_content.headers.warehouse') }}</th>
                <th>{{ __('parcels.table_content.headers.sender') }}</th>
                <th>{{ __('parcels.table_content.headers.order_number') }}</th>
                <th>{{ __('parcels.table_content.headers.tracking_number') }}</th>
                <th>{{ __('parcels.table_content.headers.description_of_package') }}</th>
                <th>{{ __('parcels.table_content.headers.submitted') }}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($parcels as $parcel)
                <tr class="{{ $parcel->send_back ? 'disabled' : null }}">
                    <td class="actions">
                        <div class="btn-group">
                            <button class="btn btn-link" id="dLabel" type="button" data-toggle="dropdown" aria-haspopup="true"
                                    aria-expanded="false">
                                <i class="fa fa-bars"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dLabel">
                                <li>
                                    <a href="{{ route('warehouse.parcels.show', [$parcel->warehouse->slug, $parcel->id]) }}">
                                      {{ __('parcels.table_content.see_details') }}
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('warehouse.parcels.show', [$parcel->warehouse->slug, $parcel->id]) }}#messages">
                                      {{ __('parcels.table_content.write_a_message') }}
                                    </a>
                                </li>
                                @if($parcel->canAuthUserEdit())
                                    <li>
                                        <a href="{{ route('warehouse.parcels.edit', [$parcel->warehouse->slug, $parcel->id]) }}">
                                          {{ __('parcels.table_content.edit_package') }}
                                        </a>
                                    </li>
                                @endif
                                @if($parcel->canAuthUserDelete())
                                    <li>
                                        <a href="{{ route('warehouse.parcels.destroy', [$parcel->warehouse->slug, $parcel->id]) }}"
                                           class="destroy-parcel">
                                          {{ __('parcels.table_content.delete_package') }}
                                        </a>
                                    </li>
                                @endif
                            </ul>
                        </div>
                    </td>
                    <td>{{ $parcel->warehouse->name }}</td>
                    <td>
                        <a tabindex="0" role="button" data-container="body" data-toggle="tooltip" data-placement="top"
                           title="{{$parcel->sender}}">
                            {{ Str::limit($parcel->sender, 16) }}
                        </a>
                    </td>
                    <td>{{ $parcel->order_number }}</td>
                    <td>
                    <span>
                        <a href="https://parcelsapp.com/en/tracking/{{$parcel->tracking_number}}"
                           rel="nofollow" target="_blank">
                            {{ $parcel->tracking_number }}
                        </a>
                    </span>
                    </td>
                    <td>
                        <a tabindex="0" role="button" data-container="body" data-toggle="tooltip" data-placement="top"
                           title="{{$parcel->description}}">
                            {{ Str::limit($parcel->description, 24) }}
                        </a>
                    </td>
                    <td>
                        @if($parcel->order_id)
                            <a href="{{ $parcel->order->dashboardShowUrl() }}">Tak</a>
                        @elseif($parcel->vehicle_id)
                            <a href="{{ route('vehicles.show', $parcel->vehicle_id) }}">Tak</a>
                        @else
                            {{ __('parcels.table_content.no') }}
                        @endif

                        @if($parcel->added_by_admin)
                            <img src="{{asset('img/logo_small.png')}}" class="added-by-admin" data-toggle="tooltip"
                                 data-placement="top"
                                 title="{{ __('parcels.table_content.added_by_admin') }}"/>
                        @else
                            <img src="{{asset('img/user.png')}}" class="added-by-admin" data-toggle="tooltip"
                                 data-placement="top"
                                 title="{{ __('parcels.table_content.added_by_user') }}"/>
                        @endif
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
@else
    @include('partials._no_results')
@endif
@if($parcels->hasPages())
    <div class="text-center">
        {!! $parcels->render() !!}
    </div>
@endif
