@extends('layouts.base')

@section('content')
    <div id="page_services" class="bg-gray">
        <section class="services_create">
            <div class="container">

                @include('partials.alerts')

                <div id="pjax-container">
                    <nav class="navbar navbar-default nav-bordered">
                        <div class="container-fluid">
                            <div class="navbar-header">
                                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#section-nav" aria-expanded="false">
                                    <span class="sr-only">Toggle navigation</span>
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                    <span class="icon-bar"></span>
                                </button>
                            </div>

                            <div class="collapse navbar-collapse" id="section-nav">
                                <ul class="nav navbar-nav">
                                    <li><a href="{{ route('parcels.index') }}">{{ __('common.cancel_and_return_to_list') }}</a></li>
                                </ul>
                            </div>
                        </div>
                    </nav>

                    <div class="box-bordered">
                        {!! Former::open()->action(route('warehouse.parcels.store', [$currentWarehouse->slug]))->addClass('create_form repeatable-form fieldset')->method('POST') !!}

                        <div class="row">
                            <div class="col-xs-offset-1 col-xs-10">
                                <div class="box-body">
                                    <p class="head">{{ __('parcels.create.add_package') }}</p>
                                </div>
                            </div>
                        </div>

                        <div style="display: none;" class="single-item form-fields form-preset">
                            <div class="row">
                                <div class="col-xs-offset-1 col-xs-10">
                                    <div class="row">
                                        <div class="col-md-12">
                                            {!! Former::text('parcel[0][sender]', __('parcels.create.sender'))->placeholder(__('parcels.create.sender_help'))->disabled() !!}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            {!! Former::text('parcel[0][order_number]', __('parcels.create.order_number'))->placeholder(__('parcels.create.order_number_help'))->disabled() !!}
                                        </div>
                                        <div class="col-md-6">
                                            {!! Former::text('parcel[0][tracking_number]', __('parcels.create.tracking_number'))->placeholder(__('parcels.create.tracking_number_help'))->disabled() !!}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            {!! Former::textarea('parcel[0][description]', __('parcels.create.description_of_package'))->rows(3)->disabled() !!}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button class="btn btn-red btn-delete remove-form-fields"><i class="fa fa-times"></i></button>
                        </div>

                        <div class="items fieldset-container">
                            <div class="single-item form-fields">
                                <div class="row">
                                    <div class="col-xs-offset-1 col-xs-10">
                                        <div class="row">
                                            <div class="col-md-12">
                                                {!! Former::text('parcel[1][sender]', __('parcels.create.sender'))->placeholder(__('parcels.create.sender_help')) !!}
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                {!! Former::text('parcel[1][order_number]', __('parcels.create.order_number'))->placeholder(__('parcels.create.order_number_help')) !!}
                                            </div>
                                            <div class="col-md-6">
                                                {!! Former::text('parcel[1][tracking_number]', __('parcels.create.tracking_number'))->placeholder(__('parcels.create.tracking_number_help')) !!}
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12">
                                                {!! Former::textarea('parcel[1][description]', __('parcels.create.description_of_package'))->rows(3) !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button class="btn btn-red btn-delete remove-form-fields"><i class="fa fa-times"></i></button>
                            </div>

                            @if(count(old('parcel', [])) > 1)
                                @foreach(collect(old('parcel'))->except(1) as $key => $value)
                                    <div class="single-item">
                                        <div class="row">
                                            <div class="col-xs-offset-1 col-xs-10">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        {!! Former::text("parcel[{$key}][sender]", __('parcels.create.sender'))->value($value['sender']) !!}
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        {!! Former::text("parcel[{$key}][order_number]", __('parcels.create.order_number'))->value($value['order_number']) !!}
                                                    </div>
                                                    <div class="col-md-6">
                                                        {!! Former::text("parcel[{$key}][tracking_number]", __('parcels.create.tracking_number'))->value($value['tracking_number']) !!}
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-12">
                                                        {!! Former::textarea("parcel[{$key}][description]", __('parcels.create.description_of_package'))->value($value['description'])->rows(3) !!}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <button class="btn btn-red btn-delete"><i class="fa fa-times"></i></button>
                                    </div>
                                @endforeach
                            @endif
                        </div>

                        <div class="row">
                            <div class="col-xs-offset-1 col-xs-10">
                                <div class="text-right">
                                    <button class="btn btn-blue add-preset"><i class="fa fa-plus fa-fw"></i> {{ __('parcels.create.add_another_package') }}</button>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-xs-offset-1 col-xs-10">
                                <div class="box-footer">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <img src="{{ asset('img/icon_1.png') }}" class="img-responsive center-block">
                                        </div>
                                        <div class="col-md-10">
                                            <p class="head">{{ __('parcels.create.finalize') }}</p>
                                            <p></p>
                                            {!! Former::large_red_submit(__('parcels.create.add_all_packages_to_the_warehouse')) !!}
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        {!! Former::close() !!}
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
