<div class="container">
    <nav class="navbar navbar-default navbar-bordered-transparent">
        <div class="container-fluid">
            <ul class="nav navbar-nav">
                <li class="{{ Str::contains(Route::currentRouteAction(), 'ParcelsController@index') ? 'active' : '' }}">
                    <a href="{{ route('parcels.index') }}">
                        <span>{{ __('parcels.top_bar.warehouse') }}</span>
                    </a>
                </li>
                <li class="{{ Str::contains(Route::currentRouteAction(), 'ParcelsController@sent') ? 'active' : '' }}">
                    <a href="{{ route('parcels.sent') }}">
                        <span>{{ __('parcels.top_bar.sent') }}</span>
                    </a>
                </li>
                <li class="{{ Str::contains(Route::currentRouteAction(), 'OrdersController@index') ? 'active' : '' }}">
                    <a href="{{ route('orders.index', ['parcels']) }}">
                        <span>{{ __('parcels.top_bar.orders') }}</span>
                    </a>
                </li>
                <li class="{{ Str::contains(Route::currentRouteAction(), 'WarehouseController@addresses') ? 'active' : '' }}">
                    <a href="{{ route('parcels.address') }}">
                        <span>{{ __('parcels.top_bar.warehouses_addresses') }}</span>
                    </a>
                </li>
                <li class="{{ Str::contains(Route::currentRouteAction(), 'WarehouseController@compare') ? 'active' : '' }}">
                    <a href="{{ route('parcels.warehouses-comparison') }}">
                        <span>{{ __('parcels.top_bar.compare_warehouses') }}</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</div>