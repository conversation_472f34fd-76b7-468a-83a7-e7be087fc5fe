@extends('layouts.base')

@section('content')
    <div id="page_services" class="bg-gray">
        <section class="narrow-padded">
            <div class="container">
                <div class="clearfix">
                    <div class="account-balances">
                        <h3>
                            {{ __('vehicles.reports.number_of_free_reports_available') }}
                            <strong>
                                {{ auth()->user()->free_vehicle_reports_count }}
                            </strong>
                        </h3>
                    </div>

                    <div class="page-actions">
                        <div class="page-actions__btns">
                            <a href="{{ route('vehicles.index') }}" class="btn btn-red">
                                <strong>
                                    {{ __('vehicles.reports.vehicles') }}
                                    <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                                </strong>
                            </a>

                            @if($createReportEnabled)
                                @canany(['vehicle-reports.can-pay-with-points', 'vehicle-reports.can-pay-with-any-company'])
                                    <a href="{{ route('vehicle-reports.create') }}" class="btn btn-blue">
                                        <strong>
                                            {{ __('vehicles.reports.check_vehicle_history') }}
                                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                                        </strong>
                                    </a>
                                @else
                                    <button title="{{ __('vehicles.reports.no_credits') }} $ {{ price_to_string(config('payments.prices.carfax_report')) }}"
                                            class="btn btn-blue disabled"
                                            type="button" data-toggle="tooltip" data-placement="top">
                                        <strong>
                                            {{ __('vehicles.reports.check_vehicle_history') }}
                                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                                        </strong>
                                    </button>
                                @endcanany
                            @endif
                        </div>

                        <div class="page-actions__filters">
                            {!! Former::openInline()->method('GET')->id('filters')->action(route('vehicle-reports.index')) !!}
                            {!! Former::text('search')->placeholder(__('common.tables.search'))->prepend('<i class="fa fa-search" aria-hidden="true"></i>') !!}
                            {!! Former::close() !!}
                        </div>
                    </div>
                </div>

                <div class="no-padding">
                    <div class="table-ajax">
                        @include('vehicle-reports.partials.table-content')
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
