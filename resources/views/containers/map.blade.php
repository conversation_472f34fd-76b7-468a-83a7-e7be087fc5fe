<!DOCTYPE html>
<html lang="{{ App::getLocale() }}" class="h-100 w-100">
<head>
  @if (! empty(config('auction.sentry.js_dns')))
    <script
      src="https://browser.sentry-cdn.com/5.6.1/bundle.min.js"
      integrity="sha384-pGTFmbQfua2KiaV2+ZLlfowPdd5VMT2xU4zCBcuJr7TVQozMO+I1FmPuVHY3u8KB"
      crossorigin="anonymous"
    ></script>
    <script>
      Sentry.init({ dsn: '{{ config('auction.sentry.js_dns') }}' });
    </script>
  @endif

  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="shortcut icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" />
  <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" />
  <meta name="csrf-token" content="{{ csrf_token() }}" />
  <meta name="robots" content="noindex" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Jost:ital@0;1&family=Lato:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">

  <title>Container Tracking Map</title>
  @vite(['resources/sass/new/container-tracking/trackingMap.scss', 'resources/js/new/container-tracking/trackingMap.js'])
  @translations(['en', 'pl'])
</head>
<body class="h-100 w-100">
<div class="position-relative h-100">
  <div id="route-map"></div>
  <div class="map-parent d-flex justify-content-center align-items-center"
       x-data="fetchMapData('{{ route('container-tracking-data', ['container_number' => $containerNumber]) }}')"
       x-init="getData()">
    <template x-if="isLoading">
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">{{ __('track-container.loading') }}</span>
        </div>
      </div>
    </template>

    <template x-if="error">
      <div class="alert alert-danger mt-3" role="alert">
        {{ __('track-container.loading_error') }}
      </div>
    </template>

    <template x-if="!error && !isLoading && trackData">
      <div class="details-container" x-data="{ panelVisible: true }">
        <!-- Chevron Toggle Button -->
        <button
          @click="panelVisible = !panelVisible"
          class="btn btn-light position-absolute border shadow-sm d-flex align-items-center justify-content-center"
          style="top: -15px; left: 50%; transform: translateX(-50%); width: 40px; height: 30px; border-radius: 15px; z-index: 1000;"
          :aria-label="panelVisible ? 'Hide panel' : 'Show panel'"
        >
          <i
            class="fas fa-chevron-down transition-all"
            :class="{ 'fa-chevron-up': panelVisible, 'fa-chevron-down': !panelVisible }"
            style="font-size: 12px; transition: transform 0.3s ease;"
          ></i>
        </button>

        <div
          class="container-info-parent show transition-all"
          x-show="panelVisible"
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0 transform translate-y-full"
          x-transition:enter-end="opacity-100 transform translate-y-0"
          x-transition:leave="transition ease-in duration-300"
          x-transition:leave-start="opacity-100 transform translate-y-0"
          x-transition:leave-end="opacity-0 transform translate-y-full"
        >
          <div class="container-info-top">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="container-info-top__title" x-text="containerTitle()"></h5>
              <template x-if="trackData.shippingStatus">
                <span class="container-info-top__status" x-text="trackData.shippingStatus"></span>
              </template>
            </div>
            <div class="container-info-top__subtitle">
              <div class="time-left-info" x-show="etaAtdDifference() > 0">
                <div class="time-left-info__text">
                  {{ __('track-container.estimated_arrival') }}
                </div>
                <div class="time-left-info__value">
                  <span x-text="etaAtdDifference()"></span>
                  <template x-if="etaAtdDifference() === 1">
                    <span>{{ __('track-container.day_left') }}</span>
                  </template>
                  <template x-if="etaAtdDifference() > 1">
                    <span>{{ __('track-container.days_left') }}</span>
                  </template>
                </div>
              </div>
              <div class="progress-parent">
                <img src="{{ Vite::image('map/icons/port-crane.svg') }}" alt="" class="progress-icons" />
                <div class="flex-fill">
                  <div class="progress my-2 bg-white" style="height: 8px;">
                    <div class="progress-bar custom-progress-bar"
                         role="progressbar"
                         :style="`width: ${progress()}%`"
                         :aria-valuenow="progress()"
                         aria-valuemin="0"
                         aria-valuemax="100">
                    </div>
                  </div>
                </div>
                <img src="{{ Vite::image('map/icons/meta.svg') }}" alt="" class="progress-icons" />
              </div>
              <div class="destination-info">
                <div>
                  <div class="destination-info__city" x-text="polCity()"></div>
                  <div class="destination-info__date" x-text="atdDate()"></div>
                </div>
                <div>
                  <div class="destination-info__city text-end" x-text="podCity()"></div>
                  <div class="destination-info__date" x-text="etaDate()"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="container-buttons">
            <button @click="showTab('route')" :class="{ 'active' : activeTab === 'route' }">
              {{ __('track-container.sidebar.route') }}
            </button>
            <button @click="showTab('details')" :class="{ 'active' : activeTab === 'details' }">
              {{ __('track-container.sidebar.details') }}
            </button>
          </div>
          <div class="container-info-bottom">
            <template x-if="activeTab === 'route' && trackData.events && trackData.events.length === 0">
              <div>
                {{ __('track-container.no_data') }}
              </div>
            </template>
            <template x-if="activeTab === 'route' && trackData.events && trackData.events.length > 0">
              <div x-show="trackData.events.length > 0">
                <div class="route-info container" x-data>
                  <div class="position-relative ps-4 mb-1">
                    <template x-for="(event, index) in trackData.events"
                              :key="event.location.name + event.location.countryCode">
                      <div class="pb-2 position-relative"
                           :class="routeDataHasAllActual(event.routeData) ? 'solid-connection' : 'dashed-connection'">

                        <img
                          :src="(() => {
                              if (index === 0) return '{{ Vite::image('map/icons/port-crane.svg') }}';
                              if (index === trackData.events.length - 1) return '{{ Vite::image('map/icons/meta.svg') }}';
                              if (routeDataHasAllActualAndIsLastActual(event.routeData)) return '{{ Vite::image('container-tracking/cargo-icon.svg') }}';
                              return '{{ Vite::image('container-tracking/custom-marker.svg') }}';
                            })()"
                          alt=""
                          class="route-info__icons"
                          width="24"
                          height="24"
                        />

                        <div class="d-flex gap-2 align-items-center mb-2">
                          <div>
                            <h6 class="fw-bold text-uppercase mb-0 pt-1"
                                x-text="getFormattedEventPlace(event)"></h6>
                          </div>
                          <span class="in-transit-btn" x-show="routeDataHasAllActualAndIsLastActual(event.routeData)">
                                {{ __('track-container.statuses.in_transit') }}
                            </span>
                        </div>

                        <ul class="list-unstyled mb-0">
                          <template x-for="item in event.routeData" :key="item.type + item.date">
                            <li class="d-flex justify-content-between">
                              <span x-text="item.description"></span>
                              <span class="text-muted text-nowrap" x-text="formatDateTime(item.date)"></span>
                            </li>
                          </template>
                        </ul>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <template x-if="activeTab === 'details'">
              <div class="details-info">
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.shipment_type') }}</div>
                  <div class="details-info_row--value" x-text="trackData.shipmentType"></div>
                </div>
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.shipment_number') }}</div>
                  <div class="details-info_row--value" x-text="trackData.shipmentNumber"></div>
                </div>
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.sealine_name') }}</div>
                  <div class="details-info_row--value" x-text="trackData.sealineName"></div>
                </div>
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.vessels') }}</div>
                  <div class="details-info_row--value" x-text="formattedVessels()"></div>
                </div>
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.last_updated_at') }}</div>
                  <div class="details-info_row--value" x-text="lastUpdatedDate()"></div>
                </div>
                <div class="details-info_row">
                  <div class="details-info_row--label">{{ __('track-container.sidebar.container_size_type') }}</div>
                  <div class="details-info_row--value" x-text="trackData.containerSizeType"></div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>

  </div>
</div>
</body>
</html>
