@php
    /** @var \App\Models\Presenters\VehiclePresenter $vehicle */
@endphp
<div class="btn-group">
    <button
            class="dropdown-toggle btn btn-link"
            type="button" data-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false"
    >
        <i class="fa fa-bars" aria-hidden="true"></i>
    </button>
    <ul class="dropdown-menu">
        <li>
            <a href="{{ route('vehicles.show', $vehicle) }}">
              {{ __('vehicles.table_actions.click_for_details') }}
            </a>
        </li>
        <li>
            <a href="{{ route('vehicles.show', $vehicle) }}#messages">
                {{ __('vehicles.table_actions.text_carrierwise') }}
            </a>
        </li>
        <li>
            <a
                href="{{ $vehicle->shareInfoUrl() }}"
                class="get-shareable-url"
            >
                {{ __('dealers.table.actions.get_shareable_url') }}
            </a>
        </li>
        @if($vehicle->hasImages())
            <li>
                <a href="{{ route('vehicles.files-download', [$vehicle->id, 'images']) }}" target="_blank">
                    <strong>{{ __('vehicles.table_actions.download_pictures') }}</strong>
                </a>
            </li>
        @endif
        @if(!empty($vehicle->title_file))
            <li>
                <a href="{{ route('vehicles.files-download', [$vehicle->id, 'title_file']) }}" target="_blank">
                    <strong>{{ __('vehicles.table_actions.download_title') }}</strong>
                </a>
            </li>
        @endif
        @if($vehicle->order->canShowInvoice())
            <li>
                <a href="{{ $vehicle->order->invoiceDownloadUrl() }}" target="_blank">
                    <strong>{{ __('vehicles.table_actions.download_invoice') }}</strong>
                </a>
            </li>
        @endif
        @if($vehicle->canAssignParcels())
            <li>
                <a href="{{ $vehicle->assignParcelsUrl() }}">
                    <strong>{{ __('vehicles.table_actions.add_parcels') }}</strong>
                </a>
            </li>
        @endif
    </ul>
</div>
