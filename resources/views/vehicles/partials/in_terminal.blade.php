@php
    /** @var \App\Models\Presenters\VehiclePresenter[]|\App\Project\Core\Presenters\PaginatorPresenter $vehicles */
@endphp
<table class="table table-white">
    <thead>
    <tr>
        <th></th>
        <th>ID</th>
        <th>{{ __('vehicles.table.header.vehicle_photos') }}</th>
        <th>{{ __('vehicles.table.header.terminal') }}</th>
        <th>{{ __('vehicles.table.header.vehicle') }}</th>
        <th>VIN</th>
        <th>{{ __('vehicles.table.header.title') }}</th>
        <th>{{ __('vehicles.table.header.destination') }}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($vehicles as $vehicle)
        <tr>
            <td class="actions">
                @include('vehicles.partials._vehicle-actions')
            </td>
            <td>
                {{ $vehicle->order->id }}
            </td>
            <td class="narrow">
                @include('vehicles.partials._table_thumbnail')
            </td>
            <td>
                @include('vehicles.partials._terminal')
            </td>
            <td>
                @include('vehicles.partials._vehicle-description')
            </td>
            <td>
                {{ $vehicle->vin_number }}
            </td>
            <td>
                @if($vehicle->title_status !== null)
                    {{ $vehicle->title_status ? 'Present' : 'Missing' }}
                @endif
            </td>
            <td>
                @include('vehicles.partials._vehicle-delivery-location')
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
