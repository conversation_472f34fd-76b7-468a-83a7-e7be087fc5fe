@php
    /** @var \App\Models\Presenters\VehiclePresenter[]|\App\Project\Core\Presenters\PaginatorPresenter $vehicles */
@endphp
<table class="table table-white">
    <thead>
    <tr>
        <th style="width: 40px;">
            <input data-select-all type="checkbox">
        </th>
        <th></th>
        <th>ID</th>
        <th>{{ __('vehicles.table.header.vehicle_photos') }}</th>
        <th>{{ __('vehicles.table.header.vehicle') }}</th>
        <th>VIN</th>
        <th>{{ __('vehicles.table.header.shipping_line') }}</th>
        <th>{{ __('vehicles.table.header.container_number') }}</th>
        <th>ETA</th>
        <th>{{ __('vehicles.table.header.destination') }}</th>
        <th>{{ __('vehicles.table.header.tracking_document_title') }}</th>
        <th>{{ __('vehicles.table.header.customs_status') }}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($vehicles as $vehicle)
        <tr @if($vehicle->customsDeliveredToCustomer())class="active"@endif>
            <td>
                <input type="checkbox" data-select-all-target value="{{ $vehicle->getKey() }}">
            </td>
            <td class="actions">
                @include('vehicles.partials._vehicle-actions')
            </td>
            <td>{{ $vehicle->order->id }}</td>
            <td class="narrow">
                @include('vehicles.partials._table_thumbnail')
            </td>
            <td>
                @include('vehicles.partials._vehicle-description')
            </td>
            <td>
                @include('vehicles.partials._short-vin')
            </td>
            <td>{{ $vehicle->shippingLine ? $vehicle->shippingLine->name : '' }}</td>
            <td><a href="" data-track-container="{{ $vehicle->container_number }}">{{ $vehicle->container_number }}</a>
            </td>
            <td>{{ $vehicle->eta ? $vehicle->eta->format('d.m.Y') : '' }}</td>
            <td>
                @include('vehicles.partials._vehicle-delivery-location')
            </td>
            <td>
                @if(!empty($vehicle->title_tracking_number))
                    <a href="{{ $vehicle->titleTrackingUrl() }}" target="_blank">
                        {{ $vehicle->title_tracking_number }}
                    </a>
                @endif
            </td>
            <td style="min-width: 175px;">
                @include('vehicles.partials._customs-agency-status-form')
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
