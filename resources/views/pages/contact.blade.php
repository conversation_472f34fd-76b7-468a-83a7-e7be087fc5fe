@extends('layouts.landing')

@section('content')
    <div id="page_contact">
        <section>
            <div class="container">
                <div class="row">
                    <div class="col-md-7">
                        <div class="box-bordered">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-offset-1 col-md-10">
                                        <p class="head">{{ __('contact.lets_be_in_touch') }}</p>

                                        <div class="contact_list_item">
                                            <h4><strong>{{ __('contact.customer_service') }}</strong></h4>
                                            {!! hide_email(config('site.contact.mail')) !!}<br/>
                                            <a href="tel:****** 732 8238">****** 732 8238</a>
                                        </div>

                                        <div class="contact_list_item">
                                            <h4><strong>{{ __('contact.contact_list.krystian.name') }} | {{ __('contact.contact_list.krystian.languages') }}</strong></h4>
                                            <strong>{{ __('contact.contact_list.krystian.responsible_for') }}</strong><br/>
                                            {!! hide_email(__('contact.contact_list.krystian.email')) !!}
                                        </div>

                                        <div class="contact_list_item">
                                            <h4><strong>{{ __('contact.contact_list.ediie.name') }} | {{ __('contact.contact_list.ediie.languages') }}</strong></h4>
                                            <strong>{{ __('contact.contact_list.ediie.responsible_for') }}<br/></strong>
                                            {!! hide_email(__('contact.contact_list.ediie.email')) !!}
                                        </div>

                                        <div class="contact_list_item">
                                            <h4><strong>{{ __('contact.contact_list.peter.name') }} | {{ __('contact.contact_list.peter.languages') }}</strong></h4>
                                            <strong>{{ __('contact.contact_list.peter.responsible_for') }}</strong><br/>
                                            {!! hide_email(__('contact.contact_list.peter.email')) !!}
                                        </div>

                                        <div class="contact_list_item">
                                            <h4><strong>Social Media</strong></h4>
                                            <a href="http://fb.com/CarrierWiseCom" target="_blank" rel="nofollow">Facebook</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="box-bordered">
                            <div class="box-body">
                                <p class="head text-center">{{ __('contact.contact_form') }}</p>

                                <div class="row">
                                    <div class="col-md-offset-1 col-md-10">
                                        {!! Former::open() !!}
                                        {!! Former::email('email', __('contact.email'))->required() !!}
                                        {!! Former::textarea('content', __('contact.message'))->required()->rows(5) !!}
                                        {!! Former::hidden('recaptcha')->id('contact-recaptcha') !!}
                                        <p style="font-size: 10px; margin-bottom: 20px; color: gray;"
                                           class="text-center">
                                          {!! __('contact.recaptcha_info') !!}
                                        </p>
                                        <div class="text-center">
                                            {!! Former::actions()->large_red_submit(__('contact.send')) !!}
                                        </div>
                                        {!! Former::close() !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

@section('scripts')
    <script type="module">
        grecaptcha.ready(function () {
            grecaptcha.execute('{{ config('recaptcha.site_key') }}', {action: 'contact'}).then(function (token) {
                if (token) {
                    $('#contact-recaptcha').val(token);
                }
            });
        });
    </script>
@append
