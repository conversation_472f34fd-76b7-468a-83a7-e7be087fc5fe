@extends('profile.settings._settings-layout')

@section('tab-content')
    <nav class="navbar navbar-default navbar-bordered-transparent">
        <div class="container-fluid">
            <ul class="nav navbar-nav">
                @foreach($statuses as $key => $item)
                    <li class="{{ $item === $status ? 'active' : '' }}">
                        <a href="{{ route('settings.title-types', ['status' => $item]) }}">
                            {{ __("settings.sales_document.tabs.$item") }}
                            <span class="pull-right" data-toggle="tooltip" data-placement="top"
                                  title="{{ __("vehicles.title_types_statuses_tooltips.{$key}") }}">
                                <i class="fa fa-info-circle" aria-hidden="true"></i>
                            </span>
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <div class="clearfix">
        <div class="page-actions">
            <div class="page-actions__btns">
                <button type="button" class="btn btn-red" data-toggle="modal"
                        data-target="#vehicle-title-types-info-modal">
                  {{ __('settings.sales_document.how_to_check_type_of_document') }}
                </button>
            </div>

            <div class="page-actions__filters">
                {!! Former::openInline()->method('GET')->id('filters')->action(route('settings.title-types')) !!}
                {!! Former::text('search')->placeholder(__('settings.sales_document.search'))->prepend('<i class="fa fa-search" aria-hidden="true"></i>')->style('min-width: 250px;') !!}
                {!! Former::close() !!}
            </div>
        </div>
    </div>

    <div class="no-padding">
        <div class="table-ajax">
            @include('profile.settings.title-types.partials.table-content')
        </div>
    </div>
@endsection

@include('profile.settings.title-types.partials.info-modal')
