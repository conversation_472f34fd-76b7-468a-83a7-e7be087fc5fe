@php
    /** @var \App\ViewModels\Dashboard\Settings\SettingsPriceListViewModel $viewModel */
@endphp
@if($viewModel->vehicleDeliveryLocation === null)
    <div class="text-center">
        <h2>{{ __('settings.vehicle_pricing.select_delivery_location') }}</h2>
    </div>
@elseif($viewModel->vehiclePrices && $viewModel->vehiclePrices->isNotEmpty())
    <div class="table-responsive" style="overflow-x: visible;">
        <table class="table table-white table-white--narrow table-white--sticky-header">
            <thead>
            <tr>
                @foreach(array_keys($viewModel->vehiclePrices->first()) as $key)
                    <th>{{ $key }}</th>
                @endforeach
            </tr>
            </thead>
            <tbody>
                @foreach($viewModel->vehiclePrices as $vehiclePriceData)
                    <tr style="height: 53px;">
                        @foreach($vehiclePriceData as $value)
                            <td>{{ $value }}</td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    @include('partials._no_results')
@endif
