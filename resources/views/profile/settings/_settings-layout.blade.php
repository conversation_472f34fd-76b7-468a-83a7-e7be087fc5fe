@extends('layouts.base')

@section('content')
    <div id="page_settings" class="bg-gray">
        <section>
            <div class="container">

                @include('partials.validation')

                <nav class="navbar navbar-default nav-bordered">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"
                                data-target="#section-nav" aria-expanded="false">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                    </div>

                    <div class="collapse navbar-collapse" id="section-nav">
                        <ul class="nav navbar-nav">
                            <li @if(Str::contains(Route::currentRouteAction(), 'SettingsController@invoice')) class="active" @endif>
                                <a href="{{ route('settings.invoice') }}">{{ __('settings.tabs.account_details') }}</a>
                            </li>
                            <li @if(isset($tab) && $tab==='contact') class="active" @endif>
                                <a href="{{ route('settings.index', ['tab' => 'contact']) }}">{{ __('settings.tabs.contact_information') }}</a>
                            </li>
                            <li @if(isset($tab) && $tab==='notifications') class="active" @endif>
                                <a href="{{ route('settings.index', ['tab' => 'notifications']) }}">{{ __('settings.tabs.notifications') }}</a>
                            </li>
                            <li @if(Str::contains(Route::currentRouteName(), 'settings.price-list.index')) class="active" @endif>
                                <a href="{{ route('settings.price-list.index') }}">{{ __('settings.tabs.vehicle_pricing') }}</a>
                            </li>
                            <li @if(Str::contains(Route::currentRouteName(), 'settings.title-types')) class="active" @endif>
                                <a href="{{ route('settings.title-types') }}">{{ __('settings.tabs.sales_document') }}</a>
                            </li>
                            <li @if(Str::contains(Route::currentRouteName(), 'settings.commissions')) class="active" @endif>
                                <a href="{{ route('settings.commissions') }}">{{ __('settings.tabs.commission') }}</a>
                            </li>
                            <li @if(isset($tab) && $tab==='password') class="active" @endif>
                                <a href="{{ route('settings.index', ['tab' => 'password']) }}">{{ __('settings.tabs.change_password') }}</a>
                            </li>
                        </ul>
                    </div>
                </nav>

                @yield('tab-content')

            </div>
        </section>
    </div>
@endsection
