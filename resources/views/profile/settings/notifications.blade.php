@extends('profile.settings._settings-layout')

@section('tab-content')
    @component('profile.settings._tab-panel')
        {!! Former::openVertical()->action(route('settings.post'))->method('POST') !!}
        {!! Former::populate(auth()->user()) !!}
        <h2 class="head">{{ __('settings.notifications.title') }}</h2>

        {!! Former::checkbox('notifications[new_item]', '')->text(__('settings.notifications.new_item'))->value(true) !!}
        {!! Former::checkbox('notifications[new_reply]', '')->text(__('settings.notifications.new_reply'))->value(true) !!}
        {!! Former::checkbox('notifications[new_status]', '')->text(__('settings.notifications.new_status'))->value(true) !!}
        {!! Former::checkbox('notifications[new_valuation]', '')->text(__('settings.notifications.new_valuation'))->value(true) !!}
        {!! Former::checkbox('notifications[vehicle_status_changed]', '')->text(__('settings.notifications.vehicle_status_changed'))->value(true) !!}

        <div class="text-center">
            {!! Former::large_red_submit(__('settings.notifications.save')) !!}
        </div>

        {!! Former::close() !!}
    @endcomponent
@endsection
