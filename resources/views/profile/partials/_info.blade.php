<section class="borderless bg-gray">
    <div class="container">
        @if(!auth()->user()->active)
            <div class="alert alert-success verified-alert">
                <i class="fa fa-envelope"></i>
                <span>
                    {{ __('info.confirm_email', ['siteName' => config('site.name')]) }}
                    {!! __('info.confirm_email_send_to', ['email' => auth()->user()->email]) !!}
                </span>
            </div>
        @endif

        @if(auth()->user()->is_suspended)
            <div class="alert alert-danger verified-alert">
                <span>{{ __('info.account_suspended') }}</span>
            </div>
        @endif
        <div class="row info-cards">
            <div class="col-sm-4">
                <div class="info-card">
                    <div class="info-card__header">
                        <img src="{{ asset('img/money_stock.svg') }}"
                             class="svg info-card__icon">
                    </div>
                    <div class="info-card__body">
                        <span class="info-card__text">{{ __('info.balance') }}</span>
                        @if($transactionBalances->isNotEmpty())
                            @foreach($transactionBalances as $transactionBalance)
                                <div class="info-card__text--small__bold">{{ $transactionBalance->company->name }}</div>
                                <div class="info-card__text--small" style="margin-bottom: 8px;">
                                    {{ '$'.price_to_string($transactionBalance->balance) }}
                                </div>
                            @endforeach
                        @else
                            <div class="info-card__text--small">{{ __('info.no_balance') }}</div>
                        @endif
                    </div>
                    <div class="info-card__footer">
                        <a href="{{ route('payments.index') }}" class="btn btn-red">
                          {{ __('info.deposit') }}
                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="info-card">
                    <div class="info-card__header">
                        <img src="{{ asset('img/bill.svg') }}"
                             class="svg info-card__icon">
                    </div>
                    <div class="info-card__body">
                        <span class="info-card__text">{{ __('info.to_pay') }}</span>
                        @if(!empty($pricedOrdersBalances))
                            @foreach($pricedOrdersBalances as $pricedOrdersBalance)
                                <div class="info-card__text--small__bold">
                                    {{ $pricedOrdersBalance->companyName }}
                                </div>
                                <div class="info-card__text--small" style="margin-bottom: 8px;">
                                    {{ '$'.price_to_string($pricedOrdersBalance->balance) }}
                                </div>
                            @endforeach
                        @else
                            <div class="info-card__text--small">{{ __('info.no_priced') }}</div>
                        @endif
                    </div>
                    <div class="info-card__footer">
                        <a href="{{ route('payments.orders') }}" class="btn btn-red">
                          {{ __('info.bills') }}
                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="info-card">
                    <div class="info-card__header">
                        <div class="circle-progress-bar circle-progress-bar--progress-{{ $progressBarData->pointsPercentage }}">
                            <span>{{ $progressBarData->pointsPercentage }}%</span>
                        </div>
                        <div>
                            <span class="info-card__text info-card__text--small">{{ __('info.number_of_points') }}</span>
                            <span class="info-card__text info-card__text--big info-card__text--mb-0">
                                {{ $progressBarData->points }}
                            </span>
                        </div>
                    </div>
                    <div class="info-card__body">
                        <span class="info-card__text info-card__text--gray">
                            @if($progressBarData->missingPoints > 0)
                                {!! __('info.missing_points', ['missingPoints' => $progressBarData->missingPoints]) !!}
                            @else
                                {!! __('info.congratulation_points') !!}
                            @endif
                        </span>
                    </div>
                    <div class="info-card__footer">
                        <a href="{{ route('points.index') }}" class="btn btn-gray">
                            {{ __('info.more_information') }}
                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                        </a>
                        <a href="{{ route('points.info') }}" class="btn btn-blue">
                          {{ __('info.how_does_it_work') }}
                            <i class="fa fa-angle-right btn-icon" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
