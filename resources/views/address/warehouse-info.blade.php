@php
    /** @var \App\Models\Warehouse $warehouse */
@endphp
<div class="warehouse_box__section">
  {{ __('warehouse-info.how_to_assign_a_package_to_the_warehouse_by_yourself') }}
</div>
<div class="warehouse_box__how_to">
    <div class="warehouse_box__step">
        <div class="warehouse_box__step__icon">
            <img src="{{ asset('img/money.svg') }}" class="svg">
        </div>
        <div class="warehouse_box__step__content">
            <p class="warehouse_box__step__title">{{ __('warehouse-info.pricing') }}</p>
            @if($warehouse->slug === \App\Models\Warehouse::NEWARK_SLUG)
                {{ __('warehouse-info.the_shipping_fee_for_the_package_is', ['price' => '$10']) }}
            @else
                {{ __('warehouse-info.compare_delivery_terms_and_prices_between_warehouses') }}<br/>
                <a href="{{ route('calculators.parcels') }}" target="_blank" class="btn btn-red warehouse_box__step__btn">
                    {{ __('warehouse-info.check') }}
                </a>
                <a href="{{ route('parcels.warehouses-comparison') }}" target="_blank" class="btn btn-blue warehouse_box__step__btn">
                    {{ __('warehouse-info.terms') }}
                </a>
            @endif
        </div>
    </div>

    <div class="warehouse_box__step">
        <div class="warehouse_box__step__icon">
            <img src="{{ asset('img/box.svg') }}" class="svg">
        </div>
        <div class="warehouse_box__step__content">
            <p class="warehouse_box__step__title">{{ __('warehouse-info.send_a_package_to_the_warehouse') }} {{ $warehouse->name }}</p>
            @if($warehouse->slug === \App\Models\Warehouse::NEWARK_SLUG)
                {{ __('warehouse-info.by_writing_ocean_drive_and_the_customer_number_on_the_package') }}
            @else
                {{ __('warehouse-info.by_providing_name_surname_and_customer_number_on_the_package') }}
            @endif
        </div>
    </div>

    <div class="warehouse_box__step">
        <div class="warehouse_box__step__icon">
            <img src="{{ asset('img/package.svg') }}" class="svg">
        </div>
        <div class="warehouse_box__step__content">
            @if($warehouse->slug === \App\Models\Warehouse::NEWARK_SLUG)
                <p class="warehouse_box__step__title">{{ __('warehouse-info.delivered_packages_to_warehouse_stock') }}</p>
                {{ __('warehouse-info.check_in_the_client_panel') }}<br/>
                <a href="{{ route('parcels.choose-warehouse') }}" target="_blank" class="btn btn-red warehouse_box__step__btn">
                    {{ __('warehouse-info.check') }}
                </a>
            @else
                <p class="warehouse_box__step__title">{{ __('warehouse-info.add_the_package_to_the_warehouse') }}</p>
                {{ __('warehouse-info.after_receiving_the_tracking_number_from_the_sender') }}<br/>
                <a href="{{ route('parcels.choose-warehouse') }}" target="_blank" class="btn btn-red warehouse_box__step__btn">
                    {{ __('warehouse-info.add') }}
                </a>
            @endif
        </div>
    </div>

    <div class="warehouse_box__step">
        <div class="warehouse_box__step__icon">
            <img src="{{ asset('img/box_1.svg') }}" class="svg">
        </div>
        <div class="warehouse_box__step__content">
            @if($warehouse->slug === \App\Models\Warehouse::NEWARK_SLUG)
                <p class="warehouse_box__step__title">{{ __('warehouse-info.add_all_packages_to_the_vehicle') }}</p>
                {{ __('warehouse-info.no_possibility_to_add_packages', ['name' => $warehouse->name]) }}<br/>
                <a href="{{ route('orders.choose-warehouse', ['parcels', 'service' => 'shipment']) }}" target="_blank" class="btn btn-red warehouse_box__step__btn">
                    {{ __('warehouse-info.add') }}
                </a>
            @else
                <p class="warehouse_box__step__title">{{ __('warehouse-info.packages_added_to_the_warehouse') }}</p>
                {{ __('warehouse-info.can_be_assigned_for_shipping') }}<br/>
                <a href="{{ route('orders.choose-warehouse', ['parcels', 'service' => 'shipment']) }}" target="_blank" class="btn btn-red warehouse_box__step__btn">
                    {{ __('warehouse-info.send') }}
                </a>
            @endif
        </div>
    </div>
</div>
