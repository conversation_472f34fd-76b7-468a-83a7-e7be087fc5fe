@php
    /** @var \App\Models\Interfaces\Messageable $messageable */
    if (!$messageable->relationLoaded('messages')) {
        throw new \LogicException('The messages relation must be loaded on the messageable model.');
    }
@endphp
<div>
    <div class="box-messages" id="messages">
        <h2 class="text-center">{{ __('chat.messages') }}</h2>

        <div class="row">
            <div class="col-xs-offset-1 col-xs-10">
                @foreach($messageable->messages as $message)
                    <div class="message message-{{ $message->type }}">
                        <p class="date">{{ $message->created_at }}</p>
                        <div class="message-container">
                            <p class="name">{{ $message->user->full_name }}</p>
                            <p class="content">{!! nl2br(e($message->content)) !!}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="box-messages-reply">
        <div class="row">
            <div class="col-xs-offset-1 col-xs-10">
                {!! Former::openVertical()->action(route('messages.store'))->method('POST') !!}
                {!! Former::hidden('messageable_id')->value($messageable->id) !!}
                {!! Former::hidden('messageable_type')->value(get_class($messageable)) !!}
                {!! Former::textarea('content', __('chat.write_message'))->append('<button class="btn btn-red" type="submit">' . __('chat.send') . '</button>')->required() !!}
                {!! Former::close() !!}
            </div>
        </div>
    </div>
</div>
