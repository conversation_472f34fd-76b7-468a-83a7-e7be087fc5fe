<!DOCTYPE html>
<html lang="{{ App::getLocale() }}">
  <head>
    @if (! empty(config('auction.sentry.js_dns')))
      <script
        src="https://browser.sentry-cdn.com/5.6.1/bundle.min.js"
        integrity="sha384-pGTFmbQfua2KiaV2+ZLlfowPdd5VMT2xU4zCBcuJr7TVQozMO+I1FmPuVHY3u8KB"
        crossorigin="anonymous"
      ></script>
      <script>
        Sentry.init({ dsn: '{{ config('auction.sentry.js_dns') }}' });
      </script>
    @endif

    @if (config('app.env') == 'production')
      <!-- Google tag (gtag.js) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZH89Z7YP79"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() {
          dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-ZH89Z7YP79');
      </script>
    @endif

    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" />
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    {!! app(\App\Project\Core\HrefLangGenerator::class)() !!}

    @if (! empty(Meta::get('title')))
      <title><?= Meta::get('title') ?> | {{ config('site.name') }}</title>
    @else
      <title>{{ config('site.name') }}</title>
    @endif

    <?= Meta::tag('robots') ?>

    <?= Meta::tag('site_name', config('site.name')) ?>

    <?= Meta::tag('url', Request::url()) ?>

    <?= Meta::tag('locale', 'pl_PL') ?>

    <?= Meta::tag('description') ?>

    <?= Meta::tag('image', asset('img/og_image.jpg')) ?>

    <?= Meta::tag('image') ?>

    <script>
      WebFontConfig = {
        google: {
          families: [
            'Open Sans:400,600,700,600italic,400italic,700italic,300italic,300:latin,latin-ext',
            'Rajdhani:400,700:latin,latin-ext',
            'Nunito:400,600,700,900:latin,latin-ext',
          ],
        },
      };

      (function (d) {
        var wf = d.createElement('script'),
          s = d.scripts[0];
        wf.src = 'https://ajax.googleapis.com/ajax/libs/webfont/1.6.16/webfont.js';
        s.parentNode.insertBefore(wf, s);
      })(document);

      window.translations = @json(['common' => trans('common')]);
    </script>
    @if (! empty(config('recaptcha.site_key')))
      <script src="https://www.google.com/recaptcha/api.js?render={{ config('recaptcha.site_key') }}"></script>
    @endif

    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    @yield('stylesheets')
  </head>
  <body>
    @if (config('app.env') === 'production')
      <!-- Google Tag Manager (noscript) -->
      <noscript>
        <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-KWK69SL"
          height="0"
          width="0"
          style="display: none; visibility: hidden"
        ></iframe>
      </noscript>
      <!-- End Google Tag Manager (noscript) -->
    @endif

    {{-- @if(!empty(config('carrierwise.facebook.customer_chat_id'))) --}}
    {{-- <!-- Load Facebook SDK for JavaScript --> --}}
    {{-- <div id="fb-root"></div> --}}
    {{-- <script> --}}
    {{-- window.fbAsyncInit = function() { --}}
    {{-- FB.init({ --}}
    {{-- xfbml            : true, --}}
    {{-- version          : 'v5.0' --}}
    {{-- }); --}}
    {{-- }; --}}

    {{-- (function(d, s, id) { --}}
    {{-- var js, fjs = d.getElementsByTagName(s)[0]; --}}
    {{-- if (d.getElementById(id)) return; --}}
    {{-- js = d.createElement(s); js.id = id; --}}
    {{-- js.src = 'https://connect.facebook.net/pl_PL/sdk/xfbml.customerchat.js'; --}}
    {{-- fjs.parentNode.insertBefore(js, fjs); --}}
    {{-- }(document, 'script', 'facebook-jssdk'));</script> --}}

    {{-- <!-- Your customer chat code --> --}}
    {{-- <div class="fb-customerchat" --}}
    {{-- attribution=setup_tool --}}
    {{-- page_id="{{ config('carrierwise.facebook.customer_chat_id') }}" --}}
    {{-- theme_color="#0084ff" --}}
    {{-- logged_in_greeting="Witaj, jak możemy Ci dzisiaj pomóc?" --}}
    {{-- logged_out_greeting="Witaj, jak możemy Ci dzisiaj pomóc?"> --}}
    {{-- </div> --}}
    {{-- @endif --}}
  </body>
</html>
