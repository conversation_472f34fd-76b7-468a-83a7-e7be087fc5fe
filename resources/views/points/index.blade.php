@extends('layouts.base')

@section('content')
    <div id="points" class="bg-gray">
        <section class="narrow-padded">
            <div class="container">
                <div class="account-balances">
                    <h3>
                      {{ __('points.number_of_points') }}:
                        <strong>
                            {{ auth()->user()->formattedPointsBalance() }}
                        </strong>
                    </h3>

                    <h3 class="text-right">
                        @if($progressBarData->missingPoints > 0)
                            {{ __('points.points_left_for_next_discount', ['missingPoints' => $progressBarData->missingPoints]) }}
                        @else
                            {!! __('points.can_get_discount_next_order') !!}
                        @endif
                    </h3>
                </div>

                <div class="progress" style="margin-top: 20px;">
                    <div class="progress-bar" role="progressbar" aria-valuenow="{{ $progressBarData->pointsPercentage }}"
                         aria-valuemin="0" aria-valuemax="100" style="width:{{ $progressBarData->pointsPercentage }}%">
                        <strong>
                            {{ $progressBarData->pointsPercentage }}%
                        </strong>
                    </div>
                </div>

                <div class="page-actions">
                    <div class="page-actions__btns">
                        <a href="{{ route('points.info') }}" class="btn btn-red">
                          {{ __('points.how_does_it_work') }}
                        </a>
                    </div>

                    <div class="page-actions__filters">
                        {!! Former::openInline()->method('GET')->id('filters')->action(route('points.index')) !!}
                        {!! Former::select('amount_type')->options(['all' => __('points.outgoings_and_income'), 'outgoings' => __('points.outgoings'), 'income' => __('points.income')])->select('all') !!}
                        {!! Former::text('search')->placeholder(__('points.search'))->prepend('<i class="fa fa-search" aria-hidden="true"></i>') !!}
                        {!! Former::close() !!}
                    </div>
                </div>
                <div class="no-padding">
                    <div class="table-ajax">
                        @include('points.partials.table-content')
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
