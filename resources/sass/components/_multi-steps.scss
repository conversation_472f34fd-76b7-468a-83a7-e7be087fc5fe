/* --------------------------------

Basic Style

-------------------------------- */
.cd-breadcrumb, .cd-multi-steps {
  width: 90%;
  max-width: 768px;
  padding: 0.5em 1em;
  margin: 1em auto;
  background-color: #edeff0;
  border-radius: .25em;
}

.cd-breadcrumb::after, .cd-multi-steps::after {
  clear: both;
  content: "";
  display: table;
}

.cd-breadcrumb li, .cd-multi-steps li {
  display: inline-block;
  float: left;
  margin: 0.5em 0;
}

.cd-breadcrumb li::after, .cd-multi-steps li::after {
  /* this is the separator between items */
  display: inline-block;
  content: '\00bb';
  margin: 0 .6em;
  color: #959fa5;
}

.cd-breadcrumb li:last-of-type::after, .cd-multi-steps li:last-of-type::after {
  /* hide separator after the last item */
  display: none;
}

.cd-breadcrumb li > *, .cd-multi-steps li > * {
  /* single step */
  display: inline-block;
  font-size: 1.4rem;
  color: #2c3f4c;
}

.cd-breadcrumb li.current > *, .cd-multi-steps li.current > * {
  /* selected step */
  color: #ededed;
}

.no-touch .cd-breadcrumb a:hover, .no-touch .cd-multi-steps a:hover {
  /* steps already visited */
  color: #ededed;
}

.cd-breadcrumb.custom-separator li::after, .cd-multi-steps.custom-separator li::after {
  /* replace the default separator with a custom icon */
  content: '';
  height: 16px;
  width: 16px;
  //background: url('../../public/img/cd-custom-separator.svg') no-repeat center center;
  vertical-align: middle;
}

.cd-breadcrumb.custom-icons li > *::before, .cd-multi-steps.custom-icons li > *::before {
  /* add a custom icon before each item */
  content: '';
  display: inline-block;
  height: 20px;
  width: 20px;
  margin-right: .4em;
  margin-top: -2px;
  //background: url('../../public/img/cd-custom-icons-01.svg') no-repeat 0 0;
  vertical-align: middle;
}

.cd-breadcrumb.custom-icons li:not(.current):nth-of-type(2) > *::before, .cd-multi-steps.custom-icons li:not(.current):nth-of-type(2) > *::before {
  /* change custom icon using image sprites */
  background-position: -20px 0;
}

.cd-breadcrumb.custom-icons li:not(.current):nth-of-type(3) > *::before, .cd-multi-steps.custom-icons li:not(.current):nth-of-type(3) > *::before {
  background-position: -40px 0;
}

.cd-breadcrumb.custom-icons li:not(.current):nth-of-type(4) > *::before, .cd-multi-steps.custom-icons li:not(.current):nth-of-type(4) > *::before {
  background-position: -60px 0;
}

.cd-breadcrumb.custom-icons li.current:first-of-type > *::before, .cd-multi-steps.custom-icons li.current:first-of-type > *::before {
  /* change custom icon for the current item */
  background-position: 0 -20px;
}

.cd-breadcrumb.custom-icons li.current:nth-of-type(2) > *::before, .cd-multi-steps.custom-icons li.current:nth-of-type(2) > *::before {
  background-position: -20px -20px;
}

.cd-breadcrumb.custom-icons li.current:nth-of-type(3) > *::before, .cd-multi-steps.custom-icons li.current:nth-of-type(3) > *::before {
  background-position: -40px -20px;
}

.cd-breadcrumb.custom-icons li.current:nth-of-type(4) > *::before, .cd-multi-steps.custom-icons li.current:nth-of-type(4) > *::before {
  background-position: -60px -20px;
}

@media only screen and (min-width: 768px) {
  .cd-breadcrumb, .cd-multi-steps {
    padding: 0 1.2em;
  }
  .cd-breadcrumb li, .cd-multi-steps li {
    margin: 1.2em 0;
  }
  .cd-breadcrumb li::after, .cd-multi-steps li::after {
    margin: 0 1em;
  }
  .cd-breadcrumb li > *, .cd-multi-steps li > * {
    font-size: 1.6rem;
  }
}

/* --------------------------------

Multi steps indicator

-------------------------------- */
@media only screen and (min-width: 768px) {
  .cd-multi-steps {
    /* reset style */
    background-color: transparent;
    padding: 0;
    text-align: center;
  }

  .cd-multi-steps li {
    position: relative;
    float: none;
    margin: 0.4em 40px 0.4em 0;
  }
  .cd-multi-steps li:last-of-type {
    margin-right: 0;
  }
  .cd-multi-steps li::after {
    /* this is the line connecting 2 adjacent items */
    position: absolute;
    content: '';
    height: 4px;
    background: #edeff0;
    /* reset style */
    margin: 0;
  }
  .cd-multi-steps li.visited::after {
    background-color: #ededed;
  }
  .cd-multi-steps li > *, .cd-multi-steps li.current > * {
    position: relative;
    color: #fff;
  }

  .cd-multi-steps.custom-separator li::after {
    /* reset style */
    height: 4px;
    background: #edeff0;
  }

  .cd-multi-steps.text-center li::after {
    width: 100%;
    top: 50%;
    left: 100%;
    -webkit-transform: translateY(-50%) translateX(-1px);
    -moz-transform: translateY(-50%) translateX(-1px);
    -ms-transform: translateY(-50%) translateX(-1px);
    -o-transform: translateY(-50%) translateX(-1px);
    transform: translateY(-50%) translateX(-1px);
  }
  .cd-multi-steps.text-center li > * {
    z-index: 1;
    padding: .6em 1em;
    border-radius: .25em;
    background-color: #edeff0;
  }
  .no-touch .cd-multi-steps.text-center a:hover {
    background-color: #2c3f4c;
  }
  .cd-multi-steps.text-center li.current > *, .cd-multi-steps.text-center li.visited > * {
    color: #ffffff;
    background-color: #ededed;
  }
  .cd-multi-steps.text-center.custom-icons li.visited a::before {
    /* change the custom icon for the visited item - check icon */
    background-position: 0 -60px;
  }

  .cd-multi-steps.text-top li, .cd-multi-steps.text-bottom li {
    width: 80px;
    text-align: center;
  }
  .cd-multi-steps.text-top li::after, .cd-multi-steps.text-bottom li::after {
    /* this is the line connecting 2 adjacent items */
    position: absolute;
    left: 50%;
    /* 40px is the <li> right margin value */
    width: calc(100% + 40px);
  }
  .cd-multi-steps.text-top li > *::before, .cd-multi-steps.text-bottom li > *::before {
    /* this is the spot indicator */
    content: '';
    position: absolute;
    z-index: 1;
    left: 50%;
    right: auto;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background-color: #bec1c6;
    border: 10px solid #edeff0;
  }
  .cd-multi-steps.text-top li.visited > *::before,
  .cd-multi-steps.text-top li.current > *::before, .cd-multi-steps.text-bottom li.visited > *::before {
    background-color: #bec1c6;
    border: 10px solid #ededed;
  }
  .cd-multi-steps.text-bottom li.current > *::before {
    background-color: $color-blue;
    border: 10px solid #ededed;
  }
  .no-touch .cd-multi-steps.text-top a:hover, .no-touch .cd-multi-steps.text-bottom a:hover {
    color: #ededed;
  }
  .no-touch .cd-multi-steps.text-top a:hover::before, .no-touch .cd-multi-steps.text-bottom a:hover::before {
    box-shadow: 0 0 0 3px rgba(150, 192, 61, 0.3);
  }

  .cd-multi-steps.text-top li::after {
    /* this is the line connecting 2 adjacent items */
    bottom: 4px;
  }
  .cd-multi-steps.text-top li > * {
    padding-bottom: 20px;
  }
  .cd-multi-steps.text-top li > *::before {
    /* this is the spot indicator */
    bottom: 0;
  }

  .cd-multi-steps.text-bottom li::after {
    /* this is the line connecting 2 adjacent items */
    top: 3px;
  }
  .cd-multi-steps.text-bottom li > * {
    padding-top: 20px;
  }
  .cd-multi-steps.text-bottom li > *::before {
    /* this is the spot indicator */
    top: 0;
  }
}

/* --------------------------------

Add a counter to the multi-steps indicator

-------------------------------- */
.cd-multi-steps.count li {
  counter-increment: steps;
}

.cd-multi-steps.count li > *::before {
  content: counter(steps) " - ";
}

@media only screen and (min-width: 768px) {
  .cd-multi-steps.text-top.count li > *::before,
  .cd-multi-steps.text-bottom.count li > *::before {
    /* this is the spot indicator */
    content: counter(steps);
    height: 75px;
    width: 75px;
    line-height: 52px;
    font-size: 23px;
    color: #ffffff;
  }

  .cd-multi-steps.text-top.count li:not(.current) em::before,
  .cd-multi-steps.text-bottom.count li:not(.current) em::before {
    /* steps not visited yet - counter color */
    color: #fff;
  }

  .cd-multi-steps.text-top.count li::after {
    bottom: 11px;
  }

  .cd-multi-steps.text-top.count li > * {
    padding-bottom: 34px;
  }

  .cd-multi-steps.text-bottom.count li::after {
    top: 31px;
  }

  .cd-multi-steps.text-bottom.count li > * {
    padding-top: 34px;
  }
}

/*
    Common
*/

.wizard,
.tabcontrol {
  display: block;
  width: 100%;
  overflow: hidden;
}

.wizard a,
.tabcontrol a {
  outline: 0;
}

.wizard ul,
.tabcontrol ul {
  list-style: none !important;
  padding: 0;
  margin: 0;
  text-align: center;
}

.wizard ul > li,
.tabcontrol ul > li {
  display: block;
  padding: 0;
}

/* Accessibility */
.wizard > .steps .current-info,
.tabcontrol > .steps .current-info {
  position: absolute;
  left: -999em;
}

.wizard > .content > .title,
.tabcontrol > .content > .title {
  position: absolute;
  left: -999em;
}

/*
    Wizard
*/

.wizard > .steps {
  position: relative;
  display: block;
  width: 100%;
}

.wizard.vertical > .steps {
  display: inline;
  float: none;
  width: 30%;
}

.wizard > .steps .number {
  font-size: 1.429em;
}

.wizard > .steps > ul > li {
  width: 150px;
  position: relative;
  z-index: 2;
  text-align: left;

  &:last-child {
    width: auto;
  }

  &::after {
    /* this is the line connecting 2 adjacent items */
    position: absolute;
    content: '';
    height: 2px;
    background: #edeff0;
    /* reset style */
    margin: 0;
    left: 50%;
    top: 35px;
    width: calc(100% + 0px);
    z-index: -1;
  }

  &:last-child {
    &:after {
      content: none;
    }
  }
}

.wizard > .steps > ul > li,
.wizard > .actions > ul > li {
  float: none;
  display: inline-block;
}

.wizard.vertical > .steps > ul > li {
  float: none;
  width: 100%;
}

.wizard > .steps a,
.wizard > .steps a:hover,
.wizard > .steps a:active {
  display: block;
  width: auto;
  margin: 0 0.5em 0.5em;
  padding: 1em 1em;
  text-decoration: none;

  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}

.wizard > .steps .current a,
.wizard > .steps .current a:hover,
.wizard > .steps .current a:active,
.wizard > .steps .disabled a,
.wizard > .steps .disabled a:hover,
.wizard > .steps .disabled a:active,
.wizard > .steps .done a,
.wizard > .steps .done a:hover,
.wizard > .steps .done a:active {
  background: #bec1c6;
  color: #fff;
  cursor: default;
  display: inline-block;
  border-radius: 50%;
  font-size: 23px;
  border: 10px solid #eaeaea;
  font-weight: bold;
  padding: 0;
  margin: 0;
  width: 75px;
  height: 75px;
  text-align: center;
  line-height: 52px;
}

.wizard > .steps .current a,
.wizard > .steps .current a:hover,
.wizard > .steps .current a:active {
  background: $color-blue;
}

.wizard > .steps .error a,
.wizard > .steps .error a:hover,
.wizard > .steps .error a:active {
  background: $color-red;
  color: #fff;
}

.wizard > .content {
  display: block;
  overflow: hidden;
  min-height: 0;
  position: relative;
  width: auto;
  padding-bottom: 50px;
}

.wizard > .content > .body {
  width: 100%;
  height: auto;
  padding: 25px;
}

.wizard > .content > .body ul {
  list-style: disc !important;
}

.wizard > .content > .body ul > li {
  display: list-item;
}

.wizard > .content > .body > iframe {
  border: 0 none;
  width: 100%;
  height: 100%;
}

.wizard > .content > .body input {
  display: block;
  border: 1px solid #ccc;
}

.wizard > .content > .body input[type="checkbox"] {
  display: inline-block;
}

.wizard > .content > .body input.error {
  background: rgb(251, 227, 228);
  border: 1px solid #fbc2c4;
  color: #8a1f11;
}

.wizard > .content > .body label {
  display: inline-block;
  margin-bottom: 0.5em;
}

.wizard > .content > .body label.error {
  color: #8a1f11;
  display: inline-block;
  margin-left: 1.5em;
}

.wizard > .actions {
  position: relative;
  display: block;
  text-align: right;
  width: 100%;
  padding-right: 25px;
}

.wizard > .actions > ul {
  display: inline-block;
  text-align: right;
}

.wizard > .actions > ul > li {
  margin: 0 0.5em;
}

.wizard.vertical > .actions > ul > li {
  margin: 0 0 0 1em;
}

.wizard > .actions a,
.wizard > .actions a:hover,
.wizard > .actions a:active {
  @extend .btn, .btn-red;
}

.wizard > .actions a[href^="#previous"],
.wizard > .actions a[href^="#previous"]:hover,
.wizard > .actions a[href^="#previous"]:active {
  @extend .btn, .btn-black;
}

.wizard > .actions .disabled a,
.wizard > .actions .disabled a:hover,
.wizard > .actions .disabled a:active {
  background: #eee;
  color: #aaa;
}

.wizard > .loading {
}

.wizard > .loading .spinner {
}

/*
    Tabcontrol
*/

.tabcontrol > .steps {
  position: relative;
  display: block;
  width: 100%;
}

.tabcontrol > .steps > ul {
  position: relative;
  margin: 6px 0 0 0;
  top: 1px;
  z-index: 1;
}

.tabcontrol > .steps > ul > li {
  float: left;
  margin: 5px 2px 0 0;
  padding: 1px;

  -webkit-border-top-left-radius: 5px;
  -webkit-border-top-right-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-topright: 5px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.tabcontrol > .steps > ul > li:hover {
  background: #edecec;
  border: 1px solid #bbb;
  padding: 0;
}

.tabcontrol > .steps > ul > li.current {
  background: #fff;
  border: 1px solid #bbb;
  border-bottom: 0 none;
  padding: 0 0 1px 0;
  margin-top: 0;
}

.tabcontrol > .steps > ul > li > a {
  color: #5f5f5f;
  display: inline-block;
  border: 0 none;
  margin: 0;
  padding: 10px 30px;
  text-decoration: none;
}

.tabcontrol > .steps > ul > li > a:hover {
  text-decoration: none;
}

.tabcontrol > .steps > ul > li.current > a {
  padding: 15px 30px 10px 30px;
}

.tabcontrol > .content {
  position: relative;
  display: inline-block;
  width: 100%;
  overflow: hidden;
  border-top: 1px solid #bbb;
  padding-top: 20px;
}

.tabcontrol > .content > .body {
  float: left;
  position: absolute;
  width: 95%;
  height: 95%;
  padding: 2.5%;
}

.tabcontrol > .content > .body ul {
  list-style: disc !important;
}

.tabcontrol > .content > .body ul > li {
  display: list-item;
}

@media (max-width: 998px) {
  .wizard > .steps > ul > li {
    width: 86px !important;

    &::after {
      display: none !important;
    }
  }
}
