@use "sass:math";

.image-gallery {
  &--scroll-type {
    .image-gallery__image {
      height: 180px;
      width: 320px;
    }
  }

  &__scroll {
    width: 100%;
    display: flex;
    overflow-x: auto;
  }

  &__image {
    margin: 0 4px 8px;
    height: 90px;
    width: 160px;
    flex: 0 0 auto;

    &:last-of-type {
      margin-right: 0;
    }

    &:first-of-type {
      margin-left: 0;
    }

    a {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center center;
      display: block;
      cursor: zoom-in;
    }

    &--big {
      width: 100%;
      margin-top: 0;
      margin-bottom: 8px;
      margin-right: 0;
      height: auto;

      &::after {
        display: none;
      }

      a {
        height: auto;
        width: 100%;

        img {
          width: 100%;
          height: auto;
          max-height: 700px;
          object-fit: cover;
        }
      }
    }

    &--placeholder {
      margin: 0;

      a {
        cursor: default;
      }
    }
  }

  @media print {
    .image-gallery__image:not(.image-gallery__image--big) {
      display: none;
    }
  }
}
