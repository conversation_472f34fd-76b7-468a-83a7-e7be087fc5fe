.notice {
  border-radius: 10px;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  height: 100%;
  position: relative;
  padding: 30px 25px 80px;

  &__title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  &__date {
    color: #85878a;
    font-size: 14px;
  }

  &__separator {
    width: 30px;
    height: 4px;
    border-radius: 2px;
    background-color: $color-blue;
    border-top: none;
    margin: 10px 0;
  }

  &__content {
    line-height: 1.5em;
    word-break: break-word;

    p:last-child {
      margin-bottom: 0;
    }
  }

  &__show-more-btn {
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    bottom: 10px;
  }
}

.notices {
  &__carousel {
    margin-top: 50px;
    visibility: hidden;
    padding: 0 15px;

    @media (min-width: 1250px) {
        padding: 0;
    }

    &.slick-initialized {
      visibility: visible;
    }

    .slick-track {
      display: flex !important;
    }

    .slick-slide {
      height: auto;
    }
  }

  &__carousel-btn {
    border: 0;
    position: absolute;
    font-size: 30px;
    top: 50%;
    margin-top: -15px;
    background: transparent;
    opacity: 0.6;
    transition: opacity .4s;
    padding: 0;

    &.slick-disabled {
      display: none !important;
    }

    &:hover, &:active {
      opacity: 1;
    }

    &--next {
      right: 5px;

      @media (min-width: 1250px) {
        right: -40px;
      }
    }

    &--prev {
      left: 5px;

      @media (min-width: 1250px) {
        left: -40px;
      }
    }
  }
}
