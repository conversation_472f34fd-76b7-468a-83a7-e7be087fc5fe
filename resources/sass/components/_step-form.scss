.step-form {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;

  ul[role=tablist] {
    list-style: none;
    display: flex;
    padding: 0;
    margin: 60px 0;

    li {
      flex-grow: 1;
      padding: 20px 5px 20px 45px;
      background-color: #fff;
      margin-right: 1px;
      box-shadow: 2px 2px 2px 0px rgba(0, 0, 0, 0.08);

      &:first-of-type {
        border-top-left-radius: $step-form-border-radius;
        border-bottom-left-radius: $step-form-border-radius;
      }

      &:last-of-type {
        border-bottom-right-radius: $step-form-border-radius;
        border-top-right-radius: $step-form-border-radius;
        margin-right: 0;
      }

      &.current {
        a {
          color: $color-red;
        }
      }

      a {
        cursor: default;
        font-size: 20px;
        font-weight: bold;
        color: $text-color;
        transition: color .3s;

        &:hover, &:focus {
          text-decoration: none;
        }

        span {
          display: block;
          font-size: 14px;
          font-weight: normal;
        }

        span.current-info {
          display: none;
        }
      }
    }
  }

  ul[role=menu] {
    list-style: none;
    padding: 0;
    margin: 0;
    float: right;

    li {
      display: inline-block;
      a {
        @extend .btn;

        &[href="#next"] {
          @extend .btn-blue;
        }

        &[href="#finish"] {
          @extend .btn-blue;
        }

        &[href="#previous"] {
          margin-right: 35px;
          @extend .btn-black;
        }
      }

      &.disabled {
        a {
          @extend .btn-disabled;
        }
      }
    }
  }

  &__title {
    display: none;
  }
}
