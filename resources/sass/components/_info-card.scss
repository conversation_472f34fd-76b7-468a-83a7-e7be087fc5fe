.info-card {
  border-radius: 10px;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.04);
  padding: 50px 15px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  height: 100%;

  @media (min-width: $screen-md-min) {
    padding: 50px 40px;
    min-height: 445px;
  }

  @media (min-width: $screen-lg-min) {
    min-height: 400px;
  }

  &__header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    min-height: 100px;
    flex-direction: column;
    text-align: center;

    @media (min-width: $screen-md-min) {
      flex-direction: row;
      text-align: left;
    }
  }

  &__body {
    text-align: center;

    @media (min-width: $screen-md-min) {
      text-align: left;
    }
  }

  &__icon {
    height: 97px;
    width: auto;

    path {
      fill: #27518d;
    }
  }

  &__text {
    display: block;
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 10px;

    &--mb-0 {
      @media (min-width: $screen-md-min) {
        margin-bottom: 0 !important;
      }
    }

    &--big {
      font-size: 35px;
      margin-bottom: 20px;
      line-height: 45px;

      @media (min-width: $screen-md-min) {
        font-size: 45px;
        line-height: 55px;
      }
    }

    &--small__bold {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 0;
    }

    &--small {
      font-size: 16px;
      font-weight: normal;
      margin-bottom: 0;
    }

    &--gray {
      color: #878788;
      font-size: 14px;
      font-weight: normal;
    }
  }

  &__footer {
    padding: 0 0 10px;
    display: flex;
    flex-direction: column;
    margin-top: auto;

    .btn {
      display: block;
      margin-left: auto;
      margin-right: auto;

      @media (min-width: $screen-md-min) {
        margin-right: auto;
        margin-left: inherit;
      }

      &:last-of-type {
        margin-top: 10px;
      }
    }
  }

  .circle-progress-bar {
    margin-bottom: 15px;
    @media (min-width: $screen-md-min) {
      margin-right: 15px;
      margin-bottom: 0;
    }
    @media (min-width: $screen-lg-min) {
      margin-right: 25px;
    }
  }
}

.info-cards {
  @media (min-width: $screen-sm-min) {
    display: flex;
  }
}
