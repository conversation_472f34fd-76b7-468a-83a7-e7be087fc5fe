section {
  padding: 100px 0;
  border-bottom: 1px solid #dadbdc;

  &.narrow {
    margin-top: 35px;
    padding-bottom: 35px;
    padding-top: 0;
  }

  &.narrow-padded {
    padding-bottom: 35px;
    padding-top: 35px;
  }

  &.narrow-padded-top {
    padding-bottom: 0;
    padding-top: 35px;
  }

  &.borderless {
    border-bottom: 0;
  }

  &:last-of-type {
    border-bottom: 0;
  }

  .lead {
    font-size: 26px;
    font-weight: bold;
  }

  .text-block {
    margin-bottom: 35px;

    .btn {
      margin-right: 15px;
    }
  }

  .desc {
    font-size: 16px;
  }
}

.section {
  &--gray-gradient {
    background: linear-gradient(to right, #e2e6e9, #f2f6f9);
  }

  &--blue {
    background: $color-blue;
    color: white;
  }

  &--blue-border-bottom {
    border-bottom: 1px solid lighten($color-blue, 20) !important;
  }
}
