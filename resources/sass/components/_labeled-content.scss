@use "sass:math";

.labeled-content {
  $label-line-height: 28px;
  $horizontal-space: 21px;
  $label-line-space: 3px;

  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  padding: 20px $horizontal-space 14px $horizontal-space;
  border: 1px solid #e1e1e1;
  border-radius: $border-radius;
  position: relative;
  margin-bottom: 30px;
  min-height: 58px;

  &__label {
    font-size: 20px;
    font-weight: 900;
    color: $color-blue;
    position: absolute;
    margin-bottom: 0;
    left: $horizontal-space - $label-line-space;
    line-height: $label-line-height;
    top: - math.div($label-line-height, 2);
    background-color: white;
    padding: 0 3px;
  }

  &__content {
    font-size: 16px;
    line-height: 1.38;
  }
}
