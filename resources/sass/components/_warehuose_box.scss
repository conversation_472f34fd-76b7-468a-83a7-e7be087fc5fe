.warehouse_box {
  color: black;

  &__heading {
    padding: 40px 15px;
    background: white !important;
    display: flex;
    border-bottom: 1px solid #d9dbde !important;
    border-top: 2px solid #31599c !important;

    @media (min-width: $screen-sm-min) {
      padding: 40px 70px;
    }

    &__info {
      color: black;
      width: 100%;
    }
  }

  &__content {
    font-size: 20px;
    padding: 50px 15px;
    font-weight: 300;
    min-height: 415px;

    @media (min-width: $screen-sm-min) {
      padding: 50px 70px;
    }

    &--slim {
      min-height: 320px;
    }

    .btn {
      margin-top: 40px;
    }
  }

  &__title {
    font-size: 36px;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 0;
    white-space: nowrap;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__shipping_info {
    font-size: 0.5em;
    margin-left: 5px;
  }

  &__symbol {
    background-color: #31599c;
    color: white;
    width: 68px;
    height: 68px;
    border-radius: 50%;
    font-size: 45px;
    line-height: 68px;
    text-align: center;
    font-weight: 900;
  }

  &__address {

  }

  &__block_info {
    margin-bottom: 29px;
  }

  &__section {
    background-color: #c42422;
    font-size: 20px;
    font-weight: bold;
    line-height: 1.2;
    color: white;
    text-align: center;
    padding: 28px 0;
  }

  &__step {
    display: flex;
    padding: 25px 40px;
    border-bottom: solid 1px #ebedf0;

    &__icon {
      margin-right: 30px;
      flex: 0 0 45px;

      svg path {
        fill: #c42422;
      }
    }

    &__title {
      font-size: 20px;
      font-weight: bold;
      line-height: 1.2;
      margin-bottom: 5px;
    }

    &__btn {
      font-weight: bold;
      font-size: 14px;
      padding-top: 7px;
      padding-bottom: 7px;
      margin-top: 15px;
      margin-right: 5px;
    }
  }

  &__btn {
    font-size: 16px;

    .fa {
      margin-left: 13px;
      vertical-align: middle;
    }
  }

  &__user {
    margin-bottom: 28px;
  }

  &--disabled {
    .warehouse_box {
      &__symbol {
        background-color: #cbcdd0;
      }

      &__address {
        color: #cbcdd0;
      }
    }
  }
}
