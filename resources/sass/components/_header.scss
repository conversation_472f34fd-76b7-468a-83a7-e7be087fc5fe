header.normal {
  background: white;
  min-height: 80px;
  position: relative;
  z-index: 5;
  color: $text-color;
  font-family: 'Nunito', sans-serif;

  .logo-wrapper {
    float: left;

    .logo {
      max-width: 100px;

      @media (min-width: $screen-sm-min) {
        max-width: 200px;
      }
    }
  }

  .right {
    display: flex;
    flex-direction: row;
    height: 100%;
    margin-left: auto;
  }

  .container {
    height: 80px;
  }

  .header-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
  }

  p {
    margin: 0;
  }

  .name {
    font-weight: bold;
    font-size: 18px;
    text-align: right;
  }

  .user-info {
    text-align: right;
    font-size: 13px;
  }

  .user {
    height: 100%;
    display: none;
    justify-content: center;
    flex-direction: column;

    @media (min-width: $screen-sm-min) {
      display: flex;
    }
  }

  .menu-items {
    display: flex;
    margin-right: -15px;

    @media (min-width: $screen-sm-min) {
      margin-right: auto;
    }

    .dropdown:first-of-type .menu-item {
      border-left: 1px solid $border-dark-color;
      margin-left: 0;

      @media (min-width: $screen-sm-min) {
        margin-left: 20px;
      }

      @media (min-width: $screen-md-min) {
        margin-left: 38px;
      }
    }

    .dropdown-menu {
      max-height: 400px;
      overflow: auto;
      background-clip: border-box;
      left: -120%;

      @media (min-width: $screen-sm-min) {
        left: 0;
      }

      li > a {
        padding: 4px 15px;
      }
    }
  }

  .menu-item {
    border-right: 1px solid $border-dark-color;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    padding: 0;

    @media (min-width: $screen-sm-min) {
      width: 60px;
    }

    @media (min-width: $screen-md-min) {
      width: 75px;
    }

    &:hover, &:focus, &:active {
      text-decoration: none;
    }

    svg, .svg {
      height: 30px;
      width: auto;
    }

    &.settings path {
      fill: #c1c1c1;
    }

    &.logout path {
      fill: #d12b2b;
    }

    &.admin path {
      fill: $color-blue;
    }

    &.notifications {
      color: $color-blue;
      font-size: 30px;
    }
  }
}

#page_landing {
  padding-top: 77px;

  .header_top {
    transition: .5s all;
    position: fixed;
    left: 0;
    top: 0;
    padding-top: 15px;
    padding-bottom: 15px;
    z-index: 999;
    @include center-absolute();
    background: #1f2124;

    .dropdown {
      .dropdown-menu {
        a {
          color: #1f2124;
        }
      }
    }

    &.expanded {
      transition: .5s all;

      &:before {
        opacity: 1;

        @media (max-width: 998px) {
          height: 100%;
        }
      }
    }

    &.shrink {
      padding-top: 10px;
      padding-bottom: 10px;

      .mobile-menu #toggleMobileMenu{
        top: 23px;
      }
    }

    .img-responsive {
      max-height: 47px;
    }

    a, .btn-link {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      margin-top: 0;
      display: block;
      padding: 0;

      &:hover, &:focus, &:active {
        text-decoration: none;
      }
    }

    i {
      font-size: 20px;
    }

    ul.landing-nav {
      padding: 0;
      margin: 13px 0 0;
      list-style-type: none;
      text-align: right;

      li {
        display: inline-block;
        margin-right: 55px;

        &:last-of-type {
          margin-right: 0;
        }
      }
    }

    .header_pagemenu {
      background: $color-blue;
      height: 50px;
      position: absolute;
      top: -50px;
      width: 100%;
      transition: .5s all;

      &.expanded {
        transition: .5s all;
      }

      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        font-size: 0;

        li {
          position: relative;
          display: inline-block;
          margin: 0;
          border-left: 1px solid #254b81;

          ul {
            display: none;
            position: absolute;
            top: 50px;
            right: 0;
            width: 200px;
            background: $color-blue;
            z-index: 999999;
            max-height: 320px;
            overflow: auto;

            li {
              border-bottom: 1px solid #254b81;
              display: block;
            }
          }

          &:last-child {
            border-right: 1px solid #254b81;
          }

          &:hover {
            background-color: #254b81;

            ul {
              display: block;
            }
          }

          a, .btn-link {
            margin: 0;
            padding: 16px 15px;
            font-size: 13px;
            font-weight: 400;
            display: block;

            i {
              margin-left: 5px;
              font-size: 10px;
            }
          }
        }
      }
    }

    .mobile-menu {
      height: 100%;

      .container {
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
      }

      #toggleMobileMenu {
        position: absolute;
        top: 28px;
        right: 15px;
        transition: top .5s;
      }

      &-container {
        position: absolute;
        top: 0;
        left: -1000px;
        background: #28282d;
        width: 100%;
        height: 100%;
        height: 100vh;
        overflow: auto;

        .exit {
          background: $color-blue;

          i {
            margin-right: 10px;
          }

          a {
            margin: 0;
            padding: 15px 0 15px 40px;
            display: block;
          }
        }

        .title {
          color: #fff;
          background: #1f1f23;
          font-size: 20px;
          font-weight: 600;
          padding-left: 40px;
          padding-top: 15px;
          padding-bottom: 15px;

          i {
            margin-right: 10px;
          }
        }

        ul {
          list-style-type: none;
          padding: 0;
          margin: 0;

          li {
            border-bottom: 1px solid #121214;
            border-top: 1px solid #39393d;
            padding-left: 40px;

            a, .btn-link {
              padding: 15px 0;
              margin: 0;
              display: block;
            }
          }
        }
      }
    }
  }

  .header_calculators {
    display: none;

    .navbar {
      margin: 0;

      .nav > li {
        display: inline-block;
        max-width: 140px;

        @media (max-width: 998px) {
          width: 32%;
          max-width: 100%;
        }
      }
    }

    ul li {
      a {
        text-align: center;
        color: #fff;
        font-size: 15px;
        padding: 15px 8px;
        font-weight: normal;

        &:hover, &:focus, &:active {
          background-color: transparent;
        }
      }

      i {
        font-size: 32px;
        margin-bottom: 5px;
      }
    }

    .icon {
      svg {
        max-width: 32px;
        max-height: 32px;
        fill: #fff !important;
      }
    }
  }
}
