.modal-register, .modal-login {
  background: url('../../public/img/flag_overlay.png') 50% 50% no-repeat;
  background-size: cover;

  .modal-body {
    padding: 15px 15px 60px;
  }

  .logo-sign {
    margin: 45px auto;
  }

  .related-link {
    margin-bottom: 5px;
    display: block;
    color: #797d83;

    &:first-of-type {
      margin-top: 20px;
    }
  }
}

.modal {
  .close {
    color: #151515;
    opacity: 1;
    font-size: 31px;
    margin-right: 10px;
  }
}

.modal_form {
  &__dialog {
    @media (min-width: $screen-sm-min) {
      width: 540px;
    }
  }

  &__body {
    padding: 60px 55px;

    .form-group {
      margin-bottom: 30px;

      label {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  &__title {
    font-size: 26px;
    font-weight: bold;
    margin-bottom: 23px;
  }

  &__desc {
    font-size: 16px;
    line-height: 1.63;
    margin-bottom: 30px;
  }

  &__footer {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.63;
    margin-bottom: 40px;
  }

  &__close {
    width: 35px;
    height: 35px;
    position: absolute;
    top: -15px;
    right: -19px;
    background: white !important;
    border-radius: 50%;
    border: solid 1px #c5c5c5 !important;
  }
}

.modal-xl {
  @media (min-width: $screen-sm) {
    width: 90%;
  }
}

.modal.loading .modal-dialog {
  &::before {
    font-family: FontAwesome;
    content: "\f110";
    display: block;
    position: absolute;
    z-index: 999;
    height: 50px;
    width: 50px;
    font-size: 50px;
    line-height: 50px;
    overflow: visible;
    animation: rotating 2s linear infinite;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }

  &::after {
    display: block;
    content: '';
    z-index: 998;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(128, 128, 128, 0.3);
  }
}

#track-package-modal {
  text-align: center;
  padding: 0 !important;

  &::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px;
  }

  .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }

  .package-info {
    font-size: 17px;
    min-height: 300px;

    li {
      margin-bottom: 5px;
    }

    .fa {
      display: none;
    }

    .alert {
      margin-bottom: 0;
    }

    &.loading {
      font-size: 40px;
      display: flex;
      justify-content: center;
      align-items: center;

      .fa {
        display: inline-block;
      }
    }
  }
}
