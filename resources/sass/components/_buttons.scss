.btn {
  font-size: 16px;
  padding: 10px 20px;

  strong {
    font-weight: 600;
  }
}

.btn-sm {
  font-size: 16px;
  padding: 6px 22px;
}

.btn-icon {
  margin-left: 5px;
  font-size: 1.3em !important;
  margin-top: -2px;
  vertical-align: middle;
}

.btn-blue {
  &, &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
    @include blue-gradient();
    color: #fff;
    outline: 0;
  }
}

.btn-group > .btn-blue {
  &:first-of-type {
    z-index: 2;
    border-right: 1px solid lighten($color-blue, 50);
  }

  &:last-of-type {
    &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
      z-index: 0;
    }
  }
}

.btn-red {
  &, &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
    @include red-gradient();
    color: #fff;
  }
}

.btn-black {
  &, &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
    background: #000;
    color: #fff;
  }
}

.btn-gray {
  &, &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
    background-image: linear-gradient(to top, #a9acae, #bfc0c1);
    color: #fff;
  }
}

.btn-white-o {
  color: #fff;
  background: transparent;
  border: 2px solid #fff;
  transition: .5s all;

  &:hover, &:active, &:focus, &:active:focus {
    background: #fff;
    transition: .5s all;
    color: #112241 !important;
  }
}

.btn-blue-o {
  color: #fff;
  background: transparent;
  border: 2px solid $color-blue;

  &:hover, &:active, &:focus, &:active:focus {
    background: $color-blue;
    transition: .5s all;
    color: #fff;
  }
}

.btn-blue-o-t {
  color: $color-blue;
  background: transparent;
  border: 2px solid $color-blue;

  &:hover, &:active, &:focus, &:active:focus {
    color: $color-blue;
    background: transparent;
  }
}

.btn-red-o-t {
  color: $color-red;
  background: transparent;
  border: 2px solid $color-red;

  &:hover, &:active, &:focus, &:active:focus {
    color: $color-red;
    background: transparent;
  }
}

.btn-disabled {
  &, &:hover, &:hover:focus, &:active, &:active:focus, &:focus {
    background: #eee;
    color: #aaa;
  }
}

.btn-file {
  position: relative;
  overflow: hidden;
}
.btn-file input[type=file] {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  font-size: 100px;
  text-align: right;
  filter: alpha(opacity=0);
  opacity: 0;
  outline: none;
  background: white;
  cursor: inherit;
  display: block;
}
