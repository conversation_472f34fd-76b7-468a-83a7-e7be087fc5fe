.notification {
  display: flex;

  &__icon {
    width: 42px;
    height: 42px;
    background: $color-blue;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;

    i {
      color: white;
      font-size: 16px;
      width: 38px;
      height: 38px;
      border-radius: 50%;
      border: 2px solid white;
      text-align: center;
      vertical-align: middle;
      line-height: 38px;
    }
  }

  &__main {
    width: 251px;
    white-space: normal;
  }

  &__date {
    font-size: 12px;
    color: $gray-light;
  }
}

.notifications {
  position: relative;

  &__count {
    position: absolute;
    background: $color-red;
    border-radius: 50%;
    font-size: 9px;
    color: white;
    width: 16px;
    height: 16px;
    text-align: center;
    line-height: 16px;
    font-weight: bold;
    left: 28px;
    top: 40px;

    @media (min-width: $screen-sm-min) {
      left: 38px;
    }
  }
}
