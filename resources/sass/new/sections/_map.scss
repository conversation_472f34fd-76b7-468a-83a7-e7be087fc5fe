.map {
  padding: 64px 16px;
  max-width: 1200px;
  margin: 0 auto;

  @include media-breakpoint-up(md) {
    padding: 100px 40px 40px;
  }

  &__content {
    text-align: center;
    padding-bottom: 40px;

    @include media-breakpoint-up(md) {
      padding-bottom: 64px;
    }

    h2 {
      margin: 0 auto 12px;
    }

    p {
      max-width: 780px;
      margin: 0 auto;
    }
  }

  &__image {
    position: relative;
    width: 100%;
    height: auto;
    aspect-ratio: 2 / 1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  &__marker {
    z-index: 1;
    display: none;
    position: absolute;
    width: 28px;
    height: 28px;
    cursor: pointer;
    transition: 0.3s opacity ease;
    animation: pulse 2s infinite;

    @include media-breakpoint-up(lg) {
      display: block;
    }
  }

  &__tooltip {
    z-index: 1;
    position: absolute;
    background: linear-gradient(71.49deg, #4256e4 0, #041766 85%);
    color: var(--bs-white);
    padding: 14px 16px;
    border-radius: 8px;
    transform: translateX(-50%) translateY(-100%);
    box-shadow: 0 2px 4px 0 #1b1c1d0a;
    opacity: 0;
    animation: fade-in-slide-up 0.3s ease forwards;
    animation-delay: 0s, 0.3s;

    &-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      white-space: nowrap;

      span {
        font-weight: bold;
        font-size: 14px;
        line-height: 24px;
      }

      img {
        width: 24px;
        height: 24px;
      }
    }

    ul {
      list-style-type: none;
      padding: 6px 0 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
      }

      img {
        width: 16px;
        height: 16px;
      }
    }
  }

  &__mobile-list-button {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    @include media-breakpoint-up(lg) {
      display: none;
    }
  }

  &__modal {
    &-overlay {
      position: fixed;
      inset: 0;
      background: rgb(0 0 0 / 50%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1050;
    }

    &-content {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 18px;
        font-weight: 500;
      }
    }

    &-close {
      position: absolute;
      top: 10px;
      right: 5px;
      background: none;
      border: none;

      svg {
        width: 32px;
        height: 32px;
      }
    }

    &-scrollable {
      overflow-y: auto;
      margin-top: 10px;
      padding-right: 10px;
      max-height: 75vh;
    }

    &-title {
      display: flex;
      align-items: center;
      gap: 8px;
      white-space: nowrap;
      line-height: 28px;
      font-size: 14px;

      &--without-icon {
        padding: 10px 0;
      }

      img {
        width: 24px;
        height: 24px;
      }
    }

    &-list {
      list-style-type: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        gap: 8px;
        line-height: 28px;
        font-size: 14px;
      }

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}

.hidden-line {
  visibility: hidden;
}

.visible-line {
  visibility: visible;
}
