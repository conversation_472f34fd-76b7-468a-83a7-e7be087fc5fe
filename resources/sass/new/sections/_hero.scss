.hero {
  @include media-breakpoint-up(md) {
    padding: 0 32px;
  }

  &__container {
    width: 100%;
    min-height: calc(100vh - 72px);
    min-height: calc(100svh - 72px);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px;
    transition: background-image 0.5s ease-in-out;

    @include media-breakpoint-up(md) {
      min-height: max(600px, 70vh);
      min-height: max(600px, 70svh);
      border-radius: 20px;
      padding: 0 32px;
    }

    @include media-breakpoint-up(lg) {
      min-height: max(680px, 70vh);
      min-height: max(680px, 70svh);
    }
  }

  &__tabs {
    display: flex;
    background-color: rgb(255 255 255 / 20%);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    padding: 4px;
    margin: 32px 0 20px;

    @include media-breakpoint-up(md) {
      margin: 64px 0 20px;
    }

    button {
      display: flex;
      align-items: center;
      gap: 6px;
      background: transparent;
      padding: 7px 16px;
      border: 0;
      outline: 0;
      color: var(--bs-white);
      font-size: 12px;
      font-weight: 500;
      border-radius: 8px;
      transition:
        background-color 0.5s,
        box-shadow 0.3s;

      &:focus-visible {
        border: 1px solid var(--bs-white);
      }

      svg {
        display: none;
        color: var(--bs-white);

        @include media-breakpoint-up(xs) {
          display: block;
        }
      }

      &.active {
        background: linear-gradient(180deg, $primary, transparent) shade-color($primary, 30%);
        border-image-source: linear-gradient(
          180deg,
          tint-color($primary, 25%) 0%,
          shade-color($primary, 40%) 100%
        );

        &:hover,
        &:focus-visible {
          background-color: tint-color($primary, 15%);
          box-shadow: 0 4px 10px -1px $primary;
        }
      }
    }
  }

  &__step {
    margin: 0 auto;
    width: min(860px, 100%);

    h1,
    p {
      color: var(--bs-white);
      text-align: center;
    }

    h1 {
      @include media-breakpoint-up(lg) {
        padding: 10px 0 20px;
      }
    }
  }

  &__input,
  &__select {
    width: 100%;
  }

  &__select {
    &--vehicle-type,
    &--type,
    &--make,
    &--model {
      grid-column: 1/3;
    }

    @include media-breakpoint-up(md) {
      &--make {
        grid-column: 1/2;
      }

      &--model {
        grid-column: 2/3;
      }
    }

    @include media-breakpoint-up(lg) {
      &--type {
        min-width: 160px;
      }

      &--make {
        min-width: 180px;
      }

      &--model {
        min-width: 180px;
      }

      &--year-from {
        min-width: 100px;
      }

      &--year-to {
        min-width: 100px;
      }
    }
  }

  &__vehicle-finder-form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin: 36px 0;

    @include media-breakpoint-up(lg) {
      display: flex;
      justify-content: stretch;
      margin: 40px 0 120px;
    }
  }

  &__shipping-rate-form {
    margin: 36px 0;

    @include media-breakpoint-up(lg) {
      margin: 40px 0 120px;
    }

    &__wrapper {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      @include media-breakpoint-up(lg) {
        display: flex;
        justify-content: stretch;
      }
    }
  }

  &__input {
    .form-control {
      padding: 10px;
    }
  }

  &__unit-toggle {
    align-items: center;

    &__label {
      font-size: 12px;
      line-height: 16px;
      color: var(--bs-white);
    }

    .form-check {
      margin-bottom: 0;
    }
  }

  &__unit-toggle-mobile {
    display: flex;

    @include media-breakpoint-up(lg) {
      display: none;
    }
  }

  &__unit-toggle-desktop {
    margin: 8px 0;
    display: none;

    @include media-breakpoint-up(lg) {
      display: flex;
    }
  }

  &__button {
    grid-column: 1/3;
    display: flex;
    justify-content: center;
    white-space: nowrap;
  }
}
