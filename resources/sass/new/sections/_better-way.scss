.better-way {
  padding: 0 16px;

  @include media-breakpoint-up(md) {
    padding: 0 32px;
  }

  &__container {
    width: 100%;
    padding: 32px 20px;
    border-radius: 20px;

    @include media-breakpoint-up(md) {
      padding: 64px 20px;
      background: var(--bs-gray-200);
    }
  }

  &__content {
    text-align: center;
    padding-bottom: 40px;

    @include media-breakpoint-up(md) {
      padding-bottom: 64px;
    }

    h2 {
      margin: 0 auto 12px;
    }

    p {
      max-width: 780px;
      margin: 0 auto;
    }
  }

  &__list {
    max-width: 1080px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;

    @include media-breakpoint-up(lg) {
      grid-template-columns: repeat(3, 1fr);
    }

    @include media-breakpoint-up(xl) {
      gap: 40px;
    }
  }

  &__item {
    width: 100%;
    text-align: center;
    padding-bottom: 42px;

    @include media-breakpoint-up(md) {
      padding-bottom: 0;
    }

    h3 {
      font-size: 24px;
      line-height: 28px;
      margin-top: 24px;
    }

    p {
      max-width: 450px;
      margin: 0 auto;
    }
  }

  &__image {
    height: 104px;
    width: 104px;

    @include media-breakpoint-up(md) {
      height: 120px;
      width: 120px;
    }
  }
}
