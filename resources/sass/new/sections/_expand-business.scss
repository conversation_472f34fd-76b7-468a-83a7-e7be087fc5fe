.expand-business {
  padding: 0 16px;

  @include media-breakpoint-up(md) {
    padding: 0 32px;
  }

  &__container {
    background: var(--bs-gray-200);
    width: 100%;
    padding: 32px 20px;
    border-radius: 20px;

    @include media-breakpoint-up(md) {
      padding: 64px 20px;
    }
  }

  &__content {
    text-align: center;
    padding-bottom: 40px;

    @include media-breakpoint-up(md) {
      padding-bottom: 64px;
    }

    h2 {
      margin: 0 auto 12px;
    }

    p {
      max-width: 780px;
      margin: 0 auto;
    }
  }

  &__list {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    @include media-breakpoint-up(md) {
      gap: 8px;
      grid-template-columns: repeat(3, 1fr);
    }

    @include media-breakpoint-up(lg) {
      gap: 16px;
    }

    @include media-breakpoint-up(xl) {
      gap: 32px;
    }
  }

  &__image {
    overflow: hidden;
    border-radius: 12px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.3s;

    &::before {
      content: '';
      position: absolute;
      bottom: -5%;
      left: -5%;
      right: -5%;
      height: 35%;
      background: rgb(0 0 0 / 50%);
      filter: blur(10px);
    }

    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background: #000;
      opacity: 0.2;
      transition: opacity 0.3s;
    }
  }

  &__item {
    margin: 0 auto;
    overflow: hidden;
    background-color: var(--bs-white);
    padding: 14px;
    border-radius: 12px;
    border: 1px solid var(--bs-gray-300);
    height: 300px;
    width: 100%;

    @include media-breakpoint-up(lg) {
      height: 340px;
    }

    &:hover .expand-business__image {
      transform: scale(1.1);
    }

    &:hover .expand-business__image::after {
      opacity: 0.1;
    }
  }

  &__text {
    position: absolute;
    bottom: 15px;
    left: 20px;
    z-index: 1;

    span {
      display: block;
      text-decoration: none;
    }
  }

  &__name {
    color: var(--bs-white);
    font-weight: 600;
    font-size: 24px;
    line-height: 28px;
    margin-bottom: 4px;
  }

  &__learn-more {
    color: var(--bs-white);
    font-weight: 500;
    font-size: 12px;
    line-height: 24px;
  }
}
