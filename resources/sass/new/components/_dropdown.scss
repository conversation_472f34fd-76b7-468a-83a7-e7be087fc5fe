.dropdown {
  display: flex;
  justify-content: center;

  &__wrapper {
    position: relative;
  }

  &__button {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--bs-white);
    padding: 6px 12px 6px 6px;
    border-radius: 24px;
    border: none;
    cursor: pointer;
    transition: 0.3s background-color;

    &--outlined {
      border: 1px solid var(--bs-gray-300);
    }

    &:hover {
      background-color: var(--bs-gray-200);
    }

    &:focus-visible {
      box-shadow: 0 4px 6px rgb(0 0 0 / 10%);
    }
  }

  &__lang-icon {
    display: flex;
    height: 32px;
    width: 32px;
  }

  &__avatar-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--bs-gray-200);
    border-radius: 50%;
    height: 32px;
    width: 32px;
  }

  &__arrow-icon {
    svg {
      height: 14px;
      width: 14px;
    }
  }

  &__list {
    position: absolute;
    right: 50%;
    transform: translateX(50%);
    margin-top: 8px;
    background-color: var(--bs-white);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgb(0 0 0 / 10%);
    display: flex;
    flex-direction: column;
  }

  &__item {
    display: flex;
    align-items: center;
    background-color: var(--bs-white);

    a,
    button {
      padding: 12px 16px;
      text-decoration: none;
      border: 0;
      font-size: 14px;
      color: var(--bs-black);
      background-color: transparent;
    }

    &:first-of-type {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    &:last-of-type {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    &:hover {
      background-color: var(--bs-primary);

      a,
      button {
        color: var(--bs-white);
      }
    }
  }
}
