.search-container {
  display: flex;
  gap: 5px;
  min-height: 43px;

  &__wrapper {
    width: 100%;
    display: flex;
    flex: 1;
    align-items: center;
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
  }

  &__icon {
    border: 0;
    background: none;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 100%;
    color: var(--bs-gray-500);

    &--close {
      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  input {
    height: 100%;
    border: none;

    &::placeholder {
      color: var(--bs-gray-500);
      font-weight: 400;
    }

    &:focus {
      outline: none;
      border: 0;
    }
  }

  &__button {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }
}
