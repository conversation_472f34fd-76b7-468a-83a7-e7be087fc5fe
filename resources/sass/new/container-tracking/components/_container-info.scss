.details-container {
  font-family: Inter, sans-serif;
  position: absolute;
  width: calc(100% - 20px);
  left: 10px;
  bottom: 0;
  top: 10px;
  z-index: 9999;
  font-size: .75rem;

  @include media-breakpoint-up(md) {
    width: 35%;
    max-width: 500px;
    top: 20px;
    max-height: calc(100vh - 40px);
  }

  @include media-breakpoint-down(md) {
    position: relative;
    border: 1px solid $color-grey-border;
    border-radius: 12px;
    margin: 0 16px 24px;
    left: auto;
    top: auto;
    bottom: auto;
    width: 100%;
  }

  .container-info-parent {
    overflow: hidden;
    transition: max-height 0.3s ease;
    border-radius: 16px;
    border-width: 1px;
    gap: 12px;
    padding: 16px 0;
    background: $color-white;
    height: 100%;

    .container-info-top {
      border-bottom: 1px solid $color-grey-border;
      padding: 0 16px 16px;
      margin-bottom: 16px;

      &__title {
        color: $color-dark-blue;
        font-size: 20px;
        letter-spacing: -0.1px;
        padding: 0;
        line-height: 24px;
        margin: 0;
      }

      &__status {
        border-radius: 4px;
        padding: 4px 6px;
        background: $color-blue-light;
        color: $color-blue;
        font-weight: 500;
        text-transform: capitalize;
        margin: 0;
      }

      &__subtitle {
        margin-top: 16px;
        border-radius: 8px;
        gap: 12px;
        padding: 12px;
        border: 1px solid $color-grey-border;
        background: $color-grey-bg;

        .progress-parent {
          display: flex;
          gap: 8px;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .custom-progress-bar {
            background: linear-gradient(180deg, $color-blue-gradient-start 0%, $color-blue-gradient-end 100%);
          }

          .progress-icons {
            width: 24px;
            height: 24px;
          }
        }

        .destination-info {
          display: flex;
          gap: 8px;
          justify-content: space-between;
          align-items: center;

          &__city {
            font-size: 12px;
            font-weight: 400;
            color: $color-grey;
          }

          &__date {
            font-size: 12px;
            font-weight: 500;
            color: $color-black;
            text-align: right;
          }
        }

        .time-left-info {
          display: flex;
          gap: 8px;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          &__text {
            color: $color-grey;
            font-size: 14px;
          }

          &__value {
            color: $color-blue;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    .container-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      border-bottom: 1px solid $color-grey-border;
      padding: 0 16px 16px;
      margin-bottom: 16px;

      button {
        padding: 8px 12px;
        font-size: 14px;
        font-weight: 500;
        color: $color-grey;
        border: none;
        cursor: pointer;
        text-transform: capitalize;
        background: none;

        &:hover, &.active {
          border-radius: 7px;
          background-color: $color-grey-hover;
          color: $color-blue;
        }
      }
    }

    .container-info-bottom {
      padding: 0 16px 16px;

      .route-info {
        overflow-y: auto;

        @include media-breakpoint-down(md) {
          max-height: 100%;
          overflow-y: visible;
        }

        .route-dot {
          position: absolute;
          left: -25px;
          top: 9px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          z-index: 2;
        }

        .route-dot-blue {
          background: $color-blue-strong;
        }

        .route-dot-white {
          background: $color-white;
          border: 2px solid $color-grey-connection;
        }

        &__icons {
          position: absolute;
          left: -36px;
          top: 2px;
          z-index: 2;
        }

        .in-transit-btn {
          color: $color-blue;
          background: $color-blue-light;
          padding: 2px 6px;
          border-radius: 6px;
        }
      }

      .details-info {
        &_row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid $color-grey-border;
          margin-bottom: 8px;

          &--label {
            color: $color-grey;
            font-size: 14px;
            font-weight: 500;
          }

          &--value {
            color: $color-black;
            font-size: 14px;
            font-weight: 500;
            text-align: right;
          }
        }
      }
    }
  }

  .mobile-tabs {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0.75rem;
    z-index: 9999;


    button {
      background: $color-grey-bg;
      margin: 0 0.25rem;
      text-transform: uppercase;
      border: 0;
      border-radius: 6px;
      padding: 4px 6px;
      color: $color-black;
    }

    button.active {
      background: $color-blue-strong;
      color: $color-white;
    }
  }

  .solid-connection::after, .dashed-connection::after {
    content: '';
    position: absolute;
    top: 2px;
    left: -26px;
    height: 100%;
    z-index: 0;
    border-left-width: 2px;
    border-left-style: solid;
  }

  .dashed-connection::after {
    border-left-style: dashed;
    border-left-color: $color-grey-connection;
  }

  .solid-connection::after {
    border-left-style: solid;
    border-left-color: $color-blue-strong;
  }
}
