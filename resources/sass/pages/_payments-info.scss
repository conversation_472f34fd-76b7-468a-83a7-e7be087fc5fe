#payments-info {
  .to-pay {
    display: block;
    background: $color-blue;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 20px 30px;
    margin-top: 50px;
    height: auto;
    border-radius: $border-radius;

    &:hover, &:focus, &:active {
      text-decoration: none;
    }
  }

  .client-number {
    display: inline-block;
    padding: 20px 30px;
    border-radius: 6px;
    border: solid 2px #d9d9d9;
    background-color: #ffffff;
    font-weight: bold;
    margin-top: 10px;
  }

  .content {
    p {
      margin-bottom: 20px;

      a {
        text-decoration: underline;
        font-weight: bold;
      }
    }
  }
}
