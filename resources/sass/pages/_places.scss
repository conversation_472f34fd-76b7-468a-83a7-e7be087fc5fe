#page_places {
  .tab-content {
    font-size: 16px;

    p {
      margin-bottom: 5px;
    }
  }

  #place-switch {
    select {
      visibility: hidden;
    }

    .select2-container .select2-selection--single {
      @include blue-gradient();
      border: 0;
      //height: 70px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
      color: #fff;
      text-align: left;
      padding-left: 20px;
      //line-height: 70px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
      b {
        border-color: #fff transparent transparent transparent;
      }

      //height: 70px;
    }

  }
}