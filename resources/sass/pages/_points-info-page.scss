@use "sass:math";

#points-info-page {
  p.lead {
    margin-bottom: 30px;
    margin-top: 30px;
  }

  section.cars {
    .row {
      .col-sm-4 {
        display: flex;
        justify-content: center;
      }

      @media (max-width: $screen-sm-min) {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .exchange {
    font-size: 20px;
    font-weight: bold;

    &__top {
      text-align: center;
      position: relative;

      &::after, &::before {
        display: block;
        content: '';
        position: absolute;
        top: 50px;
        background-size: contain;
        background-position: center center;
        width: 100px;
        height: 100px;
      }

      &::before {
        left: 50%;
        margin-left: -50px;
        background-image: url('../../public/img/points-info/s1.png');
        transform: rotate(-60deg);
        top: 65px;

        @media (min-width: $screen-sm-min) {
          top: 50px;
          margin-left: auto;
          left: 25%;
          transform: none;
        }
      }

      &::after {
        left: 50%;
        margin-left: -50px;
        background-image: url('../../public/img/points-info/s2.png');
        transform: rotate(60deg);
        top: 225px;

        @media (min-width: $screen-sm-min) {
          top: 50px;
          margin-left: auto;
          right: 25%;
          left: auto;
          transform: none;
        }
      }
    }

    &__bottom {
      display: flex;
      flex-direction: column;
      align-items: center;

      @media (min-width: $screen-sm-min) {
        margin-top: 90px;
        align-items: normal;
        flex-direction: row;
        justify-content: space-between;
      }
    }

    &__amount {
      color: #c42422;
      font-size: 45px;
      font-weight: bold;
      line-height: 40px;

      &--small {
        font-size: 40px;
        line-height: 35px;
      }
    }

    &__info {
      display: flex;
      align-items: center;
      flex-direction: row;
      margin-top: 90px;

      @media (min-width: $screen-sm-min) {
        margin-top: auto;
      }

      & > div {
        margin-right: 23px;
      }
    }
  }

  .table-info thead tr th {
    width: math.div(100%, 3);
  }
}
